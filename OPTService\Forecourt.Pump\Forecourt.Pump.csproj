﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net48</TargetFramework>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<LangVersion>9.0</LangVersion>
		<Platforms>AnyCPU;x64;x86</Platforms>
	</PropertyGroup>

	<PropertyGroup>
		<NoWarn>1701;1702;1591</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="CsvHelper" Version="[33.1.0,)" />
		<PackageReference Include="Htec.Foundation" Version="[5.0.0,)" />
		<PackageReference Include="Htec.Foundation.Connections" Version="[6.0.0,)" />
		<PackageReference Include="Htec.Hydra.Core.SecAuth" Version="[1.0.0,)" />
		<PackageReference Include="Htec.Hydra.Opt.Common" Version="[2.4.0,)" />
		<PackageReference Include="Htec.Hydra.Core.Pump" Version="3.5.0-feat-775416-exte0079" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Forecourt.Core\Forecourt.Core.csproj" />
	</ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="Forecourt.Common.Tests" />
		<InternalsVisibleTo Include="Forecourt.Common.Integration.Tests" />
	</ItemGroup>

	<ItemGroup Label="DOMS Components-x64" Condition="'$(Platform)' != 'x86'">
		<None Include="..\DOMS\64bit\PSS_Forecourt_Lib.dll" Link="PSS_Forecourt_Lib.dll">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="..\DOMS\64bit\PSS_TcpIp_Lib.dll" Link="PSS_TcpIp_Lib.dll">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<Reference Include="PSS_Forecourt_Lib">
			<EmbedInteropTypes>False</EmbedInteropTypes>
			<HintPath>..\DOMS\64bit\PSS_Forecourt_Lib.dll</HintPath>
		</Reference>
		<Reference Include="PSS_TcpIp_Lib">
			<EmbedInteropTypes>True</EmbedInteropTypes>
			<HintPath>..\DOMS\64bit\PSS_TcpIp_Lib.dll</HintPath>
		</Reference>
	</ItemGroup>

	<ItemGroup Label="DOMS Components-x86" Condition="'$(Platform)' == 'x86'">
		<None Include="..\DOMS\32bit\PSS_Forecourt_Lib.dll" Link="PSS_Forecourt_Lib.dll">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Include="..\DOMS\32bit\PSS_TcpIp_Lib.dll" Link="PSS_TcpIp_Lib.dll">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<Reference Include="PSS_Forecourt_Lib">
			<EmbedInteropTypes>False</EmbedInteropTypes>
			<HintPath>..\DOMS\32bit\PSS_Forecourt_Lib.dll</HintPath>
		</Reference>
		<Reference Include="PSS_TcpIp_Lib">
			<EmbedInteropTypes>True</EmbedInteropTypes>
			<HintPath>..\DOMS\32bit\PSS_TcpIp_Lib.dll</HintPath>
		</Reference>
	</ItemGroup>


</Project>
