﻿using CSharpFunctionalExtensions;
using Dapper;
using Forecourt.Bos.HydraDb.Models;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Enums;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Pump.Models.Doms;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.DapperWrapper.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Configuration;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Extensions;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Bos.Messages;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using Newtonsoft.Json;
using OPT.Common.Helpers;
using OPT.Common.HydraDb;
using OPT.Common.HydraDbClasses;
using OPT.Common.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Net;
using System.Text;
using ConfigurationConstants = Forecourt.Core.Configuration.Constants;
using connGenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;
using FuelTransaction = Forecourt.Core.HydraDb.Models.FuelTransaction;
using GenericEndPoint = OPT.Common.HydraDbClasses.GenericEndPoint;
using Opt = OPT.Common.Opt;
using PumpDelivered = OPT.Common.HydraDbClasses.PumpDelivered;
using PumpModel = Forecourt.Core.Pump.Models.Pump;
using ReceiptInfo = Forecourt.Core.HydraDb.Models.ReceiptInfo;
using Wash = OPT.Common.HydraDbClasses.Wash;

namespace Forecourt.Common.HydraDbClasses
{
    /// <summary>
    /// All capabilities regarding HydraDb
    /// </summary>
    [HasConfiguration]
    public class HydraDb : CoreHydraDb<ITelemetryWorker, ReceiptInfo, FuelTransaction>, IHydraDb
    {
        /// <inheritdoc cref="ConfigurationConstants.ConfigKeySuffixHydraDb"/>
        private const string ConfigKeySuffixHydraDb = ConfigurationConstants.ConfigKeySuffixHydraDb;

        /// <inheritdoc cref="ConfigurationConstants.ConfigKeySuffixEndPoints"/>
        private const string ConfigKeySuffixEndPoints = ConfigurationConstants.ConfigKeySuffixEndPoints;

        /// <inheritdoc cref="ConfigurationConstants.CachedItemTypeConfiguration"/>
        private const string CachedItemTypeCategoriesConfiguration = ConfigurationConstants.CachedItemTypeConfiguration;

        /// <inheritdoc cref="ConfigurationConstants.CachedItemCategories"/>
        private const string CachedItemCategories = ConfigurationConstants.CachedItemCategories;

        public const string ConfigKeyCachedItemIntervalHydraDbEndPointESocket = CacheHelper.ConfigKeyPrefixCacheInterval + ConfigKeySuffixEndPoints + ConfigurationConstants.CachedItemESocket;
        public const string DefaultValueCachedItemIntervalHydraDbEndPointESocket = CacheHelper.DefaultValueCacheInterval;

        public const string ConfigKeyCachedItemIntervalHydraDbEndPointHydraOpt = CacheHelper.ConfigKeyPrefixCacheInterval + ConfigKeySuffixEndPoints + ConfigurationConstants.CachedItemHydraOpt;
        public const string DefaultValueCachedItemIntervalHydraDbEndPointHydraOpt = CacheHelper.DefaultValueCacheInterval;

        public const string ConfigKeyCachedItemIntervalHydraDbIsGenericLoyaltyAvailable = CacheHelper.ConfigKeyPrefixCacheInterval + ConfigKeySuffixHydraDb + ConfigurationConstants.CachedItemIsGenericLoyaltyAvailable;
        public const string DefaultValueCachedItemIntervalHydraDbIsGenericLoyaltyAvailable = CacheHelper.DefaultValueCacheInterval;

        public const string ConfigKeyCachedItemIntervalHydraDbSiteInfo = CacheHelper.ConfigKeyPrefixCacheInterval + ConfigKeySuffixHydraDb + ConfigurationConstants.CategoryNameSiteInfo;
        public const string DefaultValueCachedItemIntervalHydraDbSiteInfo = CacheHelper.DefaultValueCacheInterval;

        public const string ConfigKeyCachedItemIntervalHydraDbGrades = CacheHelper.ConfigKeyPrefixCacheInterval + ConfigKeySuffixHydraDb + ConfigurationConstants.ConfigKeyGrades;
        public const string DefaultValueCachedItemIntervalHydraDbGrades = CacheHelper.DefaultValueCacheInterval;

        private const int OneDayInSeconds = 86400;
        private const int SixMonthsInDays = 183;
        private const int TwoWeeksInDays = 14;
        private const int DefaultCurrencyCode = 826;
        private const int DefaultTillNumber = 99;
        private const int DefaultFuelCategory = 99;
        public const string DefaultContactlessTtq = "32000000";
        private readonly IConfigurationRepository _configurationRepository;

        private readonly ICacheHelper _cacheHelper;

        public IList<long> ReceiptTrans() =>
            GetReceiptCollection().Where(x => x.TransactionNumber != 0).Select(x => x.TransactionNumber).ToList();

        /// <summary>Constructor for Hydra Database.</summary>
        /// <param name="dbExecutorFactory">Executor factory for Hydra database.</param>
        /// <param name="logger">Htec logger to use.</param>
        /// <param name="telemetryWorker">Telemetry Worker to use.</param>
        /// <param name="configurationManager">Configuration manager to use.</param>
        /// <param name="configurationRepository">Repository for configuration handling.</param>
        /// <param name="cacheHelper">ICacheHelper to use.</param>
        public HydraDb(IDbExecutorFactory dbExecutorFactory, IHtecLogger logger, ITelemetryWorker telemetryWorker, IConfigurationManager configurationManager,
            IConfigurationRepository configurationRepository, ICacheHelper cacheHelper)
            : base(logger, dbExecutorFactory, configurationManager, telemetryWorker)
        {
            _configurationRepository = configurationRepository ?? throw new ArgumentNullException(nameof(configurationRepository));
            _cacheHelper = cacheHelper ?? throw new ArgumentNullException(nameof(cacheHelper));
        }

        #region End Points

        public OptEndPoints FetchEndPoints(string hydraId = null)
        {
            hydraId ??= Id ?? throw new ArgumentNullException(nameof(hydraId));
            return _cacheHelper.GetCachedItem(ConfigKeySuffixEndPoints, $"{ConfigurationConstants.CachedItemHydraOpt}{hydraId}", () => DoFetchEndPoints(hydraId));
        }

        internal OptEndPoints DoFetchEndPoints(string hydraId)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            OptEndPoints optEndPoints = new OptEndPoints();
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    List<OptEndPoints> endPoints =
                        db.Query<OptEndPoints>("GetOPTEndPoints", commandType: CommandType.StoredProcedure).ToList();
                    // ReSharper disable once ConvertIfStatementToSwitchStatement
                    if (hydraId == null && endPoints.Exists(x => !x.HydraId.Equals(string.Empty)))
                    {
                        hydraId = endPoints.Find(x => !x.HydraId.Equals(string.Empty)).HydraId;
                    }
                    else if (hydraId == null)
                    {
                        hydraId = OptEndPoints.DefaultHydraId;
                    }

                    if (endPoints.Exists(x => x.HydraId.Equals(hydraId)))
                    {
                        optEndPoints = endPoints.Find(x => x.HydraId.Equals(hydraId));
                    }
                    else if (endPoints.Exists(x => x.HydraId.Equals(string.Empty)))
                    {
                        optEndPoints = endPoints.Find(x => x.HydraId.Equals(string.Empty));
                        optEndPoints.HydraId = hydraId;
                    }
                    else
                    {
                        optEndPoints = new OptEndPoints(hydraId);
                    }

                    GetLogger().Debug($"Results of query, Hydra Id is {optEndPoints.HydraId}" +
                                      $" From OPT End Point is {optEndPoints.FromOptEndPoint}" +
                                      $" To OPT End Point is {optEndPoints.ToOptEndPoint}" +
                                      $" Heartbeat End Point is {optEndPoints.HeartbeatEndPoint}" +
                                      $" Hydra POS End Point is {optEndPoints.HydraPosEndPoint}" +
                                      $" Retalix POS End Point is {optEndPoints.RetalixPosEndPoint}" +
                                      $" Third Party POS End Point is {optEndPoints.ThirdPartyPosEndPoint}");
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching OPT End Points", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetOPTEndPoints", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return optEndPoints;
        }

        /// <inheritdoc/>
        public connGenericEndPoint FetchAnprEndPoint()
        {
            return FetchEndPoint("GetANPREndPoint", () => new AnprEndPoint());
        }

        /// <inheritdoc/>
        public connGenericEndPoint FetchCarWashEndPoint()
        {
            return FetchEndPoint("GetCarWashEndPoint", () => new CarWashEndPoint());
        }

        /// <inheritdoc/>
        public connGenericEndPoint FetchTankGaugeEndPoint()
        {
            return FetchEndPoint("GetTankGaugeEndPoint", () => new TankGaugeEndPoint());
        }

        /// <inheritdoc/>
        public connGenericEndPoint FetchHydraMobileEndPoint()
        {
            return FetchEndPoint("GetHydraMobileEndPoint", () => new HydraMobileEndPoint());
        }

        public IList<connGenericEndPoint> FetchESocketEndPoints()
        {
            return _cacheHelper.GetCachedItem<IList<connGenericEndPoint>>(ConfigKeySuffixEndPoints, ConfigurationConstants.CachedItemESocket, () => DoFetchESocketEndPoints());
        }

        public IList<connGenericEndPoint> DoFetchESocketEndPoints()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IList<ESocketEndPoint> endPoints =
                        db.Query<ESocketEndPoint>("GetESocketEndPoints", commandType: CommandType.StoredProcedure).ToList();
                    if (GetLogger().IsDebugEnabled && endPoints.Any())
                    {
                        StringBuilder debugString = new StringBuilder();
                        debugString.Append("Results of query, eSocket Endpoints:");

                        foreach (ESocketEndPoint endpoint in endPoints)
                        {
                            debugString.Append($" {endpoint.IpAddress}:{endpoint.Port}");
                        }

                        GetLogger().Debug(debugString.ToString());
                    }

                    TelemetryWorker.QueryReturnedFromHydraDb("GetESocketEndPoints", guid);

                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return endPoints.Select(x => (connGenericEndPoint)x).ToList();
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching eSocket.POS End Points", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetESocketEndPoints", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new List<connGenericEndPoint>();
        }

        public void SetServiceAddress(string hydraId, IPAddress ip)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Setting Service Address = {ip}");
            OptEndPoints endpoints = FetchEndPoints(hydraId);
            string ipAddress = ip.ToString();
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetOPTEndPoints", new
                    {
                        hydraId,
                        ipAddress,
                        fromOPTPort = endpoints.FromOptEndPoint.Port,
                        toOPTPort = endpoints.ToOptEndPoint.Port,
                        heartbeatPort = endpoints.HeartbeatEndPoint.Port,
                        hydraPosPort = endpoints.HydraPosEndPoint.Port,
                        retalixPosPort = endpoints.RetalixPosEndPoint.Port,
                        thirdPartyPosPort = endpoints.ThirdPartyPosEndPoint.Port,
                        mediaChannelPort = endpoints.MediaChannelBindEndPoint.Port
                    }, commandType: CommandType.StoredProcedure);
                }

                _cacheHelper.ForceExpirationOnCachedItem(ConfigKeySuffixEndPoints, $"{ConfigurationConstants.CachedItemHydraOpt}{hydraId}");
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting service address", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetOPTEndPoints", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetServicePorts
        (string hydraId, int fromOptPort, int toOptPort, int heartbeatPort, int hydraPosPort, int retalixPosPort, int thirdPartyPosPort,
            int mediaChannelPort)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Service Ports, from OPT = {fromOptPort}, To OPT = {toOptPort}," +
                             $" Heartbeat = {heartbeatPort}, Hydra POS = {hydraPosPort}, Retalix POS = {retalixPosPort}," +
                             $" Third Party POS = {thirdPartyPosPort}, Media Channel = {mediaChannelPort}");
            OptEndPoints endpoints = FetchEndPoints(hydraId);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetOPTEndPoints", new
                    {
                        hydraId,
                        ipAddress = endpoints.FromOptEndPoint.Address.ToString(),
                        fromOptPort,
                        toOptPort,
                        heartbeatPort,
                        hydraPosPort,
                        retalixPosPort,
                        thirdPartyPosPort,
                        mediaChannelPort
                    }, commandType: CommandType.StoredProcedure);
                }

                _cacheHelper.ForceExpirationOnCachedItem(ConfigKeySuffixEndPoints, $"{ConfigurationConstants.CachedItemHydraOpt}{hydraId}");
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting service ports", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetOPTEndPoints", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        /// <inheritdoc/>
        public void SetPumpEndPoint(IPAddress ip, int port)
        {
            var result = DbActionWithTelemetry((db) =>
            {
                var dbResult = db.Execute("SetPumpEndPoint", new { ipAddress = ip.ToString(), port }, commandType: CommandType.StoredProcedure);
                return dbResult;
            }, "SetPumpEndPoint");
        }

        public void SetDivertOpt(IPAddress ip, int fromOptPort, int toOptPort, int heartbeatPort, int mediaChannelPort)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Divert, IP = {ip}, From OPT Port = {fromOptPort}, To OPT Port = {toOptPort}," +
                             $" Heartbeat Port = {heartbeatPort}i, Media Channel Port = {mediaChannelPort}");
            string ipAddress = ip.ToString();
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetDivertOPT", new { ipAddress, fromOptPort, toOptPort, heartbeatPort, mediaChannelPort },
                        commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting divert OPT", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetDivertOPT", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public DivertOpt FetchDivertOpt()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IEnumerable<DivertOpt> divertOpts =
                        db.Query<DivertOpt>("GetDivertOpt", commandType: CommandType.StoredProcedure).ToList();
                    if (divertOpts.Count() == 1)
                    {
                        DivertOpt divertOpt = divertOpts.Single();
                        GetLogger().Debug($"Results of query, Is Diverted is {divertOpt.IsDiverted}," +
                                          $" IP Address is {divertOpt.IpAddress}," + $" From OPT Port is {divertOpt.FromOptPort}" +
                                          $" To OPT Port is {divertOpt.ToOptPort}" + $" Heartbeat Port is {divertOpt.HeartbeatPort}" +
                                          $" Media Channel Port is {divertOpt.MediaChannelPort}");
                        TelemetryWorker.QueryReturnedFromHydraDb("GetDivertOpt", guid);
                        GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                        return divertOpt;
                    }
                }
            }
            catch (Exception e)

            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetDivertOpt", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new DivertOpt();
        }

        public void SetOptDiverted()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Set OPT Diverted");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetOPTDiverted", new { isDiverted = true }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting OPT diverted", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetOPTDiverted", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetOptNotDiverted()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Set OPT Not Diverted");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetOPTDiverted", new { isDiverted = false }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting OPT diverted", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetOPTDiverted", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }


        public void SetAnprEndPoint(IPAddress ip, int port)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set ANPR EndPoint, IP = {ip}, Port = {port}");
            string ipAddress = ip.ToString();
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetANPREndPoint", new { ipAddress, port }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting ANPR address", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetANPREndPoint", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetCarWashEndPoint(IPAddress ip, int port)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info(HeaderParameters, () => new[] { $"IP = {ip}, Port = {port}" });
            string ipAddress = ip.ToString();
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetCarWashEndPoint", new { ipAddress, port }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Car Wash address", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetCarWashEndPoint", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetTankGaugeEndPoint(IPAddress ip, int port)
        {
            var result = DbActionWithTelemetry((db) =>
            {
                var dbResult = db.Execute("SetTankGaugeEndPoint", new { ipAddress = ip.ToString(), port }, commandType: CommandType.StoredProcedure);
                return dbResult;
            }, "SetTankGaugeEndPoint");           
        }

        public void SetHydraMobileEndPoint(IPAddress ip, int port)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Hydra Mobile EndPoint, IP = {ip}, Port = {port}");
            string ipAddress = ip.ToString();
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetHydraMobileEndPoint", new { ipAddress, port }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Hydra Mobile address", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetHydraMobileEndPoint", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void AddEsocket(IPAddress ip, int port)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Add eSocket.POS, IP = {ip}, Port = {port}");
            string ipAddress = ip.ToString();
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("AddESocketEndPoint", new { ipAddress, port }, commandType: CommandType.StoredProcedure);
                }

                _cacheHelper.ForceExpirationOnCachedItem(ConfigKeySuffixEndPoints, ConfigurationConstants.CachedItemESocket);
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception adding eSocket.POS", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("AddESocketEndPoint", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void RemoveEsocket(IPAddress ip, int port)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Remove eSocket.POS, IP = {ip}, Port = {port}");
            string ipAddress = ip.ToString();
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("RemoveESocketEndPoint", new { ipAddress, port }, commandType: CommandType.StoredProcedure);
                }

                _cacheHelper.ForceExpirationOnCachedItem(ConfigKeySuffixEndPoints, ConfigurationConstants.CachedItemESocket);
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception removing eSocket.POS", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("RemoveESocketEndPoint", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetRetalixPosPrimaryIpAddress(IPAddress address)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            if (address == null)
            {
                GetLogger().Info("Clear Retalix POS Primary IP Address");
                Guid guid = Guid.NewGuid();
                TelemetryWorker.QuerySentToHydraDb(guid);
                try
                {
                    using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                    {
                        db.Execute("ClearRetalixPrimary", commandType: CommandType.StoredProcedure);
                    }

                    _cacheHelper.ForceExpirationOnCachedItem(ConfigKeySuffixHydraDb, ConfigurationConstants.CachedItemRetalixPosPrimaryIpAddress);
                }
                catch (Exception e)
                {
                    GetLogger().Error("Exception clearing Retalix Primary", e);
                }

                TelemetryWorker.QueryReturnedFromHydraDb("ClearRetalixPrimary", guid);
            }
            else
            {
                GetLogger().Info($"Set Retalix POS Primary IP Address {address}");
                string ipAddress = address.ToString();
                Guid guid = Guid.NewGuid();
                TelemetryWorker.QuerySentToHydraDb(guid);
                try
                {
                    using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                    {
                        db.Execute("SetRetalixPrimary", new { ipAddress }, commandType: CommandType.StoredProcedure);
                    }
                    _cacheHelper.ForceExpirationOnCachedItem(ConfigKeySuffixHydraDb, ConfigurationConstants.CachedItemRetalixPosPrimaryIpAddress);
                }
                catch (Exception e)
                {
                    GetLogger().Error($"Exception setting Retalix Primary {address}", e);
                }

                TelemetryWorker.QueryReturnedFromHydraDb("SetRetalixPrimary", guid);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        /// <inheritdoc />
        public IPAddress GetRetalixPosPrimaryIpAddress()
        {
            return _cacheHelper.GetCachedItem<IPAddress>(ConfigKeySuffixHydraDb, ConfigurationConstants.CachedItemRetalixPosPrimaryIpAddress, () => DoGetRetalixPosPrimaryIpAddress());
        }

        internal IPAddress DoGetRetalixPosPrimaryIpAddress()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IEnumerable<string> addresses =
                        db.Query<string>("GetRetalixPrimary", commandType: CommandType.StoredProcedure).ToList();
                    if (addresses.Count() == 1)
                    {
                        if (IPAddress.TryParse(addresses.Single(), out IPAddress address))
                        {
                            GetLogger().Debug($"Results of query, Address is {address}");
                        }
                        else
                        {
                            GetLogger().Error($"Unable to parse {addresses.Single()}");
                            address = null;
                        }

                        TelemetryWorker.QueryReturnedFromHydraDb("GetRetalixPrimary", guid);
                        GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                        return address;
                    }
                }
            }
            catch (Exception e)

            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetCarWashEndPoint", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        #endregion

        public OptMode FetchOptMode(string optIdString)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Fetching OPT Mode for OPT {optIdString}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IEnumerable<OptMode> modes = db.Query<OptMode>("GetOPTMode", commandType: CommandType.StoredProcedure).ToList();
                    if (GetLogger().IsDebugEnabled && modes.Any())
                    {
                        StringBuilder debugString = new StringBuilder();
                        debugString.Append("Results of query, OPT Modes:");

                        foreach (OptMode entry in modes)
                        {
                            debugString.Append($" {entry.OptId} / {entry.Contactless}");
                        }

                        GetLogger().Debug(debugString.ToString());
                    }

                    TelemetryWorker.QueryReturnedFromHydraDb("GetOPTMode", guid);

                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return modes.FirstOrDefault(x => optIdString.Equals(x.OptId)) ?? new OptMode(optIdString);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching OPT Mode", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetOPTMode", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new OptMode(optIdString);
        }

        #region Pumps and OPTs

        public IList<PumpTid> FetchAllPumps(IEnumerable<string> allTids)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IList<PumpTid> tids = db.Query<PumpTid>("GetPumpTids", commandType: CommandType.StoredProcedure).ToList();
                    if (GetLogger().IsDebugEnabled && tids.Any())
                    {
                        StringBuilder debugString = new StringBuilder();
                        debugString.Append("Results of query, Pump TIDs:");

                        foreach (PumpTid entry in tids)
                        {
                            debugString.Append($" {entry.Number} / {entry.Tid} / {entry.OptId}");
                        }

                        GetLogger().Debug(debugString.ToString());
                    }

                    List<PumpTid> results = new List<PumpTid>();
                    foreach (PumpTid tid in tids)
                    {
                        if (tid.Tid != null && allTids.Any(x => tid.Tid.Equals(x)) && tid.Number >= byte.MinValue &&
                            tid.Number <= byte.MaxValue)
                        {
                            results.Add(tid);
                        }
                        else if (tid.OptId != null && tid.Number >= byte.MinValue && tid.Number <= byte.MaxValue)
                        {
                            results.Add(new PumpTid(tid.Number, null, tid.OptId, tid.DefaultKioskOnly, tid.DefaultMixed,
                                tid.CurrentKioskOnly, tid.CurrentMixed, tid.Closed, tid.MaxFillOverrideForFuelCards,
                                tid.MaxFillOverrideForPaymentCards));
                        }
                    }

                    TelemetryWorker.QueryReturnedFromHydraDb("GetPumpTids", guid);

                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return results;
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Pump TIDs", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetPumpTids", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new List<PumpTid>();
        }

        public void MapOpt(byte number, string opt)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Map OPT, Pump = {number}, OPT = {opt}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    if (string.IsNullOrWhiteSpace(opt))
                    {
                        db.Execute("AddPumpOPT", new { number }, commandType: CommandType.StoredProcedure);
                    }
                    else
                    {
                        db.Execute("AddPumpOPT", new { number, opt }, commandType: CommandType.StoredProcedure);
                    }
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception mapping OPT", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("AddPumpOPT", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void MapTid(byte number, string tid)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Map TID, Pump = {number}, TID = {tid}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    if (string.IsNullOrWhiteSpace(tid))
                    {
                        db.Execute("AddPumpTID", new { number }, commandType: CommandType.StoredProcedure);
                    }
                    else
                    {
                        db.Execute("AddPumpTID", new { number, tid }, commandType: CommandType.StoredProcedure);
                    }
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception mapping TID", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("AddPumpTID", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public IEnumerable<PaymentTimeout> FetchPaymentTimeouts()
        {
            return _cacheHelper.GetCachedItem(ConfigKeySuffixHydraDb, $"{nameof(PaymentTimeout)}List", () => DoFetchPaymentTimeout());
        }

        public int FetchPaymentTimeout(PaymentTimeoutType mode)
        {
            DoDeferredLogging(LogLevel.Debug, $"{HeaderParameters}.Mode", () => new[] { $"{mode}" });

            var timeouts = _cacheHelper.GetCachedItem(ConfigKeySuffixHydraDb, $"{nameof(PaymentTimeout)}List", () => DoFetchPaymentTimeout());

            return timeouts?.FirstOrDefault(x => x.Mode.Equals(mode))?.Timeout ?? 
                (int.TryParse(mode.ToDescriptionValue(), out var timeout) ? timeout : Opt.DefaultPaymentTimeoutInSeconds);
        }

        private IEnumerable<PaymentTimeout> DoFetchPaymentTimeout()
        {
            var result = DbActionWithTelemetry((db) => db.Query<PaymentTimeout>("GetPaymentTimeout", commandType: CommandType.StoredProcedure), "GetPaymentTimeout");
            if (!result.IsSuccess)
            {
                return Enumerable.Empty<PaymentTimeout>();
            }

            DoDeferredLogging(LogLevel.Debug, "Result", () => new[] { string.Join(";", result.Value.Select(x => $"{x.Mode} / {x.Timeout}")) });
            return result.Value;
        }

        public Result SetPaymentTimeout(PaymentTimeoutType mode, int timeout)
        {
            if ((mode == PaymentTimeoutType.SecAuth && (timeout < 5 || timeout > 30)) || (timeout < 0))
            {
                return Result.Failure("Invalid timeout value");
            }

            var result = DbActionWithTelemetry((db) => db.Execute("SetPaymentTimeout", new { mode, timeout }, commandType: CommandType.StoredProcedure), "SetPaymentTimeout");
            if (result.IsSuccess)
            {
                _cacheHelper.ForceExpirationOnCachedItem(ConfigKeySuffixHydraDb, $"{nameof(PaymentTimeout)}List");
            }

            return result;
        }

        #endregion

        #region Receipts

        public ReceiptInfo GetReceipt(string cardNumber, long transactionNumber = 0, bool includeExpired = false)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Getting receipt for card number {cardNumber}, transaction number {transactionNumber}");
            DateTime now = DateTime.Now;
            ReceiptInfo receipt = null;

            if (transactionNumber == 0 && !string.IsNullOrWhiteSpace(cardNumber))
            {
                receipt = GetReceiptCollection().LastOrDefault(x => x.CardNumber.Equals(cardNumber) && (includeExpired || x.Expiry > now));
            }
            else if (string.IsNullOrWhiteSpace(cardNumber))
            {
                receipt = GetReceiptCollection().LastOrDefault(x => x.TransactionNumber == transactionNumber && (includeExpired || x.Expiry > now));
            }
            else
            {
                receipt = GetReceiptCollection().LastOrDefault(x =>
                    x.CardNumber.Equals(cardNumber) && x.TransactionNumber == transactionNumber && (includeExpired || x.Expiry > now));
            }

            if (receipt != null)
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return receipt;
            }
            else
            {
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return null;
            }
        }

        public IList<ReceiptInfo> GetReceipts(string cardNumber, bool includeExpired = false)
        {
            var receipt = new List<ReceiptInfo>();

            DoAction(() =>
            {
                GetLogger().Debug($"Getting receipt for card number {cardNumber}");
                var now = DateTime.Now;

                if (!string.IsNullOrWhiteSpace(cardNumber))
                {
                    receipt = GetReceiptCollection().Where(x => x.CardNumber.Equals(cardNumber) && (includeExpired || x.Expiry > now)).ToList();
                }
            }, string.Empty);

            return receipt;
        }

        public IList<ReceiptInfo> GetReceiptsForOpt(string opt)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Getting receipts for OPT {opt}");
            var receipts = GetReceiptCollection().Where(x => x.Opt.Equals(opt)).OrderByDescending(x => x.TransactionTime).Take(ReceiptMaxCount).ToList();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return receipts;
        }

        public void SetReceiptTimeout(int timeout)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Receipt Timeout, Timeout = {timeout}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetReceiptTimeout", new { timeout }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Receipt Timeout", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetReceiptTimeout", guid);

            ReceiptTimeout = timeout;
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetReceiptMaxCount(int maxCount)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set receipt Max Count, Count = {maxCount}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetReceiptMaxCount", new { maxCount }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Receipt Max Count", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetReceiptMaxCount", guid);

            ReceiptMaxCount = maxCount;
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        /// <inheritdoc/>
        public void FetchReceipts()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            UpdateReceiptMaxCount();
            UpdateReceiptTimeout();
            GetReceipts();
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        /// <inheritdoc/>
        public SiteInfo GetSiteInfo()
        {
            return _cacheHelper.GetCachedItem(ConfigKeySuffixHydraDb, ConfigurationConstants.CategoryNameSiteInfo, DoGetSiteInfo);
        }

        internal SiteInfo DoGetSiteInfo()
        {
            var doGetSiteInfo = _configurationRepository.GetSiteInfo();
            return doGetSiteInfo.IsSuccess
                ? doGetSiteInfo.Value
                : new SiteInfo(1, null, null, true, true, DefaultCurrencyCode, false, DefaultTillNumber, DefaultFuelCategory, 0); // Morrisons;
        }

        /// <inheritdoc cref="IHydraDbConfig.GetCategoriesConfiguration"/>
        public IList<CategoryConfiguration> GetCategoriesConfiguration()
        {
            return _cacheHelper.GetCachedItem(CachedItemTypeCategoriesConfiguration, CachedItemCategories, () => _configurationRepository.GetCategoriesConfiguration());
        }

        /// <inheritdoc/>
        public PosClaim GetPosClaim()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IEnumerable<PosClaim> modes = db.Query<PosClaim>("GetPosClaim", commandType: CommandType.StoredProcedure).ToList();
                    if (modes.Count() == 1)
                    {
                        TelemetryWorker.QueryReturnedFromHydraDb("GetPosClaim", guid);
                        GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                        return modes.Single();
                    }
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching POS Claim", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetPosClaim", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new PosClaim(99);
        }

        public PruneDays GetPruneDays()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IEnumerable<PruneDays> modes = db.Query<PruneDays>("GetPruneDays", commandType: CommandType.StoredProcedure).ToList();
                    if (modes.Count() == 1)
                    {
                        TelemetryWorker.QueryReturnedFromHydraDb("GetPruneDays", guid);
                        GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                        return modes.Single();
                    }
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Prune Days", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetPruneDays", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new PruneDays(SixMonthsInDays, SixMonthsInDays, TwoWeeksInDays);
        }

        public void SetReceiptLayoutMode(int mode)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Receipt Layout Mode, Mode = {mode}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetReceiptLayoutMode", new { mode }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Receipt Layout Mode", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetReceiptLayoutMode", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetSiteName(string siteName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Site Name, Name = {siteName}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetSiteName", new { siteName }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Site Name", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetSiteName", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetVatNumber(string vatNumber)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set VAT Number, Number = {vatNumber}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetVATNumber", new { vatNumber }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting VAT Number", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetVATNumber", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetCurrencyCode(int currencyCode)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Currency Code, Code = {currencyCode}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetCurrencyCode", new { currencyCode }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Currency Code", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetCurrencyCode", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetNozzleUpForKioskUse(bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Nozzle Up For Kiosk Use, Flag = {flag}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetNozzleUpForKioskUse", new { flag }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Nozzle Up For Kiosk Use", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetNozzleUpForKioskUse", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetUseReplaceNozzleScreen(bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Use Replace Nozzle Screen, Flag = {flag}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetUseReplaceNozzleScreen", new { flag }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Use Replace Nozzle Screen", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetUseReplaceNozzleScreen", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetForwardFuelPriceUpdate(bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Forward Fuel Price Update, Flag = {flag}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetForwardFuelPriceUpdate", new { flag }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Forward Fuel Price Update", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetForwardFuelPriceUpdate", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetReceiptHeader(string opt, string receiptHeader)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set OPT Receipt Header, OPT = {opt}, Header = {receiptHeader}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetReceiptHeader", new { opt, receiptHeader }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting OPT Receipt Header", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetOPTReceiptHeader", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetReceiptFooter(string opt, string receiptFooter)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set OPT Receipt Footer, OPT = {opt}, Footer = {receiptFooter}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetReceiptFooter", new { opt, receiptFooter }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting OPT Receipt Footer", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetOPTReceiptFooter", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetPlaylistFileName(string opt, string playlistFileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set OPT Playlist File Name, OPT = {opt}, Header = {playlistFileName}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetPlaylistFileName", new { opt, playlistFileName }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting OPT Playlist File Name", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetOPTPlaylistFileName", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetLastLogTime(string opt, DateTime? logTime)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set OPT Last Log Time, OPT = {opt}, Log Time = {logTime?.ToString() ?? "No Time"}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetLastLogTime", new { opt, logTime }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting OPT Last Log Time", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetLastLogTime", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        ///// <inheritdoc/>
        //public Result<string> GetIntegrationType(IntegrationType integrationType, string loggingReference = null)
        //{
        //    return DoAction(() =>
        //    {
        //        var key = integrationType.ToDescriptionValue();
        //        var category = GetCategoriesConfiguration().FirstOrDefault(x => x.Category.Equals(ConfigurationConstants.CategoryNameSiteInfo, StringComparison.InvariantCultureIgnoreCase));
        //        var setting = category?.Settings.FirstOrDefault(x => x.Key.Equals(key, StringComparison.InvariantCultureIgnoreCase));

        //        return Result.SuccessIf(setting != null, setting.Value.Value.Value, "Not found");
        //    }, loggingReference);
        //}

        /// <inheritdoc/>
        public Result SetIntegrationType(IntegrationType integrationType, string value, string loggingReference = null)
        {
            return DoAction(() =>
            {
                var result = _configurationRepository.SetIntegrationType(integrationType, value, loggingReference);
                if (result.IsSuccess)
                {
                    _cacheHelper.ForceExpirationOnCachedItem(ConfigurationConstants.CachedItemTypeConfiguration, ConfigurationConstants.CachedItemCategories);
                }

                return result;
            }, loggingReference);
        }

        public void SetLocalAccountsEnabled(bool enabled)
        {
            _configurationRepository.SetLocalAccountsEnabled(enabled);

            _cacheHelper.ForceExpirationOnCachedItem($"{ConfigKeySuffixHydraDb}General", ConfigurationConstants.CategoryNameGeneral);
            _cacheHelper.ForceExpirationOnCachedItem(ConfigKeySuffixHydraDb, ConfigurationConstants.CategoryNameSiteInfo);
        }

        /// <inheritdoc/>
        public Result<StatusCodeResult> UpdateLocalAccountCustomer(LocalAccountCustomerFlags model, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            var storedProcName = "UpdateLocalAccountCustomerFlags";
            
            var result = DbActionWithTelemetry((db) =>
            {
                var p = new
                {
                    model.CustomerReference,
                    model.IsPinRequired,
                    model.ShouldPrintValue,
                    model.IsLoyaltyAllowed
                };
                
                return db.Execute(storedProcName, p, commandType: CommandType.StoredProcedure);
            }, storedProcName, message);

            return MapDbExecuteResult(result);
        }

        private void UpdateReceiptTimeout()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug("Updating Receipt Timeout");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IEnumerable<int> timeouts = db.Query<int>("GetReceiptTimeout", commandType: CommandType.StoredProcedure).ToList();
                    if (timeouts.Count() == 1)
                    {
                        ReceiptTimeout = timeouts.Single();
                    }
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Receipt Timeout", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetReceiptTimeout", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private void UpdateReceiptMaxCount()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug("Updating Receipt Max Count");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IEnumerable<int> counts = db.Query<int>("GetReceiptMaxCount", commandType: CommandType.StoredProcedure).ToList();
                    if (counts.Count() == 1)
                    {
                        ReceiptMaxCount = counts.Single();
                    }
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Receipt Max Count", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetReceiptMaxCount", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        #endregion

        #region Pump Modes

        public void SetKioskOnly(byte number, bool setDefault, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            DoDeferredLogging(LogLevel.Info, Pump.Pump.HeaderPump, () => new[] { $"{number}; SetDefault: {setDefault}" }, reference: message.FullId);
            DbActionWithTelemetry((db) => db.Execute("SetPumpKioskOnly", new { number, setDefault }, commandType: CommandType.StoredProcedure), nameof(SetKioskOnly), message);
        }

        public void SetMixed(byte number, bool setDefault, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            DoDeferredLogging(LogLevel.Info, Pump.Pump.HeaderPump, () => new[] { $"{number}; SetDefault: {setDefault}" }, reference: message.FullId);
            DbActionWithTelemetry((db) => db.Execute("SetPumpMixed", new { number, setDefault }, commandType: CommandType.StoredProcedure), nameof(SetMixed), message);
        }

        public void SetOutsideOnly(byte number, bool setDefault, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            DoDeferredLogging(LogLevel.Info, Pump.Pump.HeaderPump, () => new[] { $"{number}; SetDefault: {setDefault}" }, reference: message.FullId);
            DbActionWithTelemetry((db) => db.Execute("SetPumpOutsideOnly", new { number, setDefault }, commandType: CommandType.StoredProcedure), nameof(SetOutsideOnly), message);
        }

        public void SetPumpMaxFillOverrideForFuelCards(byte number, bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Pump Max Fill Override For Fuel Cards {OnOff(flag)}, Pump = {number}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetPumpMaxFillOverrideForFuelCards", new { number, flag }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Pump Max Fill Override For Fuel Cards", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetPumpMaxFillOverrideForFuelCards", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetPumpMaxFillOverrideForPaymentCards(byte number, bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Pump Max Fill Override For Payment Cards {OnOff(flag)}, Pump = {number}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetPumpMaxFillOverrideForPaymentCards", new { number, flag }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Pump Max Fill Override For Payment Cards", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetPumpMaxFillOverrideForPaymentCards", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        private static string OnOff(bool flag)
        {
            return flag ? "On" : "Off";
        }

        public void SetPumpClosed(byte number, bool closed)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Pump {(closed ? "Closed" : "Open")}, Pump = {number}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetPumpClosed", new { number, closed }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Pump Closed", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetPumpClosed", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetContactless(bool isEnabled)
        {
            _configurationRepository.SetContactless(isEnabled);
        }

        public void SetContactlessCardPreAuth(int limit)
        {
            _configurationRepository.SetContactlessCardPreAuth(limit);
        }

        public void SetContactlessDevicePreAuth(int limit)
        {
            _configurationRepository.SetContactlessDevicePreAuth(limit);
        }

        public void SetContactlessTtq(string ttq)
        {
            _configurationRepository.SetContactlessTtq(ttq);
        }

        public void SetContactlessSingleButton(bool isEnabled)
        {
            _configurationRepository.SetContactlessSingleButton(isEnabled);
        }

        public void SetAutoAuth(string hydraId, bool autoAuth)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Auto Auth = {autoAuth}, Hydra Id = {hydraId}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetAutoAuth", new { hydraId, autoAuth }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Auto Auth", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetAutoAuth", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetMediaChannel(string hydraId, bool mediaChannel)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Media Channel = {mediaChannel}, Hydra Id = {hydraId}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetMediaChannel", new { hydraId, mediaChannel }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Media Channel", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetMediaChannel", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetUnmannedPseudoPos(string hydraId, bool unmanned)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Unmanned Pseudo POS = {unmanned}, Hydra Id = {hydraId}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetUnmannedPseudoPOS", new { hydraId, unmanned }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Unmanned Pseudo POS", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetUnmannedPseudoPOS", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetAsdaDayEndReport(string hydraId, bool isAsda)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Asda Day End Report = {isAsda}, Hydra Id = {hydraId}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetAsdaDayEndReport", new { hydraId, isAsda }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Asda Day End Report", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetAsdaDayEndReport", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        #endregion

        #region Transactions and Shifts

        public IList<GradeName> FetchGradeNames()
        {
            return _cacheHelper.GetCachedItem(ConfigKeySuffixHydraDb, ConfigurationConstants.ConfigKeyGrades, () => DoFetchGradeNames());
        }

        private IList<GradeName> DoFetchGradeNames()
        {
            var result = DbActionWithTelemetry((db) => db.Query<GradeName>("GetGradeNames", commandType: CommandType.StoredProcedure), "GetGradeNames", LoggingReference.ToMessageTracking());
            if (result.IsSuccess)
            {
                DoDeferredLogging(LogLevel.Debug, "GradeNames", () => new[] { string.Join(";", result.Value.Select(x => $"{x.Grade}/{x.Name}/{x.VatRate}")) });
            }

            return result.IsSuccess ? result.Value.ToList() : new List<GradeName>();
        }

        /// <inheritdoc/>
        public Result<int> SetGradeName(byte grade, string name, float vatRate)
        {
            DoDeferredLogging(LogLevel.Info, "Grade", () => new[] { $"{grade}; Name: {name}; VAT Rate: {vatRate}" });
            var result = DbActionWithTelemetry((db) => db.Execute("SetGradeName", new { grade, gradeName = name, vatRate }, commandType: CommandType.StoredProcedure), "SetGradeName");
            if (result.IsSuccess)
            {
                InvalidateCacheForBosConfigMapping();
                _cacheHelper.ForceExpirationOnCachedItem(ConfigurationConstants.ConfigKeySuffixHydraDb, ConfigurationConstants.ConfigKeyGrades);
            }

            return result;
        }
        public void AddEvent(DateTime transactionTime, out int transactionNumber)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug("Add Event");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                DynamicParameters parameters = new DynamicParameters();
                parameters.Add("@transactionTime", transactionTime);
                parameters.Add("@transactionNumber", dbType: DbType.Int32, direction: ParameterDirection.Output);
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("AddEvent", parameters, commandType: CommandType.StoredProcedure);
                }

                transactionNumber = parameters.Get<int>("@transactionNumber");
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception Adding Event", e);
                transactionNumber = 0;
            }

            TelemetryWorker.QueryReturnedFromHydraDb("AddEvent", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void AddDayEnd
        (uint fuelAmount, uint dryAmount, uint quantity, int transactionNumber, short category, short subcategory, string gradeCode,
            string gradeName, string cardProductName, uint discount, IMessageTracking message = null)
        {
            message = message ?? new MessageTracking();
            DoAction(() =>
            {
                GetLogger().Debug("Add Day End");
                var guid = TelemetryWorker.QuerySentToHydraDb(message);
                try
                {
                    using (var db = DbExecutorFactory.CreateExecutor())
                    {
                        db.Execute("AddDayEnd", new
                        {
                            fuelCash = (long)fuelAmount,
                            dryCash = (long)dryAmount,
                            quantity = (long)quantity,
                            transaction = transactionNumber,
                            category = (int)category,
                            subcategory = (int)subcategory,
                            gradeCode,
                            gradeName,
                            cardProductName,
                            discount = (long)discount
                        }, commandType: CommandType.StoredProcedure);
                    }
                }
                catch (Exception ex)
                {
                    GetLogger().Error(HeaderException, () => new[] { ex.Message }, ex);
                }

                TelemetryWorker.QueryReturnedFromHydraDb("AddDayEnd", guid, message);
            }, message.FullId);
        }

        public void TakeDayEnd
        (bool dayEnd, out uint fuelAmount, out uint dryAmount, out uint discount, out DateTime startTime, out DateTime endTime,
            out int firstTransaction, out int lastTransaction, out int shiftNumber)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Take {(dayEnd ? "Day End" : "Shift Summary")}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                DynamicParameters parameters = new DynamicParameters();
                parameters.Add("@fuelCash", dbType: DbType.Int64, direction: ParameterDirection.Output);
                parameters.Add("@dryCash", dbType: DbType.Int64, direction: ParameterDirection.Output);
                parameters.Add("@discount", dbType: DbType.Int64, direction: ParameterDirection.Output);
                parameters.Add("@startTime", dbType: DbType.DateTime, direction: ParameterDirection.Output);
                parameters.Add("@endTime", dbType: DbType.DateTime, direction: ParameterDirection.Output);
                parameters.Add("@firstTransaction", dbType: DbType.Int32, direction: ParameterDirection.Output);
                parameters.Add("@lastTransaction", dbType: DbType.Int32, direction: ParameterDirection.Output);
                parameters.Add("@shiftNumber", dbType: DbType.Int32, direction: ParameterDirection.Output);
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute(dayEnd ? "TakeDayEnd" : "TakeShiftSummary", parameters, commandType: CommandType.StoredProcedure);
                }

                fuelAmount = (uint)parameters.Get<long>("@fuelCash");
                dryAmount = (uint)parameters.Get<long>("@dryCash");
                discount = (uint)parameters.Get<long>("@discount");
                startTime = parameters.Get<DateTime>("@startTime");
                endTime = parameters.Get<DateTime>("@endTime");
                firstTransaction = parameters.Get<int>("@firstTransaction");
                lastTransaction = parameters.Get<int>("@lastTransaction");
                shiftNumber = parameters.Get<int>("@shiftNumber");
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception Adding Transaction", e);
                fuelAmount = 0;
                dryAmount = 0;
                discount = 0;
                startTime = DateTime.MinValue;
                endTime = DateTime.MinValue;
                firstTransaction = 0;
                lastTransaction = 0;
                shiftNumber = 0;
            }

            TelemetryWorker.QueryReturnedFromHydraDb("TakeDayEnd", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public IList<ItemSales> TakeItemSales(bool dayEnd)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Take Item Sales");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IList<ItemSales> itemSales = db.Query<ItemSales>(dayEnd ? "TakeDayItemSales" : "TakeItemSales",
                        commandType: CommandType.StoredProcedure).ToList();
                    if (GetLogger().IsDebugEnabled && itemSales.Any())
                    {
                        StringBuilder debugString = new StringBuilder();
                        debugString.Append("Results of query, Take Item Sales:");

                        foreach (ItemSales item in itemSales)
                        {
                            debugString.Append($" {item.Category}:{item.Subcategory}:{item.GradeCode}:{item.GradeName}:{item.Amount}");
                        }

                        GetLogger().Debug(debugString.ToString());
                    }

                    TelemetryWorker.QueryReturnedFromHydraDb("TakeItemSales", guid);

                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return itemSales;
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Take Item Sales", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("TakeItemSales", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new List<ItemSales>();
        }

        public IList<CardSales> TakeCardSales(bool dayEnd)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Take Card Sales");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IList<CardSales> cardSales = db.Query<CardSales>(dayEnd ? "TakeDayCardSales" : "TakeCardSales",
                        commandType: CommandType.StoredProcedure).ToList();
                    if (GetLogger().IsDebugEnabled && cardSales.Any())
                    {
                        StringBuilder debugString = new StringBuilder();
                        debugString.Append("Results of query, Take Card Sales:");

                        foreach (CardSales item in cardSales)
                        {
                            debugString.Append($" {item.CardRef}:{item.Amount}");
                        }

                        GetLogger().Debug(debugString.ToString());
                    }

                    TelemetryWorker.QueryReturnedFromHydraDb("TakeCardSales", guid);

                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return cardSales;
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Take Card Sales", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("TakeCardSales", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new List<CardSales>();
        }

        public IList<CardVolumeSales> TakeCardVolumeSales()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Take Card Volume Sales");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IList<CardVolumeSales> cardSales =
                        db.Query<CardVolumeSales>("TakeCardVolumeSales", commandType: CommandType.StoredProcedure).ToList();
                    if (GetLogger().IsDebugEnabled && cardSales.Any())
                    {
                        StringBuilder debugString = new StringBuilder();
                        debugString.Append("Results of query, Take Card Volume Sales:");

                        foreach (CardVolumeSales item in cardSales)
                        {
                            debugString.Append($" {item.CardRef}/{item.Grade}:{item.Volume}");
                        }

                        GetLogger().Debug(debugString.ToString());
                    }

                    TelemetryWorker.QueryReturnedFromHydraDb("TakeCardVolumeSales", guid);

                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return cardSales;
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Take Card Volume Sales", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("TakeCardVolumeSales", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new List<CardVolumeSales>();
        }

        public IList<CardReference> FetchCardReferences(IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            var result = new List<CardReference>();
            DoAction(() =>
            {
                var guid = TelemetryWorker.QuerySentToHydraDb(message);
                try
                {
                    using (var db = DbExecutorFactory.CreateExecutor())
                    {
                        var refs = db.Query<CardReference>("FetchCardReferences", commandType: CommandType.StoredProcedure).ToList();
                        if (Logger.IsDebugEnabled && refs.Any())
                        {
                            var debugString = new StringBuilder();

                            foreach (var item in refs)
                            {
                                debugString.Append(
                                    $" {item.CardRef}/{item.CardProductName} => {item.AcquirerName ?? "No Acquirer"} {(item.InUse ? "In Use" : "Not In Use")}");
                            }

                            DoDeferredLogging(LogLevel.Debug, "Results of query, Take Card Volume Sales:", () => new[] { debugString.ToString() }, reference: message.FullId);
                        }

                        result = refs;
                    }
                }
                catch (Exception ex)
                {
                    DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { ex.Message }, ex);
                }

                TelemetryWorker.QueryReturnedFromHydraDb("FetchCardReferences", guid, message);
            }, message.FullId);

            return result;
        }

        /// <inheritdoc/>
        public Result<int> SetCardReference(string name, int reference)
        {
            DoDeferredLogging(LogLevel.Info, "CardReference", () => new[] { $"Name: {name}; Ref: {reference}" });
            var result = DbActionWithTelemetry((db) => db.Execute("SetCardReference", new { name, reference }, commandType: CommandType.StoredProcedure), "SetCardReference");
            if (result.IsSuccess)
            {
                InvalidateCacheForBosConfigMapping();
            }

            return result;
        }

        /// <inheritdoc/>
        public Result<int> ClearCardReference(string name)
        {
            DoDeferredLogging(LogLevel.Info, "CardReference", () => new[] { $"Name: {name}" });
            var result = DbActionWithTelemetry((db) => db.Execute("ClearCardReference", new { name }, commandType: CommandType.StoredProcedure), "ClearCardReference");
            if (result.IsSuccess)
            {
                InvalidateCacheForBosConfigMapping();
            }

            return result;
        }

        /// <inheritdoc/>
        public Result<int> SetAcquirerReference(string cardName, string acquirerName)
        {
            DoDeferredLogging(LogLevel.Info, "CardReference", () => new[] { $"Name: {cardName}; Acquirer: {acquirerName}" });
            var result = DbActionWithTelemetry((db) => db.Execute("SetAcquirerReference", new { cardName, acquirerName }, commandType: CommandType.StoredProcedure), "SetAcquirerReference");
            if (result.IsSuccess)
            {
                InvalidateCacheForBosConfigMapping();
            }

            return result;
        }

        /// <inheritdoc/>
        public Result<int> ClearAcquirerReference(string cardName)
        {
            DoDeferredLogging(LogLevel.Info, "AcquirerReference", () => new[] { $"Name: {cardName}" });
            var result = DbActionWithTelemetry((db) => db.Execute("ClearAcquirerReference", new { cardName }, commandType: CommandType.StoredProcedure), "ClearAcquirerReference");
            if (result.IsSuccess)
            {
                InvalidateCacheForBosConfigMapping();
            }

            return result;
        }

        /// <inheritdoc/>
        public Result<int> SetFuelCard(string cardName, bool isFuelCard)
        {
            DoDeferredLogging(LogLevel.Info, "FuelCard", () => new[] { $"Name: {cardName}; IsFuelCard: {isFuelCard}" });
            var result = DbActionWithTelemetry((db) => db.Execute("SetCardReferenceFuelCard", new { cardName, isFuelCard }, commandType: CommandType.StoredProcedure), "SetCardReferenceFuelCard");
            if (result.IsSuccess)
            {
                InvalidateCacheForBosConfigMapping();
            }

            return result;
        }

        public Result<int> SetExternalName(string cardName, string externalCardName)
        {
            DoDeferredLogging(LogLevel.Info, "ExternalName", () => new[] { $"Name: {cardName}; ExternalName: {externalCardName}" });
            var result = DbActionWithTelemetry((db) => db.Execute("SetExternalName", new { cardName, externalCardName }, commandType: CommandType.StoredProcedure), "SetExternalName");
            if (result.IsSuccess)
            {
                InvalidateCacheForBosConfigMapping();
            }

            return result;
        }

        public Result<int> ClearExternalName(string cardName)
        {
            DoDeferredLogging(LogLevel.Info, "ExternalName", () => new[] { $"Name: {cardName}" });
            var result = DbActionWithTelemetry((db) => db.Execute("ClearExternalName", new { cardName }, commandType: CommandType.StoredProcedure),"ClearExternalName");
            if (result.IsSuccess)
            {
                InvalidateCacheForBosConfigMapping();
            }

            return result;
        }

        public IEnumerable<OtherEvent> FetchOtherEvents(DateTime startTime, DateTime endTime)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IList<OtherEvent> transactions =
                        db.Query<OtherEvent>("GetOtherEvents", new { startTime, endTime }, commandType: CommandType.StoredProcedure).ToList();
                    if (GetLogger().IsDebugEnabled && transactions.Any())
                    {
                        StringBuilder debugString = new StringBuilder();
                        debugString.Append("Results of query, Fetch Fuel Transactions:");

                        foreach (OtherEvent item in transactions)
                        {
                            debugString.Append($" Transaction ID {item.TransactionId} at {item.TransactionTime}");
                        }

                        GetLogger().Debug(debugString.ToString());
                    }

                    TelemetryWorker.QueryReturnedFromHydraDb("GetOtherEvents", guid);

                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return transactions;
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Other Events", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetOtherEvents", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new List<OtherEvent>();
        }

        public DateTime FetchShiftStart(bool dayEnd)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Fetch {(dayEnd ? "Day" : "Shift")} Start");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IList<DateTime> startList =
                        db.Query<DateTime>(dayEnd ? "GetDayStart" : "GetShiftStart", commandType: CommandType.StoredProcedure).ToList();
                    if (startList.Count == 1)
                    {
                        DateTime start = startList.Single();
                        GetLogger().Debug($"Results of query, {(dayEnd ? "Day" : "Shift")} Start Time is {start}");
                        TelemetryWorker.QueryReturnedFromHydraDb("GetDayStart", guid);
                        return start;
                    }
                }
            }
            catch (Exception e)
            {
                GetLogger().Error($"Exception Fetching {(dayEnd ? "Day" : "Shift")} Start", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetDayStart", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return DateTime.MinValue;
        }


        public DateTime? GetNextDayEnd()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IEnumerable<DateTime?> dates = db.Query<DateTime?>("GetNextDayEnd", commandType: CommandType.StoredProcedure).ToList();
                    if (dates.Count() == 1)
                    {
                        TelemetryWorker.QueryReturnedFromHydraDb("GetNextDayEnd", guid);
                        GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                        return dates.Single();
                    }
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Next Day End", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetNextDayEnd", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public int GetLogInterval()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IEnumerable<int> intervals = db.Query<int>("GetLogInterval", commandType: CommandType.StoredProcedure).ToList();
                    if (intervals.Count() == 1)
                    {
                        TelemetryWorker.QueryReturnedFromHydraDb("GetLogInterval", guid);
                        GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                        return intervals.Single();
                    }
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Log Interval", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetLogInterval", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return OneDayInSeconds;
        }

        public void SetTillNumber(short number)
        {
            var result = DbActionWithTelemetry((db) => db.Execute("SetTillNumber", new { number }, commandType: CommandType.StoredProcedure), "SetTillNumber");
            if (result.IsSuccess)
            {
                _cacheHelper.ForceExpirationOnCachedItem($"{ConfigKeySuffixHydraDb}General", ConfigurationConstants.CategoryNameGeneral);
                _cacheHelper.ForceExpirationOnCachedItem(ConfigKeySuffixHydraDb, ConfigurationConstants.CategoryNameSiteInfo);
            }
        }

        public void SetPosClaimNumber(byte number)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set POS Claim Number, Number = {number}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetPosClaimNumber", new { number }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting POS Claim Number", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetPosClaimNumber", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetFilePruneDays(int days)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set File Prune Days, Days = {days}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetFilePruneDays", new { days }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting File Prune Days", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetFilePruneDays", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetTransactionPruneDays(int days)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Transaction Prune Days, Days = {days}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetTransactionPruneDays", new { days }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Transaction Prune Days", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetTransactionPruneDays", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetReceiptPruneDays(int days)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Receipt Prune Days, Days = {days}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetReceiptPruneDays", new { days }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Receipt Prune Days", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetReceiptPruneDays", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetFuelCategory(short category)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Fuel Category, Category = {category}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetFuelCategory", new { category }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Fuel Category", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetFuelCategory", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetMaxFillOverride(uint maxFillOverride)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Max Fill Override, Override = {maxFillOverride}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetMaxFillOverride", new { maxFillOverride = (long)maxFillOverride },
                        commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Max Fill Override", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetMaxFillOverride", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetNextDayEnd(DateTime? dayEnd)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Next Day End, Day End = {(dayEnd == null ? "Not Set" : $"{dayEnd}")}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetNextDayEnd", new { dayEnd }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Next Day End", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetNextDayEnd", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetLogInterval(int interval)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Log Interval, Interval = {interval}  seconds");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetLogInterval", new { interval }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Log Interval", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetLogInterval", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public PrinterConfig GetPrinterConfig()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IEnumerable<PrinterConfig> configs =
                        db.Query<PrinterConfig>("GetPrinterConfig", commandType: CommandType.StoredProcedure).ToList();
                    if (configs.Count() == 1)
                    {
                        TelemetryWorker.QueryReturnedFromHydraDb("GetPrinterConfig", guid);
                        GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                        return configs.Single();
                    }
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Printer Config", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetPrinterConfig", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public void SetPrinterEnabled(bool isEnabled)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Printer Enabled = {isEnabled}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetPrinterEnabled", new { isEnabled }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Printer Enabled", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetPrinterEnabled", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetPrinterPortName(string portName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Printer Port Name = {portName}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetPrinterPortName", new { portName }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Printer Port Name", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetPrinterPortName", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetPrinterBaudRate(int baudRate)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Printer Baud Rate = {baudRate}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetPrinterBaudRate", new { baudRate }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Printer Baud Rate", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetPrinterBaudRate", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetPrinterHandshake(Handshake handshake)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Printer Handshake = {handshake}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetPrinterHandshake", new { handshake }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Printer Handshake", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetPrinterHandshake", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetPrinterStopBits(StopBits stopBits)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Printer Stop Bits = {stopBits}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetPrinterStopBits", new { stopBits }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Printer Stop Bits", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetPrinterStopBits", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetPrinterDataBits(int dataBits)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set Printer Stop Bits = {dataBits}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetPrinterStopBits", new { dataBits }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception setting Printer Data Bits", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetPrinterDataBits", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public IEnumerable<MeterReading> FetchPreviousMeterReadings()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IEnumerable<MeterReading> readings =
                        db.Query<MeterReading>("FetchPreviousMeterReadings", commandType: CommandType.StoredProcedure).ToList();
                    TelemetryWorker.QueryReturnedFromHydraDb("FetchPreviousMeterReadings", guid);

                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return readings;
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Previous Meter Readings", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("FetchPreviousMeterReadings", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new MeterReading[0];
        }

        public void StorePreviousMeterReadings(IEnumerable<MeterReading> readings)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Store Previous Meter Readings");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("ClearPreviousMeterReadings", commandType: CommandType.StoredProcedure);
                }

                foreach (MeterReading reading in readings)
                {
                    using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                    {
                        db.Execute("AddPreviousMeterReading", new
                        {
                            pump = (int)reading.Pump,
                            volume1 = (long)reading.Volume1,
                            cash1 = (long)reading.Cash1,
                            volume2 = (long)reading.Volume2,
                            cash2 = (long)reading.Cash2,
                            volume3 = (long)reading.Volume3,
                            cash3 = (long)reading.Cash3,
                            volume4 = (long)reading.Volume4,
                            cash4 = (long)reading.Cash4
                        }, commandType: CommandType.StoredProcedure);
                    }
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception storing Previous Meter Readings", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("StorePreviousMeterReadings", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void AddShiftToList(int shiftNumber, DateTime startTime, DateTime endTime)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Add to Shift List {shiftNumber} from {startTime} to {endTime}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("AddShiftToList", new
                    {
                        shiftNumber, startTime, endTime
                    }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception adding Shift to List", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("AddShiftToList", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public IEnumerable<Shift> TakeShiftList()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug("Taking Shift List");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IEnumerable<Shift> shifts = db.Query<Shift>("TakeShiftList", commandType: CommandType.StoredProcedure).ToList();
                    TelemetryWorker.QueryReturnedFromHydraDb("TakeShiftList", guid);

                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return shifts;
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception taking Shift List", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("TakeShiftList", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new Shift[0];
        }

        #endregion

        #region Loyalty

        public void AddGenericLoyalty(string name)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Add {name} Loyalty");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("AddLoyaltyReference", new { name }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("AddGenericLoyalty", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void DeleteGenericLoyalty(string name)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Delete {name} Loyalty");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("DeleteLoyaltyReference", new { name }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("DeleteGenericLoyalty", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetGenericLoyaltyPresent(string name, bool present)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Set {name} Loyalty {(present ? "Present" : "Absent")}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetLoyaltyPresent", new
                    {
                        name,
                        present
                    }, commandType: CommandType.StoredProcedure);
                }

                _cacheHelper.ForceExpirationOnCachedItem($"{ConfigKeySuffixHydraDb}{ConfigurationConstants.CachedItemIsGenericLoyaltyAvailable}", name);
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetGenericLoyaltyPresent", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public bool IsGenericLoyaltyAvailable(string name)
        {
            return _cacheHelper.GetCachedItem($"{ConfigKeySuffixHydraDb}{ConfigurationConstants.CachedItemIsGenericLoyaltyAvailable}", name, () => DoIsGenericLoyaltyAvailable(name));
        }
        public bool IsGenericLoyaltyPresent(string name)
        {
            return _cacheHelper.GetCachedItem($"{ConfigKeySuffixHydraDb}{ConfigurationConstants.CachedItemIsGenericLoyaltyPresent}", name, () => DoIsGenericLoyaltyPresent(name));
        }

        internal bool DoIsGenericLoyaltyAvailable(string name)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Is {name} Loyalty Available");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IEnumerable<LoyaltyTerminal> terminals =
                        db.Query<LoyaltyTerminal>("GetLoyaltyTerminal", new { name }, commandType: CommandType.StoredProcedure).ToList();
                    TelemetryWorker.QueryReturnedFromHydraDb(ConfigurationConstants.CachedItemIsGenericLoyaltyAvailable, guid);
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return terminals.Any();
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
                TelemetryWorker.QueryReturnedFromHydraDb(ConfigurationConstants.CachedItemIsGenericLoyaltyAvailable, guid);
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return false;
            }
        }

        private bool DoIsGenericLoyaltyPresent(string name)
        {
            var result = DoAction(() =>
            {
                GetLogger().Debug($"Is {name} Loyalty Present");
                var guid = Guid.NewGuid();
                TelemetryWorker.QuerySentToHydraDb(guid);
                try
                {
                    using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                    {
                        IEnumerable<bool> present = db.Query<bool>("GetLoyaltyPresent", new { name }, commandType: CommandType.StoredProcedure)
                            .ToList();
                        TelemetryWorker.QueryReturnedFromHydraDb(ConfigurationConstants.CachedItemIsGenericLoyaltyPresent, guid);
                        GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                        return Result.Success(present.Any() && present.FirstOrDefault());
                    }
                }
                catch (Exception e)
                {
                    const string sqlError = "Exception raised in database call";
                    GetLogger().Error(sqlError, e);
                    TelemetryWorker.QueryReturnedFromHydraDb(ConfigurationConstants.CachedItemIsGenericLoyaltyPresent, guid);
                    return Result.Failure<bool>(sqlError);
                }
            }, string.Empty);

            return result.IsSuccess && result.Value;
        }

        public GenericLoyalty FetchGenericLoyalty(string name)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Fetch {name} Loyalty");
            object nameObject = new { name };
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IEnumerable<bool> present = db.Query<bool>("GetLoyaltyPresent", nameObject, commandType: CommandType.StoredProcedure)
                        .ToList();

                    var isLoyaltyPresent = present.Count() == 1 && present.Single();

                    IEnumerable<LoyaltyTerminal> terminals =
                        db.Query<LoyaltyTerminal>("GetLoyaltyTerminal", nameObject, commandType: CommandType.StoredProcedure).ToList();
                    if (terminals.Count() > 1)
                    {
                        GetLogger().Error("Terminals returned not single");
                        TelemetryWorker.QueryReturnedFromHydraDb("FetchGenericLoyalty", guid);
                        GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                        return null;
                    }

                    LoyaltyTerminal terminal = terminals.Count() == 1
                        ? terminals.Single()
                        : new LoyaltyTerminal(string.Empty, string.Empty, string.Empty, string.Empty, 0, string.Empty, string.Empty);
                    GetLogger().Debug($"Results of query, Site ID is {terminal?.SiteId}, Terminal ID is {terminal?.TerminalId}" +
                                  $", Footer 1 is {terminal?.Footer1}, Footer 2 is {terminal?.Footer2}");

                    IList<GenericEndPoint> hosts = db
                        .Query<GenericEndPoint>("GetLoyaltyHosts", nameObject, commandType: CommandType.StoredProcedure).ToList();
                    if (GetLogger().IsDebugEnabled && hosts.Any())
                    {
                        StringBuilder debugString = new StringBuilder();
                        debugString.Append("Results of query, Hosts:");

                        foreach (GenericEndPoint entry in hosts)
                        {
                            debugString.Append($" {entry.IpAddress}:{entry.Port}");
                        }

                        GetLogger().Debug(debugString.ToString());
                    }

                    IList<string> hostnames = db.Query<string>("GetLoyaltyHostnames", nameObject, commandType: CommandType.StoredProcedure)
                        .ToList();
                    if (GetLogger().IsDebugEnabled && hostnames.Any())
                    {
                        StringBuilder debugString = new StringBuilder();
                        debugString.Append("Results of query, Hostnames:");

                        foreach (string entry in hostnames)
                        {
                            debugString.Append($" {entry}");
                        }

                        GetLogger().Debug(debugString.ToString());
                    }

                    IList<LoyaltyIin> iins = db.Query<LoyaltyIin>("GetLoyaltyIins", nameObject, commandType: CommandType.StoredProcedure)
                        .ToList();
                    if (GetLogger().IsDebugEnabled && iins.Any())
                    {
                        StringBuilder debugString = new StringBuilder();
                        debugString.Append("Results of query, IINs:");

                        foreach (LoyaltyIin entry in iins)
                        {
                            debugString.Append($" Low {entry.Low} / High {entry.High}");
                        }

                        GetLogger().Debug(debugString.ToString());
                    }

                    IList<LoyaltyMapping> mappings =
                        db.Query<LoyaltyMapping>("GetLoyaltyMappings", nameObject, commandType: CommandType.StoredProcedure).ToList();
                    if (GetLogger().IsDebugEnabled && mappings.Any())
                    {
                        StringBuilder debugString = new StringBuilder();
                        debugString.Append("Results of query, Hosts:");

                        foreach (LoyaltyMapping entry in mappings)
                        {
                            debugString.Append($" Product Code {entry.ProductCode} / Loyalty Code {entry.LoyaltyCode}");
                        }

                        GetLogger().Debug(debugString.ToString());
                    }

                    TelemetryWorker.QueryReturnedFromHydraDb("FetchGenericLoyalty", guid);

                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return new GenericLoyalty(terminal, hosts, hostnames, iins, mappings, isLoyaltyPresent);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
                TelemetryWorker.QueryReturnedFromHydraDb("FetchGenericLoyalty", guid);
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return null;
            }
        }

        public void SetGenericLoyalty(string name, GenericLoyalty loyalty)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Set {name} Loyalty");

            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("ClearLoyalty", new { name }, commandType: CommandType.StoredProcedure);
                    if (loyalty?.Terminal != null)
                    {
                        db.Execute("SetLoyaltyTerminal", new
                        {
                            name,
                            siteId = loyalty.Terminal.SiteId ?? string.Empty,
                            terminalId = loyalty.Terminal.TerminalId ?? string.Empty,
                            footer1 = loyalty.Terminal.Footer1 ?? string.Empty,
                            footer2 = loyalty.Terminal.Footer2 ?? string.Empty,
                            timeout = loyalty.Terminal.Timeout,
                            apiKey = loyalty.Terminal.ApiKey ?? string.Empty,
                            httpHeader = loyalty.Terminal.HttpHeader ?? string.Empty
                        }, commandType: CommandType.StoredProcedure);
                    }

                    if (loyalty?.Hosts != null)
                    {
                        foreach (GenericEndPoint host in loyalty.Hosts)
                        {
                            db.Execute("AddLoyaltyHost", new { name, ipAddress = host.IpAddress, port = host.Port },
                                commandType: CommandType.StoredProcedure);
                        }
                    }

                    if (loyalty?.Hostnames != null)
                    {
                        foreach (string hostname in loyalty.Hostnames)
                        {
                            db.Execute("AddLoyaltyHostname", new { name, hostname }, commandType: CommandType.StoredProcedure);
                        }
                    }

                    if (loyalty?.Iins != null)
                    {
                        foreach (LoyaltyIin iin in loyalty.Iins)
                        {
                            db.Execute("AddLoyaltyIIN", new { name, low = iin.Low, high = iin.High },
                                commandType: CommandType.StoredProcedure);
                        }
                    }

                    if (loyalty?.TariffMappings != null)
                    {
                        foreach (LoyaltyMapping mapping in loyalty.TariffMappings)
                        {
                            db.Execute("AddLoyaltyMapping",
                                new { name, productCode = mapping.ProductCode, loyaltyCode = mapping.LoyaltyCode },
                                commandType: CommandType.StoredProcedure);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetGenericLoyalty", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        /// <inheritdoc cref="IHydraDbConfig.SetCategories(IEnumerable{CategoryConfiguration}, IEnumerable{ConfigKeyValueValidator}, IEnumerable{Action{IEnumerable{CategoryConfiguration}}})"/>
        public Result SetCategories(IEnumerable<CategoryConfiguration> categories, IEnumerable<ConfigKeyValueValidator> preActions = null, IEnumerable<Action<IEnumerable<CategoryConfiguration>>> postActions = null)
        {
            var valid = categories.IsValidForUpdate();
            if (!valid.IsSuccess)
            {
                DoDeferredLogging(LogLevel.Warn, HeaderException, () => new[] { valid.Error });
                return valid;
            }

            foreach (var action in preActions ?? Enumerable.Empty<ConfigKeyValueValidator>())
            {
                var result = categories.IsKnownConfigKeyValueValid(action.Key, action.Validator);
                if (!result)
                {
                    return Result.Failure($"Key: {action.Key}, Value validation failed!");
                }
            }

            _configurationRepository.SetCategories(categories);

            _cacheHelper.ForceExpirationOnAllItems();

            foreach (var action in postActions ?? Enumerable.Empty<Action<IEnumerable<CategoryConfiguration>>>())
            {
                action.Invoke(categories);
            }

            return Result.Success();
        }

        #endregion

        #region Washes

        public IList<Wash> FetchWashes()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IList<Wash> washes = db.Query<Wash>("GetWashes", commandType: CommandType.StoredProcedure).ToList();
                    if (GetLogger().IsDebugEnabled && washes.Any())
                    {
                        StringBuilder debugString = new StringBuilder();
                        debugString.Append("Results of query, Washes:");

                        foreach (Wash entry in washes)
                        {
                            debugString.Append(
                                $" Program Id {entry.ProgramId} / Product Code {entry.ProductCode} / Description {entry.Description} / Price {entry.Price} / VAT Rate {entry.VatRate} / Category {entry.Category} / Subcategory {entry.Subcategory}");
                        }

                        GetLogger().Debug(debugString.ToString());
                    }

                    TelemetryWorker.QueryReturnedFromHydraDb("GetWashes", guid);
                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return washes;
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
                TelemetryWorker.QueryReturnedFromHydraDb("GetWashes", guid);
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return new List<Wash>();
            }
        }

        public void AddWash(Wash wash)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug("Add Wash");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("AddWash", new
                    {
                        programId = wash.ProgramId,
                        productCode = wash.ProductCode,
                        description = wash.Description,
                        price = wash.Price,
                        vatRate = wash.VatRate,
                        category = wash.Category,
                        subcategory = wash.Subcategory
                    }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("AddWash", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void RemoveWashByProgramId(byte programId)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Debug($"Remove Wash by Program Id {programId}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("RemoveWash", new { programId }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("RemoveWash", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        #endregion

        #region Tariff Mappings

        /// <inheritdoc/>
        public IList<TariffMapping> FetchTariffMappings()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IList<TariffMapping> mappings =
                        db.Query<TariffMapping>("GetTariffMappings", commandType: CommandType.StoredProcedure).ToList();
                    if (GetLogger().IsDebugEnabled && mappings.Any())
                    {
                        StringBuilder debugString = new StringBuilder();
                        debugString.Append("Results of query, Tariff Mappings:");

                        foreach (TariffMapping item in mappings)
                        {
                            debugString.Append($" {item.Grade} => {item.ProductCode}");
                        }

                        GetLogger().Debug(debugString.ToString());
                    }

                    TelemetryWorker.QueryReturnedFromHydraDb("GetTariffMappings", guid);

                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return mappings;
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Tariff Mappings", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetTariffMappings", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new List<TariffMapping>();
        }

        /// <inheritdoc/>
        public void SetTariffMappings(IList<TariffMapping> mappings)
        {
            var result = DbActionWithTelemetry((db) => db.Execute("ClearTariffMappings", commandType: CommandType.StoredProcedure), "ClearTariffMappings");
            if (result.IsSuccess)
            {
                foreach (var mapping in mappings)
                {
                    result = DbActionWithTelemetry((db) => db.Execute("AddTariffMapping", new { grade = mapping.Grade, productCode = mapping.ProductCode }, commandType: CommandType.StoredProcedure), "AddTariffMapping");
                    if (result.IsSuccess)
                    {
                        result = DbActionWithTelemetry((db) => db.Execute("SetFuelCardsOnly", new { grade = mapping.Grade, flag = mapping.FuelCardsOnly }, commandType: CommandType.StoredProcedure), "SetFuelCardsOnly");
                    }
                }

                InvalidateCacheForBosConfigMapping();
            }
        }

        /// <inheritdoc/>
        public Result<int> SetTariffMappingFuelCardsOnly(byte grade, bool flag)
        {
            DoDeferredLogging(LogLevel.Info, "Grade", () => new[] { $"{grade}, {(flag ? "Set" : "Clear")} Tariff Mapping Fuel Cards Only" });
            var result = DbActionWithTelemetry((db) => db.Execute("SetFuelCardsOnly", new { grade, flag }, commandType: CommandType.StoredProcedure), "SetFuelCardsOnly");
            if (result.IsSuccess)
            {
                InvalidateCacheForBosConfigMapping();
            }

            return result;
        }

        #endregion

        #region Predefined Amounts

        public IList<int> FetchPredefinedAmounts()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IList<int> amounts = db.Query<int>("GetPredefinedAmounts", commandType: CommandType.StoredProcedure).ToList();
                    if (GetLogger().IsDebugEnabled && amounts.Any())
                    {
                        StringBuilder debugString = new StringBuilder();
                        debugString.Append("Results of query, Predefined Amounts:");

                        foreach (int item in amounts)
                        {
                            debugString.Append($" {item}");
                        }

                        GetLogger().Debug(debugString.ToString());
                    }

                    TelemetryWorker.QueryReturnedFromHydraDb("GetPredefinedAmounts", guid);

                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return amounts;
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Predefined Amounts", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetPredefinedAmounts", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new List<int>();
        }

        public void SetPredefinedAmounts(IList<int> amounts)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Set Predfined Amounts");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("ClearPredefinedAmounts", commandType: CommandType.StoredProcedure);

                    foreach (int amount in amounts)
                    {
                        db.Execute("AddPredefinedAmount", new { amount }, commandType: CommandType.StoredProcedure);
                    }
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetPredefinedAmounts", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        #endregion

        #region Discount Cards

        public IList<DiscountCard> FetchDiscountCards()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IList<DiscountCard> cards = db.Query<DiscountCard>("GetDiscountCards", commandType: CommandType.StoredProcedure)
                        .ToList();
                    foreach (DiscountCard card in cards)
                    {
                        IEnumerable<string> pans = db.Query<string>("GetDiscountWhitelist", new { iin = card.Iin },
                            commandType: CommandType.StoredProcedure);
                        card.SetWhitelist(pans);
                    }

                    if (GetLogger().IsDebugEnabled && cards.Any())
                    {
                        StringBuilder debugString = new StringBuilder();
                        debugString.Append("Results of query, Discount Cards:");

                        foreach (DiscountCard item in cards)
                        {
                            debugString.Append(
                                $" IIN {item.Iin} / Name {item.Name} / Type {item.Type} / Value {item.Value} / Grade {item.Grade}");
                            if (item.Whitelist.Any())
                            {
                                debugString.Append(" / Whitelist");
                                foreach (string pan in item.Whitelist)
                                {
                                    debugString.Append($" PAN {pan}");
                                }
                            }
                        }

                        GetLogger().Debug(debugString.ToString());
                    }

                    TelemetryWorker.QueryReturnedFromHydraDb("GetDiscountCards", guid);

                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return cards;
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Discount Cards", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetDiscountCards", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new List<DiscountCard>();
        }

        public void AddDiscountCard(string iin, string name, string type, float value, byte grade)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Add Discount Card");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("AddDiscountCard", new { iin, name, type, value, grade }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("AddDiscountCard", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void RemoveDiscountCard(string iin)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Remove Discount Card");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("RemoveDiscountCard", new { iin }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("RemoveDiscountCard", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void AddDiscountWhitelist(string iin, string pan)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Add Discount Whitelist");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("AddDiscountWhitelist", new { iin, pan }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("AddDiscountWhitelist", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void RemoveDiscountWhitelist(string iin, string pan)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Remove Discount Whitelist");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("RemoveDiscountWhitelist", new { iin, pan }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("RemoveDiscountWhitelist", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        #endregion

        #region Local Accounts

        private static string LocalAccountCustomersDebugString(IList<LocalAccountCustomer> customers)
        {
            StringBuilder debugString = new StringBuilder();
            debugString.Append("Results of query, Local Account Customers:");

            foreach (LocalAccountCustomer item in customers)
            {
                debugString.Append($" Customer Reference {item.CustomerReference} / Name {item.Name}");
                if (item.Cards.Any())
                {
                    debugString.Append(" / Whitelist");
                    foreach (LocalAccountCard card in item.Cards)
                    {
                        debugString.Append($" PAN {card.Pan}");
                    }
                }
            }

            return debugString.ToString();
        }

        public IList<LocalAccountCustomer> FetchLocalAccountCustomers()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IList<LocalAccountCustomer> customers =
                        db.Query<LocalAccountCustomer>("GetLocalAccountCustomers", commandType: CommandType.StoredProcedure).ToList();
                    foreach (LocalAccountCustomer customer in customers)
                    {
                        IList<LocalAccountCard> cards = db.Query<LocalAccountCard>("GetLocalAccountCards",
                            new { reference = customer.CustomerReference }, commandType: CommandType.StoredProcedure).ToList();
                        customer.SetCards(cards);
                    }

                    if (GetLogger().IsDebugEnabled && customers.Any())
                    {
                        GetLogger().Debug(LocalAccountCustomersDebugString(customers));
                    }

                    TelemetryWorker.QueryReturnedFromHydraDb("GetLocalAccountCustomers", guid);

                    GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                    return customers;
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Local Accounts", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetLocalAccounts", guid);

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return new List<LocalAccountCustomer>();
        }

        public void AddLocalAccountCustomer(LocalAccountCustomer customer)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Add Local Account Customer");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("AddLocalAccountCustomer", new
                    {
                        reference = customer.CustomerReference,
                        name = customer.Name,
                        transactionsAllowed = customer.TransactionsAllowed,
                        transLimit = (long)customer.TransactionLimit,
                        pin = customer.Pin,
                        printValue = customer.PrintValue,
                        allowLoyalty = customer.AllowLoyalty,
                        fuelOnly = customer.FuelOnly,
                        registrationEntry = customer.RegistrationEntry,
                        mileageEntry = customer.MileageEntry,
                        prepayAccount = customer.PrePayAccount,
                        lowCreditWarning = customer.LowCreditWarning,
                        maxCreditReached = customer.MaxCreditReached,
                        balance = (long)customer.Balance,
                        customerExists = customer.CustomerExists
                    }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("AddLocalAccountCustomer", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void DeleteLocalAccountCustomer(LocalAccountCustomer customer)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Delete Local Account Customer");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("DeleteLocalAccountCustomer", new
                    {
                        reference = customer.CustomerReference
                    }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("DeleteLocalAccountCustomer", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void AddLocalAccountCard(LocalAccountCustomer customer, LocalAccountCard card)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Add Local Account Card");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("AddLocalAccountCard", new
                    {
                        pan = card.Pan,
                        customerReference = customer.CustomerReference,
                        description = card.Description,
                        discount = (double)card.Discount,
                        noRestrictions = card.NoRestrictions,
                        unleaded = card.Unleaded,
                        diesel = card.Diesel,
                        lpg = card.Lpg,
                        lrp = card.Lrp,
                        gasOil = card.GasOil,
                        adBlue = card.AdBlue,
                        kerosene = card.Kerosene,
                        oil = card.Oil,
                        avgas = card.Avgas,
                        jet = card.Jet,
                        mogas = card.Mogas,
                        valeting = card.Valeting,
                        otherMotorRelatedGoods = card.OtherMotorRelatedGoods,
                        shopGoods = card.ShopGoods,
                        hot = card.Hot
                    }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("AddLocalAccountCard", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void DeleteLocalAccountCard(LocalAccountCard card)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info("Delete Local Account Card");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("DeleteLocalAccountCard", new
                    {
                        pan = card.Pan
                    }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("DeleteLocalAccountCard", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        #endregion

        private string TagPump(string message, byte pump)
        {
            return $"{message}.{PumpModel.HeaderPump}: {pump}";
        }

        #region Pump Delivery

        /// <inheritdoc />
        public void SetOptPayment(byte pump, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info, PumpModel.HeaderPump, () => new[] { $"{pump}" });
                var guid = TelemetryWorker.QuerySentToHydraDb(message);
                try
                {
                    using (var db = DbExecutorFactory.CreateExecutor())
                    {
                        db.Execute("SetPumpOPTPayment", new { pump }, commandType: CommandType.StoredProcedure);
                    }
                }
                catch (Exception ex)
                {
                    GetLogger().Error(HeaderException, () => new[] { ex.Message }, ex);
                }

                TelemetryWorker.QueryReturnedFromHydraDb(TagPump("SetPumpOPTPayment", pump), guid, message);
            }, message.FullId);
        }

        /// <inheritdoc />
        public void ClearOptPayment(byte pump, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info, PumpModel.HeaderPump, () => new[] { $"{pump}" });
                var guid = TelemetryWorker.QuerySentToHydraDb(message);
                try
                {
                    using (var db = DbExecutorFactory.CreateExecutor())
                    {
                        db.Execute("ClearPumpOPTPayment", new { pump }, commandType: CommandType.StoredProcedure);
                    }
                }
                catch (Exception ex)
                {
                    GetLogger().Error(HeaderException, () => new[] { ex.Message }, ex);
                }

                TelemetryWorker.QueryReturnedFromHydraDb(TagPump("ClearPumpOPTPayment", pump), guid, message);
            }, message.FullId);
        }

        /// <inheritdoc />
        public bool HasOptPayment(byte pump, IMessageTracking message = null)
        {
            var result = false;
            message ??= new MessageTracking();
            DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info, PumpModel.HeaderPump, () => new[] { $"{pump}" });
                var guid = TelemetryWorker.QuerySentToHydraDb(message);
                try
                {
                    using (var db = DbExecutorFactory.CreateExecutor())
                    {
                        var pumpDelivered = db.Query<PumpDelivered>("GetPumpDelivered", new { pump }, commandType: CommandType.StoredProcedure).ToList();
                        if (pumpDelivered.Count == 1)
                        {
                            TelemetryWorker.QueryReturnedFromHydraDb("GetPumpDelivered", message.Id);
                            result = pumpDelivered[0].HasOptPayment;
                        }
                    }
                }
                catch (Exception ex)
                {
                    GetLogger().Error(HeaderException, () => new[] { ex.Message }, ex);
                }

                TelemetryWorker.QueryReturnedFromHydraDb(TagPump("GetPumpDelivered", pump), guid, message);

            }, message.FullId);

            return result;
        }

        /// <inheritdoc />
        public Result<int> SetDelivered(IMessageTracking message, byte pump, byte grade, uint volume, uint amount, string name, ushort price, uint netAmount, uint vatAmount, float vatRate, int transSeqNum, byte hose)
        {
            message ??= new MessageTracking();
            DoDeferredLogging(LogLevel.Info, PumpModel.HeaderPump, () => new[] { $"{pump}" });

            return DbActionWithTelemetry((db) => db.Execute("SetPumpDelivered", new
            {
                pump = (int)pump,
                grade = (int)grade,
                volume = (long)volume,
                amount = (long)amount,
                name,
                price = (int)price,
                netAmount = (long)netAmount,
                vatAmount = (long)vatAmount,
                vatRate,
                transSeqNum,
                hose
            }, commandType: CommandType.StoredProcedure), "SetPumpDelivered", message);
        }

        /// <inheritdoc />
        public Result<PumpDelivered> GetDelivered(byte pump, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            var result = DbActionWithTelemetry((db) => db.Query<PumpDelivered>("GetPumpDelivered", new { pump }, commandType: CommandType.StoredProcedure), "GetPumpDelivered", message);

            return !result.IsSuccess ? Result.Failure<PumpDelivered>(result.Error) : result.Value.Count() == 1 ? result.Value.First() : Result.Failure<PumpDelivered>("Invalid data");
        }

        /// <inheritdoc />
        public Result<int> ClearDelivered(byte pump, IMessageTracking message)
        {
            message ??= new MessageTracking();
            DoDeferredLogging(LogLevel.Info, PumpModel.HeaderPump, () => new[] { $"{pump}" });

            return DbActionWithTelemetry((db) => db.Execute("ClearPumpDelivered", new { pump }, commandType: CommandType.StoredProcedure), "ClearPumpDelivered", message);
        }

        public bool HasDelivered
        (byte pump, out byte grade, out uint volume, out uint amount, out string name, out ushort price, out uint netAmount,
            out uint vatAmount, out float vatRate)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            DoDeferredLogging(LogLevel.Info, PumpModel.HeaderPump, () => new[] { $"{pump}" });
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    IList<PumpDelivered> pumpDelivered =
                        db.Query<PumpDelivered>("GetPumpDelivered", new { pump }, commandType: CommandType.StoredProcedure).ToList();
                    if (pumpDelivered.Count == 1)
                    {
                        grade = pumpDelivered[0].Grade;
                        volume = pumpDelivered[0].Volume;
                        amount = pumpDelivered[0].Amount;
                        name = pumpDelivered[0].Name;
                        price = pumpDelivered[0].Price;
                        netAmount = pumpDelivered[0].NetAmount;
                        vatAmount = pumpDelivered[0].VatAmount;
                        vatRate = pumpDelivered[0].VatRate;
                        GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                        return pumpDelivered[0].HasDelivered;
                    }
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception Fetching Has Delivered", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb(TagPump("GetPumpDelivered", pump), guid);

            grade = 0;
            volume = 0;
            amount = 0;
            name = null;
            price = 0;
            netAmount = 0;
            vatAmount = 0;
            vatRate = 0;
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return false;
        }

        #endregion

        #region File Locations

        /// <inheritdoc />
        public AllFileLocations GetFileLocations()
        {
            return _cacheHelper.GetCachedItem(ConfigKeySuffixHydraDb, ConfigurationConstants.CachedItemAllFileLocations, () => DoGetFileLocations());
        }

        internal AllFileLocations DoGetFileLocations()
        {
            var result = DbActionWithTelemetry((db) => db.Query<AllFileLocations>("GetFileLocations", commandType: CommandType.StoredProcedure), "GetFileLocations");
            if (!result.IsSuccess)
            {
                return null;
            }

            var locations = result.Value.ToList();

            if (locations.Count == 1)
            {
                DoDeferredLogging(LogLevel.Debug, "Results of query", () => new[] { $"Retalix Transaction File Directory {locations[0].RetalixTransactionFileDirectory}" });

                return locations[0];
            }

            return null;
        }

        private Result SetFileLocation<T>(string queryName, T value, CommandType commandType = CommandType.StoredProcedure, IMessageTracking message = default)
        {
            message ??= new MessageTracking();

            var result = DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Info, HeaderParameters, () => new[] { queryName });

                var result = DbActionWithTelemetry((db) => db.Execute(queryName, value /*is string ? new { directory = value } : value*/, commandType: commandType), queryName, message);

                return result;
            }, message.FullId);

            if (result.IsSuccess)
            {
                _cacheHelper.ForceExpirationOnCachedItem(ConfigKeySuffixHydraDb, ConfigurationConstants.CachedItemAllFileLocations);
            }

            return result;
        }

        /// <inheritdoc />
        public void SetRetalixTransactionFileDirectory(string directory) => SetFileLocation($"{(directory == null ? "Clear" : "Set")}RetalixTransactionFileDirectory", new { directory });

        /// <inheritdoc />
        public void SetTransactionFileDirectory(string directory) => SetFileLocation("SetTransactionFileDirectory", new { directory });

        /// <inheritdoc />
        public void SetWhitelistDirectory(string directory) => SetFileLocation("SetWhitelistDirectory", new { directory });

        /// <inheritdoc />
        public void SetLayoutDirectory(string directory) => SetFileLocation("SetLayoutDirectory", new { directory });

        /// <inheritdoc />
        public void SetSoftwareDirectory(string directory) => SetFileLocation("SetSoftwareDirectory", new { directory });

        /// <inheritdoc />
        public void SetMediaDirectory(string directory) => SetFileLocation("SetMediaDirectory", new { directory });

        /// <inheritdoc />
        public void SetPlaylistDirectory(string directory) => SetFileLocation("SetPlaylistDirectory", new { directory });

        /// <inheritdoc />
        public void SetOptLogFileDirectory(string directory) => SetFileLocation("SetOPTLogFileDirectory", new { directory });

        /// <inheritdoc />
        public void SetLogFileDirectory(string directory) => SetFileLocation("SetLogFileDirectory", new { directory });

        /// <inheritdoc />
        public void SetTraceFileDirectory(string directory) => SetFileLocation("SetTraceFileDirectory", new { directory });

        /// <inheritdoc />
        public void SetJournalFileDirectory(string directory) => SetFileLocation("SetJournalFileDirectory", new { directory });

        /// <inheritdoc />
        public void SetReceivedUpdateDirectory(string directory) => SetFileLocation("SetReceivedUpdateDirectory", new { directory });

        /// <inheritdoc />
        public void SetDatabaseBackupDirectory(string directory) => SetFileLocation("SetDatabaseBackupDirectory", new { directory });

        /// <inheritdoc />
        public void SetContactlessPropertiesFile(string fileName) => SetFileLocation("SetContactlessPropertiesFile", new { fileName });

        /// <inheritdoc />
        public void SetFuelDataUpdateFile(string fileName) => SetFileLocation("SetFuelDataUpdateFile", new { fileName });

        /// <inheritdoc />
        public void SetUpgradeFileDirectory(string directory) => SetFileLocation("SetUpgradeFileDirectory", new { directory });

        /// <inheritdoc />
        public void SetRollbackFileDirectory(string directory) => SetFileLocation("SetRollbackFileDirectory", new { directory });

        /// <inheritdoc />
        public void SetEsocketConnectionString(string newConnectionString) => SetFileLocation("SetESocketConnectionString", new { newConnectionString });

        /// <inheritdoc />
        public void SetEsocketUseConnectionString(bool useConnectionString) => SetFileLocation("SetESocketUseConnectionString", new { useConnectionString });

        /// <inheritdoc />
        public void SetEsocketConfigFile(string fileName) => SetFileLocation("SetESocketConfigFile", new { fileName });

        /// <inheritdoc />
        public void SetEsocketKeystoreFile(string fileName) => SetFileLocation("SetESocketKeystoreFile", new { fileName });

        /// <inheritdoc />
        public void SetEsocketDbUrl(string url) => SetFileLocation("SetESocketDbUrl", new { url });

        /// <inheritdoc />
        public void SetEsocketOverrideProperties(bool flag) => SetFileLocation("SetESocketOverrideProperties", new { flag });

        /// <inheritdoc />
        public void SetEsocketOverrideKeystore(bool flag) => SetFileLocation("SetESocketOverrideKeystore", new { flag });

        /// <inheritdoc />
        public void SetEsocketOverrideUrl(bool flag) => SetFileLocation("SetESocketOverrideUrl", new { flag });

        /// <inheritdoc />
        public void SetEsocketOverrideContactless(bool flag) => SetFileLocation("SetESocketOverrideContactless", new { flag });

        #endregion

        #region DOMS

        public DomsInfo GetDomsInfo()
        {
            return _cacheHelper.GetCachedItem(ConfigKeySuffixHydraDb, nameof(DomsInfo), () => DoGetDomsInfo());
        }

        internal DomsInfo DoGetDomsInfo()
        {
            var result = DbActionWithTelemetry((db) => db.Query<DomsInfo>("GetDOMSInfo", commandType: CommandType.StoredProcedure), "GetDomsInfo");
            if (!result.IsSuccess)
            {
                return null;
            }

            var info = result.Value.ToList().FirstOrDefault();
            if (info != null)
            {
                if (IPAddress.TryParse(info.IpAddress, out IPAddress address))
                {
                    DoDeferredLogging(LogLevel.Debug, "Results of query", () => new[] { $"Address: {address}" });
                }
                else
                {
                    DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { $"Unable to parse {info.IpAddress}" });
                    return null;
                }
            }

            return info;
        }

        public void SetDomsEnabled(bool enabled)
        {
            var result = DbActionWithTelemetry((db) => db.Execute("SetDOMSEnabled", new { enabled }, commandType: CommandType.StoredProcedure), "SetDomsEnabled");
        }

        public void SetDomsDetect(bool detect)
        {
            var result = DbActionWithTelemetry((db) => db.Execute("SetDOMSDetect", new { detect }, commandType: CommandType.StoredProcedure), "SetDomsDetect");
        }

        public void SetDomsIpAddress(IPAddress ip)
        {
            var result = DbActionWithTelemetry((db) =>
            {
                var dbResult = db.Execute("SetDOMSIpAddress", new { ipAddress = ip.ToString() }, commandType: CommandType.StoredProcedure);
                _cacheHelper.ForceExpirationOnCachedItem(ConfigKeySuffixHydraDb, nameof(DomsInfo));
                return dbResult;
            }, "SetDomsIpAddress");
        }

        public void SetDomsLoginString(string loginString)
        {
            var result = DbActionWithTelemetry((db) => db.Execute("SetDOMSLoginString", new { loginString }, commandType: CommandType.StoredProcedure), "SetDomsLoginString");
        }

        #endregion

        #region Fuelling

        /// <inheritdoc/>
        public FuellingInfo GetFuelling()
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (var db = DbExecutorFactory.CreateExecutor())
                {
                    var fuelling = db.Query<FuellingInfo>("GetFuelling", commandType: CommandType.StoredProcedure).ToList();
                    if (fuelling.Count == 1)
                    {
                        GetLogger().Debug($"Results of query, Fuelling {(fuelling[0].IsIndefiniteWait ? "" : "Not")} Indefinite Wait," +
                                      $" Wait Minutes {fuelling[0].WaitMinutes}," + $" Backoff Auth {fuelling[0].BackoffAuth}," +
                                      $" Backoff Stop Start {fuelling[0].BackoffStopStart}," +
                                      $" Backoff Stop Only {fuelling[0].BackoffStopOnly}");
                        TelemetryWorker.QueryReturnedFromHydraDb("GetFuelling", guid);

                        GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                        return fuelling[0];
                    }
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception fetching Fuelling", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("GetFuelling", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
            return null;
        }

        public void SetFuellingIndefiniteWait(bool flag)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Setting Fuelling Indefinite Wait to {flag}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetFuellingIndefiniteWait", new {flag}, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetFuellingIndefiniteWait", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetFuellingWaitMinutes(int minutes)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Setting Fuelling Wait Minutes to {minutes}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetFuellingWaitMinutes", new {minutes}, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetFuellingWaitMinutes", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetFuellingBackoffAuth(int backoff)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Setting Fuelling Backoff Auth to {backoff}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetFuellingBackoffAuth", new {backoff}, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetFuellingBackoffAuth", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetFuellingBackoffPreAuth(int backoff)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Setting Fuelling Backoff Pre Auth to {backoff}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetFuellingBackoffPreAuth", new {backoff}, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetFuellingBackoffPreAuth", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetFuellingBackoffStopStart(int backoff)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Setting Fuelling Backoff Stop Start to {backoff}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetFuellingBackoffStopStart", new {backoff}, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetFuellingBackoffStopStart", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void SetFuellingBackoffStopOnly(int backoff)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Setting Fuelling Backoff Stop Only to {backoff}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute("SetFuellingBackoffStopOnly", new {backoff}, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("SetFuellingBackoffStopOnly", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        #endregion

        #region SQL Script

        public void RunQuery(string query)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info(
                $"Running script section{Environment.NewLine}{query.Replace(Environment.NewLine, "\n").Replace("\n", Environment.NewLine)}");
            Guid guid = Guid.NewGuid();
            TelemetryWorker.QuerySentToHydraDb(guid);
            try
            {
                using (IDbExecutor db = DbExecutorFactory.CreateExecutor())
                {
                    db.Execute(query, commandType: CommandType.Text);
                }
            }
            catch (Exception e)
            {
                GetLogger().Error("Exception raised in database call", e);
            }

            TelemetryWorker.QueryReturnedFromHydraDb("RunQuery", guid);
            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        public void RunScript(string fileName)
        {
            GetLogger().Debug(HtecLoggingConstants.EntryMessage);
            GetLogger().Info($"Run Script at {fileName}");
            if (!File.Exists(fileName))
            {
                GetLogger().Warn($"File {fileName} does not exist");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            string script = string.Empty;

            using (TextReader reader = new StreamReader(fileName))
            {
                script = reader.ReadToEnd();
            }

            if (!script.All(x => char.IsLetterOrDigit(x) || char.IsWhiteSpace(x) || char.IsPunctuation(x) || char.IsSymbol(x)))
            {
                GetLogger().Warn($"File {fileName} contains invalid characters");
                GetLogger().Debug(HtecLoggingConstants.ExitMessage);
                return;
            }

            script = ' ' + script + ' ';
            IList<string> sections = new List<string>();
            int start = 0;

            for (int i = 0; i < script.Length - 3; i++)
            {
                if (char.IsWhiteSpace(script[i]) && char.ToLower(script[i + 1]) == 'g' && char.ToLower(script[i + 2]) == 'o' &&
                    char.IsWhiteSpace(script[i + 3]))
                {
                    string section = script.Substring(start, i + 1 - start).Trim();
                    start = i + 3;
                    if (!string.IsNullOrWhiteSpace(section) && !section.Contains('#'))
                    {
                        sections.Add(section);
                    }
                }
            }

            foreach (string section in sections)
            {
                RunQuery(section);
            }

            GetLogger().Debug(HtecLoggingConstants.ExitMessage);
        }

        #endregion

        /// <inheritdoc/>
        public AdvancedConfig AdvancedConfig
        {
            get { return _cacheHelper.GetCachedItem(ConfigKeySuffixHydraDb, ConfigurationConstants.CachedItemAdvancedConfig, () => new AdvancedConfig(this, Logger, ConfigurationManager)); }
        }

        public EndPointsConfig EndPointsConfig
        {
            get { return _cacheHelper.GetCachedItem($"{ConfigKeySuffixHydraDb}{ConfigurationConstants.CategoryNameBOS}:", ConfigurationConstants.CachedItemEndPoints, () => new EndPointsConfig(Logger, ConfigurationManager)); }
        }

        public BosConfig BosConfig
        {
            get { return _cacheHelper.GetCachedItem($"{ConfigKeySuffixHydraDb}{ConfigurationConstants.CategoryNameBOS}:", ConfigurationConstants.CachedItemBosConfig, () => new BosConfig(Logger, ConfigurationManager)); }
        }

        /// <inheritdoc/>
        public string Id { get; private set; }

        /// <inheritdoc/>
        public void SetId(string id) => Id = id ?? throw new ArgumentNullException(nameof(id));

        /// <inheritdoc/>
        public Result<IEnumerable<VersionInfo>> GetVersionInfo()
        {
            var message = new MessageTracking();
            return DoAction(() =>
            {
                var guid = TelemetryWorker.QuerySentToHydraDb(message);
                try
                {
                    var results = Enumerable.Empty<VersionInfo>();
                    using (var db = DbExecutorFactory.CreateExecutor())
                    {
                        results = db.Query<VersionInfo>("GetVersionInfo", commandType: CommandType.StoredProcedure).ToList();
                    }

                    TelemetryWorker.QueryReturnedFromHydraDb(nameof(GetVersionInfo), guid);

                    return Result.Success(results);
                }
                catch (Exception ex)
                {
                    GetLogger().Error(HeaderException, () => new[] { ex.Message }, ex);
                    return Result.Failure<IEnumerable<VersionInfo>>(ex.Message);
                }
            }, null);
        }

        /// <inheritdoc/>
        public Result<TransactionBookingState> GetTransactionBooking(long? transId = null, string txnNumber = null, string externalTransId = null, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            var result = DbActionWithTelemetry((db) => db.Query<TransactionBookingState>("GetTransactionBooking", new { transId, txnNumber, externalTransId }, commandType: CommandType.StoredProcedure), "GetPendingTransactionBookings", message);

            return !result.IsSuccess ? Result.Failure<TransactionBookingState>(result.Error) :
                result.Value.Any() ? Result.Success(result.Value.FirstOrDefault()) : Result.Failure<TransactionBookingState>($"{HttpStatusCode.BadRequest}");
        }

        /// <inheritdoc/>
        public Result<StatusCodeResult> GetTransactionBookingHttp(long? transId = null, string txnNumber = null, string externalTransId = null, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            var result = DbActionWithTelemetry((db) => db.Query<TransactionBookingState>("GetTransactionBooking", new { transId, txnNumber, externalTransId }, commandType: CommandType.StoredProcedure), "GetPendingTransactionBookings", message);

            return MapDbQueryResult<TransactionBookingState>(result);
        }

        /// <inheritdoc/>
        public Result<IEnumerable<TransactionBookingState>> GetPendingTransactionBookings(IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            return DbActionWithTelemetry((db) => db.Query<TransactionBookingState>("GetPendingTransactionBookings", null, commandType: CommandType.StoredProcedure), "GetPendingTransactionBookings", message);
        }

        /// <inheritdoc/>
        public Result<StatusCodeResult> GetPendingTransactionBookingsHttp(IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            var result = DbActionWithTelemetry((db) => db.Query<TransactionBookingState>("GetPendingTransactionBookings", null, commandType: CommandType.StoredProcedure), "GetPendingTransactionBookings", message);

            return !result.IsSuccess ? Result.Success(StatusCodeResult.Specific(HttpStatusCode.BadRequest)) :
                Result.Success(StatusCodeResult<IEnumerable<TransactionBookingState>>.Success(result.Value) as StatusCodeResult);
        }

        /// <inheritdoc/>
        public Result UpdateTransactionBooking(long id, long transId, SendTransactionItem item, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            return DbActionWithTelemetry((db) => db.Execute("UpdateTransactionBooking", new { id, transId, sendTransactionItem = JsonConvert.SerializeObject(item) }, commandType: CommandType.StoredProcedure), "UpdateTransactionBooking", message);
        }

        /// <inheritdoc/>
        public Result UpdateTransactionBooking(long id, long transId, string externalId, int shiftId, int periodId, DateTime businessDate, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            return DbActionWithTelemetry((db) => db.Execute("UpdateTransactionBooking", new { id, transId, externalTransId = externalId, shiftId, periodId, businessDate }, commandType: CommandType.StoredProcedure), "UpdateTransactionBooking", message);
        }      

        /// <inheritdoc/>
        public Result<StatusCodeResult> UpdateTransactionBookingHttp(long id, long transId, string externalId, int shiftId, int periodId, DateTime businessDate, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            var result = DbActionWithTelemetry((db) => db.Execute("UpdateTransactionBooking", new { id, transId, externalTransId = externalId, shiftId, periodId, businessDate }, commandType: CommandType.StoredProcedure), "UpdateTransactionBooking", message);

            return MapDbExecuteResult(result);
        }

        /// <inheritdoc/>
        public Result UpdateTransactionBooking(long id, long transId, HttpStatusCode responseCode, string response, int retryCount, DateTime nextRetryDate, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            return DbActionWithTelemetry((db) => db.Execute("UpdateTransactionBooking", new { id, transId, httpStatusCode = (int)responseCode, httpResponse = response, retryCount, nextRetryDate }, commandType: CommandType.StoredProcedure), "UpdateTransactionBooking", message);
        }

        /// <inheritdoc/>
        public Result<StatusCodeResult> UpdateTransactionBookingHttp(long id, long transId, HttpStatusCode responseCode, string response, int retryCount, DateTime nextRetryDate, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            var result = DbActionWithTelemetry((db) => db.Execute("UpdateTransactionBooking", new { id, transId, httpStatusCode = (int)responseCode, httpResponse = response, retryCount, nextRetryDate }, commandType: CommandType.StoredProcedure), "UpdateTransactionBooking", message);

            return MapDbExecuteResult(result);
        }

        /// <inheritdoc/>
        public Result CompleteTransactionBooking(long id, long transId, DateTime bookedDate, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            return DbActionWithTelemetry((db) => db.Execute("UpdateTransactionBooking", new { id, transId, bookedDate}, commandType: CommandType.StoredProcedure), "UpdateTransactionBooking", message);
        }

        /// <inheritdoc/>
        public Result<StatusCodeResult> CompleteTransactionBookingHttp(long id, long transId, DateTime bookedDate, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            var result = DbActionWithTelemetry((db) => db.Execute("UpdateTransactionBooking", new { id, transId, bookedDate }, commandType: CommandType.StoredProcedure), "UpdateTransactionBooking", message);

            return MapDbExecuteResult(result);
        }

        /// <inheritdoc/>
        public Result<FuelTransaction> FetchTransaction(uint maxTransactionNumber, long? transId = null, string txnNumber = null, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            var result = DbActionWithTelemetry((db) => db.Query<FuelTransaction>("GetTransaction", new { transId, txnNumber, maxTransactionNumber = (int)maxTransactionNumber }, commandType: CommandType.StoredProcedure), "GetPendingTransactionBookings", message);

            return result.IsSuccess ? Result.Success(result.Value.FirstOrDefault()) : Result.Failure<FuelTransaction>(result.Error);
        }

        /// <inheritdoc/>
        public Result<StatusCodeResult> FetchTransactionHttp(uint maxTransactionNumber, long? transId = null, string txnNumber = null, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            var result = DbActionWithTelemetry((db) => db.Query<FuelTransaction>("GetTransaction", new { transId, txnNumber, maxTransactionNumber = (int)maxTransactionNumber }, commandType: CommandType.StoredProcedure), "GetPendingTransactionBookings", message);

            return MapDbQueryResult<FuelTransaction>(result);
        }

        private static Result<StatusCodeResult> MapDbQueryResult<T>(Result<IEnumerable<T>> result) =>
            !result.IsSuccess? Result.Success(StatusCodeResult.Specific(HttpStatusCode.BadRequest)) :
                           result.Value.Any()? Result.Success(StatusCodeResult<T>.Success(result.Value.FirstOrDefault()) as StatusCodeResult) :
                           Result.Success(StatusCodeResult.Specific(HttpStatusCode.BadRequest));

        private static Result<StatusCodeResult> MapDbExecuteResult(Result<int> result, int expectedRowCount = 1) =>
          !result.IsSuccess ? Result.Success(StatusCodeResult.Specific(HttpStatusCode.BadRequest)) : 
              result.Value == expectedRowCount ? Result.Success(StatusCodeResult.Success) :
              Result.Success(StatusCodeResult.Specific(HttpStatusCode.BadRequest)); 

        private void InvalidateCacheForBosConfigMapping()
        {
            _cacheHelper.ForceExpirationOnCachedItem($"{ConfigurationConstants.ConfigKeySuffixHydraDb}{ConfigurationConstants.CategoryNameBOS}:", ConfigurationConstants.CachedItemHydraToExternalConfigDataMap);
        }

        /// <inheritdoc/>
        public Result<Core.HydraDb.Models.PumpDelivered> GetDeliveredInfo(byte pump, string reference = null)
        {
            var result = GetDelivered(pump, reference.ToMessageTracking());
            if (!result.IsSuccess) 
            {
                return Result.Failure<Core.HydraDb.Models.PumpDelivered>(result.Error);
            }

            var from = result.Value;
            var to = new Core.HydraDb.Models.PumpDelivered(from.Number, from.HasOptPayment, from.HasDelivered, from.Grade, from.Hose, from.Volume, from.Amount, from.Name, from.Price, from.NetAmount, from.VatAmount, from.VatRate, from.TransSeqNum);
            return Result.Success(to);
        }

        /// <inheritdoc/>
        public Result<IEnumerable<PumpGradePriceInfo>> GetPumpGradePriceInfos(IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            return DbActionWithTelemetry((db) => db.Query<PumpGradePriceInfo>("GetPumpGradePriceInfo", null, commandType: CommandType.StoredProcedure), "GetPumpGradePriceInfo", message);
        }

        /// <inheritdoc/>
        public Result UpsertPumpGradePriceInfo(byte pump, byte grade, byte hose, ushort price, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            return DbActionWithTelemetry((db) => db.Execute("UpsertPumpGradePriceInfo", new { pump, grade, hose, price = (int)price}, commandType: CommandType.StoredProcedure), "UpsertPumpGradePriceInfo", message);
        }
    }
}
