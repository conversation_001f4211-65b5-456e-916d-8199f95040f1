﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net48</TargetFramework>
		<IsPackable>false</IsPackable>
		<Platforms>AnyCPU;x64</Platforms>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Htec.Common.Abstractions" Version="4.0.0" />
		<PackageReference Include="Htec.Logger.Interfaces" Version="5.0.0" />
		<PackageReference Include="Htec.Testing.Helpers" Version="3.1.0" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.extensibility.core" Version="2.9.3" />
		<PackageReference Include="xunit.runner.console" Version="2.9.3">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="xunit.runner.visualstudio" Version="3.1.3">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Forecourt.Pump\Forecourt.Pump.csproj" />
		<ProjectReference Include="..\Forecourt.SecondaryAuth\Forecourt.SecondaryAuth.csproj" />
	</ItemGroup>

</Project>
