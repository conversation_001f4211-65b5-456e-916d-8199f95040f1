﻿using Newtonsoft.Json;

namespace Forecourt.BackOffice.Models.EvoBook
{
    /// <summary>
    /// Represents tax calculation details for a transaction line item in EvoBook transactions
    /// </summary>
    public class TaxLine
    {
        /// <summary>
        /// Sequential number identifying this tax line within the transaction
        /// </summary>
        [JsonProperty("lineItemSequenceNumber")]
        public int LineItemSequenceNumber { get; set; }
        /// <summary>
        /// Indicates whether this tax line has been voided
        /// </summary>
        [JsonProperty("voidedFlag")]
        public bool VoidedFlag { get; set; }
        /// <summary>
        /// Timestamp when the tax calculation started
        /// </summary>
        [JsonProperty("startTime")]
        public string StartTime { get; set; }
        /// <summary>
        /// Timestamp when the tax calculation completed
        /// </summary>
        [JsonProperty("endTime")]
        public string EndTime { get; set; }
        /// <summary>
        /// Indicates whether this tax line requires fiscal receipt printing
        /// </summary>
        [JsonProperty("fiscalReceipt")]
        public bool FiscalReceipt { get; set; }
        /// <summary>
        /// Unique identifier for the tax authority governing this tax
        /// </summary>
        [JsonProperty("taxAuthorityId")]
        public int TaxAuthorityId { get; set; }
        /// <summary>
        /// Identifier for the tax group classification
        /// </summary>
        [JsonProperty("taxGroupId")]
        public string TaxGroupId { get; set; }
        /// <summary>
        /// Descriptive name of the tax group
        /// </summary>
        [JsonProperty("taxGroupName")]
        public string TaxGroupName { get; set; }
        /// <summary>
        /// Calculated tax amount for this line
        /// </summary>
        [JsonProperty("taxAmount")]
        public double TaxAmount { get; set; }
        /// <summary>
        /// Base amount subject to taxation
        /// </summary>
        [JsonProperty("taxableAmount")]
        public double TaxableAmount { get; set; }
        /// <summary>
        /// Tax percentage rate applied to the taxable amount
        /// </summary>
        [JsonProperty("taxPercent")]
        public double TaxPercent { get; set; }
        /// <summary>
        /// Indicates whether this tax amount is reclaimable
        /// </summary>
        [JsonProperty("taxReclaimable")]
        public bool TaxReclaimable { get; set; }
    }
}
