﻿using Newtonsoft.Json;

namespace Forecourt.BackOffice.Models.EvoBook
{
    /// <summary>
    /// Unique transaction identifier containing company, store, workstation, and sequence information for EvoBook transactions
    /// </summary>
    public class TransactionKey
    {
        /// <summary>
        /// Unique identifier for the company
        /// </summary>
        [JsonProperty("companyId")] 
        public int CompanyId { get; set; }
        /// <summary>
        /// Unique identifier for the store location
        /// </summary>
        [JsonProperty("storeId")] 
        public int StoreId { get; set; }
        /// <summary>
        /// Unique identifier for the workstation or terminal
        /// </summary>
        [JsonProperty("workstationId")] 
        public int WorkstationId { get; set; }
        /// <summary>
        /// Business day date for the transaction
        /// </summary>
        [JsonProperty("businessDay")] 
        public string BusinessDay { get; set; }
        /// <summary>
        /// Sequential transaction number within the business day
        /// </summary>
        [<PERSON><PERSON><PERSON>roperty("transactionSequenceNumber")] 
        public int TransactionSequenceNumber { get; set; }
    }
}
