﻿using Htec.Foundation.Connections.Models.Interfaces;
using bosIntf = Htec.Hydra.Core.Bos.Interfaces;

namespace Forecourt.Bos.TransactionFiles.Interfaces
{
    /// <summary>
    /// Hydra version of <see cref="bosIntf.IBosTransactionFileWriter{TMessageTracking, TFileItem, TLocalAccountItem, TShiftEndItem, TShiftEndOptions, TSalesItem, TCardAmountItem, TCardSalesItem, TCategorySalesItem, TCardVolumeSalesItem}"/>, tied to Hydra messages
    /// </summary>

    public interface IHydraTransactionFile : bosIntf.Hydra.ITransactionFile<IMessageTracking>
    {
    }   
}