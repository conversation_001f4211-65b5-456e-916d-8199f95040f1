﻿using CSharpFunctionalExtensions;
using Forecourt.Common.Factories.Interfaces;
using Forecourt.Common.Helpers.Interfaces;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Enums;
using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Forecourt.Pump.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Attributes;
using Htec.Foundation.Configuration;
using Htec.Foundation.Connections.Core;
using Htec.Foundation.Connections.Core.Interfaces;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Foundation.Workers.Interfaces;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Messages.Opt.RequestResponse.Notifications;
using Htec.Hydra.Messages.Opt.Xsd;
using Htec.Logger.Interfaces.Tracing;
using Microsoft.AspNet.SignalR;
using Newtonsoft.Json;
using OPT.Common;
using OPT.Common.Helpers.Interfaces;
using OPT.Common.HydraDbClasses;
using OPT.Common.Models;
using OPT.Common.Workers.Interfaces;
using OPTService.Hubs;
using OPTService.OPTServiceClasses;
using System;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.IO.Ports;
using System.Linq;
using System.Net;
using static OPTService.OPTServiceClasses.AdvancedConfigDetails;
using OptDeviceStatus = Htec.Hydra.Messages.Opt.Models.DeviceState;
using VersionInfo = OPTService.OPTServiceClasses.VersionInfo;

namespace OPTService
{
    [HasConfiguration()]
    public class Web : Connectable, IWeb
    {
        public IList<long> ReceiptTrans => _core.ReceiptTrans;       
        public bool IsConfigBatch => _core.ControllerWorker.IsConfigBatch;

        private readonly IServiceFilesHelper _serviceFilesHelper;

        public IServiceFilesHelper ServiceFilesHelper => _serviceFilesHelper;

        private ICore _core;
        private IIntegratorFactories _integratorFactories;
        private readonly IHubContext _hubContext;
        private readonly IFileVersionInfoHelper _fileVersionInfo;
        private readonly IFileSystem _fileSystem;
        private readonly IHydraDb _hydraDb;
        private IControllerWorker _controllerWorker => GetWorker<IControllerWorker>();

        private bool _showDomsFetchedSetup = false;
        private bool _showDomsPreparedSetup = false;
        private bool _showDomsTcpSetup = true;

        private const int ReceiptLineMaxLength = 30;
        private const string ReceiptLineSeparator = "\n";

        public Web(ICore core, IHtecLogger logger, IConfigurationManager configurationManager, IIntegratorFactories integratorFactories, 
            IFileVersionInfoHelper fileVersionInfoHelper, IFileSystem fileSystem, IServiceFilesHelper serviceFilesHelper, IHydraDb hydraDb,
            IControllerWorker controllerWorker, IToOptWorker toOptWorker) : base(logger, configurationManager)
        {
            _core = core ?? throw new ArgumentNullException(nameof(core));
            _integratorFactories = integratorFactories ?? throw new ArgumentNullException(nameof(integratorFactories));
            _fileVersionInfo = fileVersionInfoHelper ?? throw new ArgumentNullException(nameof(fileVersionInfoHelper));
            _fileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));
            _serviceFilesHelper = serviceFilesHelper ?? throw new ArgumentNullException(nameof(serviceFilesHelper));
            _hydraDb = hydraDb ?? throw new ArgumentNullException(nameof(hydraDb));
            RegisterWorker(controllerWorker ?? throw new ArgumentNullException(nameof(controllerWorker)));

            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            _controllerWorker.PushChangeEvent += ControllerWorkerOnPushChangeEvent;
            _hubContext = GlobalHost.ConnectionManager.GetHubContext<WebHub>();

            RegisterWorker(toOptWorker ?? throw new ArgumentException(nameof(toOptWorker)));
        }

        /// <summary>
        /// SignalR marker/header constant
        /// </summary>
        public const string HeaderSignalR = ConfigConstants.HeaderSignalR;

        private readonly object _pushChangeSyncObject = new object();
        private Queue<PushChangeEventInfo> _pushChangeQueue;

        public class PushChangeEventInfo
        {
            public PushChangeEventInfo(EventType eventType, string itemId = null, string additionalData = null)
            {
                EventType = eventType;
                ItemId = itemId;
                AdditionalData = additionalData;
            }

            public EventType EventType { get; }
            public string ItemId { get; }
            public string AdditionalData { get; }
        }

        /// <summary>
        /// The handler for pushChange event
        /// </summary>
        /// <param name="eventType">The event type</param>
        /// <param name="itemId">The ID of the item changed</param>
        /// <param name="additionalData">The ID of the item changed</param>
        public void ControllerWorkerOnPushChangeEvent(EventType eventType, string itemId = null, string additionalData = null)
        {
            lock (_pushChangeSyncObject)
            {
                if (_pushChangeQueue == null)
                {
                    _pushChangeQueue = new Queue<PushChangeEventInfo>();
                    DoBackgroundTask(ProcessPushChangeEventsQueue, cycleTimeSpan: null);
                }

                _pushChangeQueue.Enqueue(new PushChangeEventInfo(eventType, itemId, additionalData));
            }
        }

        private Result ProcessPushChangeEventsQueue()
        {
            var i = 0;
            var iMax = BackgroundTaskMaxActionExecuteCount.GetValue();
            while (i < iMax)
            {
                PushChangeEventInfo info;
                lock (_pushChangeSyncObject)
                {
                    if  (!_pushChangeQueue.Any())
                    {
                        break;
                    }

                    info = _pushChangeQueue.Dequeue();
                }

                ProcessPushChangeEvent(info);

                i++;
            }

            return Result.Success();
        }

        private void ProcessPushChangeEvent(PushChangeEventInfo info)
        {
            var eventType = info.EventType;
            var itemId = info.ItemId;
            var additionalData = info.AdditionalData;

            DoDeferredLogging(LogLevel.Debug, HeaderSignalR, () => new[] { $"Pushing changes with eventType={eventType} and itemId={itemId}" });
            _hubContext.Clients.All.pushChange(eventType, itemId, additionalData);
        }

        public StatusCodeResult<IEnumerable<OptDetails>> GetOpts(string idString = null, string reference = null)
        {
            var result = DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Debug, HeaderParameters, () => new[] { $"idString: {idString}" }, reference: reference);

                return Result.Success(Opts(idString).AsEnumerable());

            }, reference);

            var results = result.Value;
            results = results.OrderBy(x=> !x.Pumps.Any() ? byte.MaxValue : x.Pumps.Select(x => x.Number).OrderBy(x => x).FirstOrDefault());

            return !results.Any() && !string.IsNullOrEmpty(idString)
                ? StatusCodeResult<IEnumerable<OptDetails>>.Specific(HttpStatusCode.NotFound)
                : StatusCodeResult<IEnumerable<OptDetails>>.Success(results);
        }

        private PumpDetails CreatePump(IOpt opt, IPump pump)
        {
            var pumpModeType = Forecourt.Core.Pump.Enums.PumpModeType.KioskUse;
            var pumpDetails = new PumpDetails()
            {
                Number = pump.Number,
                Tid = pump.Tid,
                Closed = pump.PumpIsClosed,
                OptStringId = opt?.IdString,
                InUse = pump.InUse,
                SecAuthState = $"{pump.SecAuthState}",
                HasSecAuthRequestTimedOut = pump.SecAuthState == Htec.Hydra.Core.SecAuth.Enums.SecAuthState.Requested && pump.HasSecAuthRequestTimedOut,
                Delivering = pump.IsDelivering,
                Delivered = pump.IsDelivered,
                NozzleUp = pump.IsNozzleUp,
                HasPayment = pump.HasPayment,
                ThirdPartyPending = pump.ThirdPartyWait,
                PodMode = opt?.IsInPodMode ?? false,
                KioskOnly = pump.KioskUse,
                OutsideOnly = pump.OutsideOnly,
                DefaultKioskOnly = pump.DefaultKioskUse,
                DefaultOutsideOnly = pump.DefaultOutsideOnly,
                MaxFillOverrideForFuelCards = pump.MaxFillOverrideForFuelCards,
                MaxFillOverrideForPaymentCards = pump.MaxFillOverrideForPaymentCards,
                IsMixedMode = pump.Mixed,
                IsKioskUse = pump.PumpMode == pumpModeType,
                IsMobile = pump.PumpMode == pumpModeType && pump.IsMobile
            };

            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderParameters, () => new[] { $"GetHashCode: {pump.GetHashCode()}; Details: {JsonConvert.SerializeObject(pumpDetails)}" });

            return pumpDetails;
        }

        /// <summary>
        /// Gets all the Opts and filter the list if required
        /// </summary>
        /// <param name="idString">The target Opt StringId to filter the list to</param>
        /// <returns>a list of OptDetails</returns>
        public IList<OptDetails> Opts(string idString = null)
        {
            IList<OptDetails> values = new List<OptDetails>();
            foreach (IOpt opt in _core.AllOpts.AllOpts.Where(x => idString == null || x.IdString.Equals(idString)))
            {
                var deviceStatus = opt.Connected ? opt.DeviceStatus : OptDeviceStatus.InitialisingServiceConnections;
                var optDetails = new OptDetails
                {
                    StringId = opt.IdString,
                    Connected = opt.Connected,
                    SignedIn = opt.SignedIn,
                    PrinterError = opt.PrinterError,
                    PaperLow = opt.PaperLow,
                    InUse = opt.InUse,
                    SoftwareVersion = opt.SoftwareVersion,
                    AvailableSoftware = new List<string>(),
                    AvailableSecureAssets = new List<string>(),
                    AvailableCpatAssets = new List<string>(),
                    Pumps = new List<PumpDetails>(),
                    IpAddress = _core.GetOptIpAddressString(opt),
                    Subnet = opt.Subnet,
                    Gateway = opt.Gateway,
                    Dns1 = opt.Dns1,
                    Dns2 = opt.Dns2,
                    ReceiptHeaders = GetReceiptLines(opt.ReceiptHeader),
                    ReceiptFooters = GetReceiptLines(opt.ReceiptFooter),
                    PlaylistFileName = opt.PlaylistFileName,
                    MediaChannel = _core.MediaChannel,
                    SecureAssetsVersion = opt.SecureAssetsVersion,
                    MultimediaAssetsVersion = opt.MultimediaAssetsVersion,
                    CpatAssetsVersion = opt.CpatAssetsVersion,
                    OptFirmwareVersion = opt.OptFirmwareVersion,
                    EmvKernelVersion = opt.EmvKernelVersion,
                    PluginType = opt.PluginType,
                    PluginVersion = opt.PluginVersion,
                    ModeChangePending = opt.ModeChangePending,
                    ConfigChangePending = opt.ConfigChangePending,
                    LogFileRequestSent = opt.LogFileRequestSent,
                    DeviceStatus = $"{deviceStatus}",
                    DeviceStatusCode = $"{((int)deviceStatus).ToString().PadLeft(4, '0')}"
                };

                foreach (byte number in opt.PumpList().OrderBy(x => x))
                {
                    if (_core.AllPumps.TryGetPump(number, out IPump pump))
                    {
                        optDetails.Pumps.Add(CreatePump(opt, pump));
                    }
                }

                foreach (string available in _core.AvailableSoftware)
                {
                    if (!available.Equals(optDetails.SoftwareVersion))
                    {
                        optDetails.AvailableSoftware.Add(available);
                    }
                }

                foreach (string available in _core.AvailableSecureAssets)
                {
                    if (!available.Equals(optDetails.SecureAssetsVersion))
                    {
                        optDetails.AvailableSecureAssets.Add(available);
                    }
                }

                foreach (string available in _core.AvailableCpatAssets)
                {
                    if (!available.Equals(optDetails.CpatAssetsVersion))
                    {
                        optDetails.AvailableCpatAssets.Add(available);
                    }
                }

                optDetails.Status = opt.GetModeStatusDescription();

                if (optDetails.Status != null || optDetails.Connected || _core.AllOpts.GlobalOptStringId.Equals(idString))
                {
                    values.Add(optDetails);
                }
            }

            return values;
        }

        public StatusCodeResult<IEnumerable<PumpDetails>> GetPumps(int pumpNumber = 0, string reference = null)
        {
            var result = DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Debug, HeaderParameters, () => new[] { $"pumpNumber: {pumpNumber}" }, reference: reference);

                if (pumpNumber < 0 || pumpNumber > 255)
                {
                    DoDeferredLogging(LogLevel.Debug, HeaderException, () => new[] { $"Validation error: Invalid value for pumpNumber: {pumpNumber}" }, reference: reference);

                    return Result.Failure<IEnumerable<PumpDetails>>("Invalid Pump number");
                }

                return Result.Success(Pumps(pumpNumber).AsEnumerable());
            }, reference);

            if (!result.IsSuccess)
            {
                return StatusCodeResult<IEnumerable<PumpDetails>>.Specific(HttpStatusCode.BadRequest, null, new ArgumentException("Invalid Pump number"));
            }

            var results = result.Value;

            return !results.Any() && pumpNumber > 0 
                ? StatusCodeResult<IEnumerable<PumpDetails>>.Specific(HttpStatusCode.NotFound) 
                : StatusCodeResult<IEnumerable<PumpDetails>>.Success(results);
        }

        public IList<PumpDetails> Pumps(int pumpNumber = 0)
        {
            return _core.AllPumps.AllPumps
                .Where(x => pumpNumber <= 0 || x.Number.Equals((byte)pumpNumber))
                .Select(x => CreatePump(x.Opt, x)).OrderBy(x => x.Number).ToList();
        }

        /// <inheritdoc />
        public ConnectionDetails ConnectionsDetails()
        {
            List<string> GetIps<T>(IMessageBroker mb) where T : IWorkerable
                => ((mb.GetWorker<T>() as IConnectable)?.GetAllIpAddresses().Select(x => x.ToString()) ?? Enumerable.Empty<string>()).Distinct().ToList();

            var connections = _controllerWorker.GetConnections();
            var endpoints = _controllerWorker.GetEndpoints();
            var broker = _controllerWorker.GetWorker<IMessageBroker>();

            return new ConnectionDetails
            {
                ToOptPort = endpoints.ToOpt.Port,
                FromOptPort = endpoints.FromOpt.Port,
                HeartbeatPort = endpoints.Heartbeat.Port,
                HydraPosPort = endpoints.HydraPos.Port,
                RetalixPosPort = endpoints.RetalixPos.Port,
                ThirdPartyPosPort = endpoints.ThirdPartyPos.Port,
                MediaChannelPort = endpoints.MediaChannel.Port,
                AnprIpAddress = endpoints.Anpr.Address.ToString(),
                AnprPort = endpoints.Anpr.Port,
                CarWashIpAddress = endpoints.CarWash.Address.ToString(),
                CarWashPort = endpoints.CarWash.Port,
                PumpControllerIpAddress = endpoints.Pump.Address.ToString(),
                PumpControllerPort = endpoints.Pump.Port,
                PumpControllerLogonInfo = _controllerWorker.GetWorker<IPumpWorker>()?.LogonInfo,
                TankGaugeIpAddress = endpoints.TankGauge?.Address.ToString(),
                TankGaugePort = endpoints.TankGauge?.Port ?? 0,
                HydraMobileIpAddress = endpoints.HydraMobile.Address.ToString(),
                HydraMobilePort = endpoints.HydraMobile.Port,
                OptConnectedCount = connections.AllOpt,
                HydraPosConnectedCount = connections.HydraPos,
                RetalixPosConnectedCount = connections.RetalixPos,
                ThirdPartyPosConnectedCount = connections.ThirdPartyPos,
                MediaChannelConnectedCount = connections.MediaChannel,
                IsSecAuthConnected = connections.SecAuth,
                CarWashConnected = connections.CarWash,
                TankGaugeConnected = connections.TankGauge,
                HydraMobileConnected = connections.HydraMobile,
                PaymentConfigConnected = connections.PaymentConfiguration,
                PumpControllerConnected = connections.SiteController,
                AutoAuth = _core.AutoAuth,
                MediaChannel = _core.MediaChannel,
                UnmannedPseudoPos = _core.UnmannedPseudoPos,
                RetalixDefined = _core.RetalixTransactionFileDirectory != null,
                OptIpAddresses = GetIps<IFromOptWorker>(broker),
                HydraPosIpAddresses = GetIps<IHydraPosWorker>(broker),
                RetalixPosIpAddresses = GetIps<IRetalixPosWorker>(broker),
                ThirdPartyPosIpAddresses = GetIps<IThirdPartyPosWorker>(broker),
                MediaChannelIpAddresses = GetIps<IMediaChannelWorker>(broker),
                RetalixPosPrimaryIpAddress = _core.RetalixPosPrimaryIpAddress?.ToString(),
                SignalRPosConnectedCount = connections.SignalRPos,
                SignalRPosIpAddresses = GetIps<ISignalRPosOutWorker>(broker),
                SignalRSecAuthConnectedCount = connections.SignalRSecAuth,
                SignalRSecAuthIpAddresses = GetIps<ISecAuthIntegratorOutTransient<IMessageTracking>>(broker),
                SignalRPosInConnectedCount = connections.SignalRPosIn,
                SignalRPosInIpAddresses = GetIps<ISignalRPosInModeWorker>(broker),
                SignalRBosConnectedCount = connections.SignalRBos,
                SignalRBosIpAddresses = GetIps<IBosIntegratorOut<IMessageTracking>>(broker),
            };
        }   

        public IList<string> Tids()
        {
            IList<string> values = new List<string>();
            foreach (TermId tid in _core.FetchedTids)
            {
                values.Add(tid.Tid);
            }

            return values;
        }

        private Endpoints GetEndpoints()
        {
            GetLogger().Debug("Fetching Endpoints");
            return _core.ControllerWorker.GetEndpoints();
        }

        private static string[] GetReceiptLines(string receiptLine)
        {
            return receiptLine?.Split(new[] { ReceiptLineSeparator }, StringSplitOptions.None) ?? Array.Empty<string>();
        }

        private static string GetReceiptLine(string[] receiptLines)
        {
            return receiptLines != null && receiptLines.Length > 0 ? string.Join(ReceiptLineSeparator, receiptLines.Select(x => x?.Trim())) : null;
        }

        private static bool AreValidReceiptLines(string[] receiptLines)
        {
            return receiptLines == null || receiptLines.All(x => x == null || x.Length <= ReceiptLineMaxLength);
        }

        public StatusCodeResult<GenericOptConfigDetails> GetGenericOptConfig(string reference = null)
        {
            var result = DoAction<GenericOptConfigDetails>(() => { return Result.Success(GetGenericOptConfig()); }, reference);

            return result.IsSuccess
                ? StatusCodeResult<GenericOptConfigDetails>.Success(result.Value)
                : StatusCodeResult<GenericOptConfigDetails>.Specific(HttpStatusCode.InternalServerError, null, new Exception(result.Error));
        }

        public GenericOptConfigDetails GetGenericOptConfig()
        {
            GetLogger().Debug("Fetching Generic OPT Config");
            var genOptCfg = _core.GenericOptConfig;

            if (genOptCfg == null)
            {
                return new GenericOptConfigDetails();
            }

            var gradeNames = _core.GradeNames;

            var details = new GenericOptConfigDetails
            {
                ServiceAddress = genOptCfg.ServiceAddress,
                EsocketEndPoints = genOptCfg.ESocketEndPoints
                    .Select(x => new EndPointDetails {IpAddress = x.IpAddress, Port = x.Port}).ToList(),
                Timestamp = genOptCfg.TermProcCategory?.Timestamp ?? string.Empty,
                TermCategory = genOptCfg.TermProcCategory?.TermCategory ?? string.Empty,
                ProcCategory = genOptCfg.TermProcCategory?.ProcCategory ?? string.Empty,
                CardAids = genOptCfg.CardAids.Select(x => new CardAidDetails
                {
                    Aid = x.Aid,
                    AppVerTerm = x.AppVerTerm,
                    TacDefault = x.TacDefault,
                    TacDenial = x.TacDenial,
                    TacOnline = x.TacOnline,
                    PartialMatch = x.PartialMatch,
                    Tdol = x.Tdol,
                    Ddol = x.Ddol,
                    FloorLimit = x.FloorLimit,
                    EmvTarget = x.EmvTarget,
                    EmvMaxTarget = x.EmvMaxTarget,
                    EmvThreshold = x.EmvThreshold
                }).ToList(),
                CardClessAids = genOptCfg.CardClessAids.Select(x => new CardClessAidDetails
                {
                    Aid = x.Aid,
                    AppVerTerm = x.AppVerTerm,
                    TransLimit = x.TransLimit,
                    FloorLimit = x.FloorLimit,
                    CvmLimit = x.CvmLimit,
                    OdcvmLimit = x.OdcvmLimit,
                    TermAddCapabilities = x.TermAddCapabilities,
                    TermCapabilitiesCvm = x.TermCapabilitiesCvm,
                    TermCapabilitiesNoCvm = x.TermCapabilitiesNoCvm,
                    TermRiskData = x.TermRiskData,
                    Udol = x.Udol,
                    TacDefault = x.TacDefault,
                    TacDenial = x.TacDenial,
                    TacOnline = x.TacOnline,
                    TerminalType = x.TerminalType,
                    MerchantCategoryCode = x.MerchantCategoryCode,
                    MaxTorn = x.MaxTorn,
                    MaxTornLife = x.MaxTornLife,
                    MchipCvmCapAboveLimit = x.MchipCvmCapAboveLimit,
                    MchipCvmCapBelowLimit = x.MchipCvmCapBelowLimit,
                    MstripeCvmCapAboveLimit = x.MstripeCvmCapAboveLimit,
                    MstripeCvmCapBelowLimit = x.MstripeCvmCapBelowLimit,
                    MagStripeAppVer = x.MagStripeAppVer,
                    ExpressPayReaderCapabilities = x.ExpressPayReaderCapabilities,
                    ExpressPayEnhancedReaderCapabilities = x.ExpressPayEnhancedReaderCapabilities,
                    ExpressPayKernelVersion = x.ExpressPayKernelVersion
                }).ToList(),
                CardClessDrls = genOptCfg.CardClessDrls.Select(x => new CardClessDrlDetails
                {
                    Aid = x.Aid,
                    ProgramId = x.ProgramId,
                    TransLimit = x.TransLimit,
                    FloorLimit = x.FloorLimit,
                    CvmLimit = x.CvmLimit
                }).ToList(),
                CardCapks = genOptCfg.CardCapks.Select(x => new CardCapkDetails
                {
                    Rid = x.Rid,
                    TheIndex = x.TheIndex,
                    Modulus = x.Modulus,
                    Exponent = x.Exponent,
                    Checksum = x.Checksum,
                    ExpiryDate = x.ExpiryDate
                }).ToList(),
                FuelCards = genOptCfg.FuelCards.Select(x => new FuelCardDetails
                {
                    IinStart = x.IinStart,
                    IinEnd = x.IinEnd,
                    OnlinePin = x.OnlinePin
                }).ToList(),
                ContactlessDetails = CreateContactlessDetails(genOptCfg.ContactlessConfiguration),
                TariffMappings = genOptCfg.TariffMappings.Select(x => new TariffMappingDetails
                {
                    Grade = x.Grade,
                    ProductCode = x.ProductCode,
                    FuelCardsOnly = x.FuelCardsOnly,
                    GradeName = gradeNames.FirstOrDefault(y => y.Grade == x.Grade)?.Name ?? string.Empty
                }).ToList(),
                Washes = genOptCfg.Washes.Where(x => float.TryParse(x.Price, out _) && float.TryParse(x.VatRate, out _))
                    .Select(x => new WashDetails
                    {
                        ProgramId = x.ProgramId,
                        ProductCode = x.ProductCode,
                        Description = x.Description,
                        Price = float.Parse(x.Price),
                        VatRate = float.Parse(x.VatRate),
                        Category = x.Category,
                        Subcategory = x.Subcategory
                    }).ToList(),
                PredefinedAmounts = genOptCfg.PredefinedAmounts.Select(x => new PredefinedAmountDetails {Amount = x}).ToList(),
                Loyalty = new List<LoyaltyDetails>(),
                ReceiptLayoutMode = new ReceiptLayoutModeDetails
                {
                    Mode = genOptCfg.ReceiptLayoutMode,
                    ReceiptReprintAvailability = _core.ReceiptTimeout,
                    ReceiptMaxCount = _core.ReceiptMaxCount,
                    ReceiptHeaders = GetReceiptLines(_core.AllOpts.GetOptForIdString(_core.AllOpts.GlobalOptStringId)?.ReceiptHeader),
                    ReceiptFooters = GetReceiptLines(_core.AllOpts.GetOptForIdString(_core.AllOpts.GlobalOptStringId)?.ReceiptFooter),
                },
                DiscountCards = genOptCfg.DiscountCards.Select(x => new DiscountCardDetails
                {
                    Iin = x.Iin,
                    Name = x.Name,
                    Type = x.Type,
                    Value = x.Value,
                    Grade = x.Grade,
                    Whitelist = new List<string>(x.Whitelist)
                }).ToList(),
            };

            foreach (string name in GenericOptConfig.NameList)
            {
                if (_core.IsLoyaltyAvailable(name))
                {
                    genOptCfg.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
                    LoyaltyDetails loyaltyDetails = new LoyaltyDetails
                    {
                        Name = name,
                        Present = _core.IsLoyaltyPresent(name),
                        SiteId = loyalty?.Terminal?.SiteId ?? string.Empty,
                        TerminalId = loyalty?.Terminal?.TerminalId ?? string.Empty,
                        Footer1 = loyalty?.Terminal?.Footer1 ?? string.Empty,
                        Footer2 = loyalty?.Terminal?.Footer2 ?? string.Empty,
                        Timeout = loyalty?.Terminal?.Timeout ?? 0,
                        ApiKey = loyalty?.Terminal?.ApiKey ?? string.Empty,
                        HttpHeader = loyalty?.Terminal?.HttpHeader ?? string.Empty,
                        Hosts = loyalty?.Hosts?.Select(x => new LoyaltyEndPointDetails
                        {
                            Name = name,
                            IpAddress = x.IpAddress,
                            Port = x.Port
                        }).ToList() ?? new List<LoyaltyEndPointDetails>(),
                        Hostnames = loyalty?.Hostnames?.Select(x => new LoyaltyEndPointDetails { Name = name, Hostname = x }).ToList() ??
                                    new List<LoyaltyEndPointDetails>(),
                        Iins = loyalty?.Iins?.Select(x => new LoyaltyIinDetails
                        {
                            Name = name,
                            Low = x.Low,
                            High = x.High
                        }).ToList() ?? new List<LoyaltyIinDetails>(),
                        LoyaltyMappings = loyalty?.TariffMappings?.Select(x => new LoyaltyMappingDetails
                        {
                            Name = name,
                            ProductCode = x.ProductCode,
                            LoyaltyCode = x.LoyaltyCode
                        }).ToList() ?? new List<LoyaltyMappingDetails>()
                    };


                    details.Loyalty.Add(loyaltyDetails);
                }
            }

            return details;
        }

        private static ContactlessDetails CreateContactlessDetails(ContactlessConfiguration contactlessConfiguration)
        {
            return new ContactlessDetails
            {
                IsEnabled = contactlessConfiguration.HasContactless,
                CardPreAuthLimit = contactlessConfiguration.ContactlessCardPreAuthLimit,
                DevicePreAuthLimit = contactlessConfiguration.ContactlessDevicePreAuthLimit,
                Ttq = contactlessConfiguration.Ttq,
                ShowSingleButton = contactlessConfiguration.SingleContactlessButton
            };
        }

        public IList<LocalAccountCustomerDetails> GetLocalAccountCustomers()
        {
            var activeCustomers = _core.LocalAccountCustomers.Where(x => x.CustomerExists);
            return activeCustomers.Select(x => new LocalAccountCustomerDetails
            {
                CustomerReference = x.CustomerReference,
                Name = x.Name,
                TransactionsAllowed = x.TransactionsAllowed,
                TransactionLimit = x.TransactionLimit,
                Pin = x.Pin,
                PrintValue = x.PrintValue,
                AllowLoyalty = x.AllowLoyalty,
                FuelOnly = x.FuelOnly,
                RegistrationEntry = x.RegistrationEntry,
                MileageEntry = x.MileageEntry,
                PrePayAccount = x.PrePayAccount,
                LowCreditWarning = x.LowCreditWarning,
                MaxCreditReached = x.MaxCreditReached,
                Balance = (decimal)x.Balance / 100,
                Cards = x.Cards.Select(y => new LocalAccountCardDetails
                {
                    Pan = y.Pan,
                    Description = y.Description,
                    Discount = y.Discount,
                    NoRestrictions = y.NoRestrictions,
                    Unleaded = y.Unleaded,
                    Diesel = y.Diesel,
                    Lpg = y.Lpg,
                    Lrp = y.Lrp,
                    GasOil = y.GasOil,
                    AdBlue = y.AdBlue,
                    Kerosene = y.Kerosene,
                    Oil = y.Oil,
                    Avgas = y.Avgas,
                    Jet = y.Jet,
                    Mogas = y.Mogas,
                    Valeting = y.Valeting,
                    OtherMotorRelatedGoods = y.OtherMotorRelatedGoods,
                    ShopGoods = y.ShopGoods,
                    Hot = y.Hot,
                    CustomerReference = x.CustomerReference,
                    Restrictions1 = y.Restrictions1,
                    Restrictions2 = y.Restrictions2
                }).OrderBy(y => y.Pan).ToList()
            }).OrderBy(x => x.CustomerReference).ToList();
        }

        public AdvancedConfigDetails GetAdvancedConfig()
        {
            var categories = GetCategoriesConfiguration();

            categories = categories
                .Where(x => x.IsStandardEditable && x.Settings.Any())
                .ToList();

            var advConfig = _core.AdvancedConfig;

            var details = new AdvancedConfigDetails
            {
                AutoAuth = _core.AutoAuth,
                MediaChannel = _core.MediaChannel,
                UnmannedPseudoPos = _core.UnmannedPseudoPos,
                AsdaDayEndReport = _core.AsdaDayEndReport,
                LoyaltyAvailable = GenericOptConfig.NameList.Where(x => _core.IsLoyaltyAvailable(x)).ToList(),
                PaymentTimeoutOpt = _hydraDb.FetchPaymentTimeout(PaymentTimeoutType.Opt),
                PaymentTimeoutPod = _hydraDb.FetchPaymentTimeout(PaymentTimeoutType.Pod),
                PaymentTimeoutMixed = _hydraDb.FetchPaymentTimeout(PaymentTimeoutType.Mixed),
                PaymentTimeoutNozzleDown = _hydraDb.FetchPaymentTimeout(PaymentTimeoutType.NozzleDown),
                TimeoutKiosk = _hydraDb.FetchPaymentTimeout(PaymentTimeoutType.Kiosk),
                TimeoutSecAuth = _hydraDb.FetchPaymentTimeout(PaymentTimeoutType.SecAuth),
                TillNumber = _core.TillNumber,
                FuelCategory = _core.FuelCategory,
                ForwardFuelPriceUpdate = _core.ForwardFuelPriceUpdate,
                FuellingIndefiniteWait = _core.FuellingIndefiniteWait,
                FuellingWaitMinutes = _core.FuellingWaitMinutes,
                FuellingBackoffAuth = _core.FuellingBackoffAuth,
                FuellingBackoffPreAuth = _core.FuellingBackoffPreAuth,
                FuellingBackoffStopStart = _core.FuellingBackoffStopStart,
                FuellingBackoffStopOnly = _core.FuellingBackoffStopOnly,
                PosClaimNumber = _core.PosClaimNumber,
                FilePruneDays = _core.FilePruneDays,
                TransactionPruneDays = _core.TransactionPruneDays,
                ReceiptPruneDays = _core.ReceiptPruneDays,
                NozzleUpForKioskUse = advConfig.NozzleUpForKioskUse,
                UseReplaceNozzleScreen = advConfig.UseReplaceNozzleScreen,
                MaxFillOverride = advConfig.MaxFillOverride,
                SiteType = advConfig.SiteType.ToString(),
                PosType = advConfig.PosType.ToString(),
                PosTypes = _integratorFactories.PosIntegratorOutFactory.GetDiscriminatorValues(LoggingReference).Select(x => new IntegrationTypeSetting(x.Key, x.Value)),
                PumpType = advConfig.PumpType,
                PumpTypes = _integratorFactories.PumpIntegratorInFactory.GetDiscriminatorValues(LoggingReference).Select(x => new IntegrationTypeSetting(x.Key, x.Value)),
                // TODO:
                MobilePaymentType = advConfig.MobilePaymentType,
                MobilePaymentTypes = new List<IntegrationTypeSetting>() { new(ConfigConstants.NoneUpper, ConfigConstants.None), new("HYDRAMOBILE", "HydraMobile") },
                MobilePosType = advConfig.MobilePosType,
                MobilePosTypes = _integratorFactories.MobilePosIntegratorInFactory.GetDiscriminatorValues(LoggingReference).Select(x => new IntegrationTypeSetting(x.Key, x.Value)),
                BosType = advConfig.BosType,
                BosTypes = _integratorFactories.BosIntegratorOutFactory.GetDiscriminatorValues(LoggingReference).Select(x => new IntegrationTypeSetting(x.Key, x.Value)),
                SecAuthType = advConfig.SecAuthType,
                SecAuthTypes = _integratorFactories.SecAuthIntegratorOutFactory.GetDiscriminatorValues(LoggingReference).Select(x => new IntegrationTypeSetting(x.Key, x.Value)),
                PaymentConfigType = advConfig.PaymentConfigType,
                PaymentConfigTypes = _integratorFactories.PaymentConfigIntegratorOutFactory.GetDiscriminatorValues(LoggingReference).Select(x => new IntegrationTypeSetting(x.Key, x.Value)),
                SiteName = advConfig.SiteName,
                VatNumber = advConfig.VatNumber,
                CurrencyCode = advConfig.CurrencyCode,
                LocalAccountsEnabled = advConfig.LocalAccountsEnabled,
                CardReferences = _core.CardReferences.Select(x => new CardReferenceDetails
                {
                    Reference = x.CardRef,
                    Name = x.CardProductName,
                    FuelCard = x.FuelCard,
                    Acquirer = x.AcquirerName,
                    InUse = x.InUse,
                    External = x.CardExternalName
                }).OrderBy(x => x.Name).ToList(),
                ConfigurationCategories = categories.ToList(),
                ESocketPosConfig = new ESocketPosConfigDetails()
            };

            try
            {
                details.ESocketPosConfig = new ESocketPosConfigDetails
                {
                    EsocketConnectionMade = _core.EsocketConnectionMade,
                    EsocketUseConnectionString = _core.EsocketUseConnectionString,
                    EsocketOverrideProperties = _core.EsocketOverrideProperties,
                    EsocketConfigFile = _core.EsocketConfigFile,
                    EsocketOverrideKeystore = _core.EsocketOverrideKeystore,
                    EsocketKeystoreFile = _core.EsocketKeystoreFile,
                    EsocketOverrideUrl = _core.EsocketOverrideUrl,
                    EsocketDbUrl = _core.EsocketDbUrl,
                    EsocketConnectionString = _core.EsocketConnectionString,
                };
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { ex.Message }, ex);
            }
            return details;
        }

        public FileLocations GetFileLocations()
        {
            return new FileLocations
            {
                RetalixTransactionFileDirectory = _core.RetalixTransactionFileDirectory,
                TransactionFileDirectory = _core.TransactionFileDirectory,
                WhitelistDirectory = _core.WhitelistDirectory,
                LayoutDirectory = _core.LayoutDirectory,
                SoftwareDirectory = _core.SoftwareDirectory,
                ContactlessPropertiesFile = _core.ContactlessPropertiesFile,
                FuelDataUpdateFile = _core.ControllerWorker.FuelDataUpdateFile,
                UpgradeFileDirectory = _core.ControllerWorker.UpgradeFileDirectory,
                RollbackFileDirectory = _core.ControllerWorker.RollbackFileDirectory,
                MediaDirectory = _core.MediaDirectory,
                PlaylistDirectory = _core.PlaylistDirectory,
                OptLogFileDirectory = _core.OptLogFileDirectory,
                LogFileDirectory = _core.ControllerWorker.LogFileDirectory,
                TraceFileDirectory = _core.ControllerWorker.TraceFileDirectory,
                JournalFileDirectory = _core.ControllerWorker.JournalFileDirectory,
                ReceivedUpdateDirectory = _core.ReceivedUpdateDirectory,
                DatabaseBackupDirectory = _core.ControllerWorker.DatabaseBackupDirectory,
                EsocketOverrideContactless = _core.EsocketOverrideContactless,
                MediaChannel = _core.MediaChannel
            };
        }

        public IList<GradePrices> GetPrices(byte gradeFilter = 0)
        {
            var values = new List<GradePrices>();
            foreach (var prices in _core.GradePrices
                .Where(x => x.Value.Any())
                .OrderBy(y => y.Key))
            {
                foreach (var grade in prices.Value
                    .Where(z => gradeFilter <= 0 || z.Id.Equals(gradeFilter))
                    .OrderBy(x => x.Id))
                {
                    var gradePrices = values.Find(x => x.Grade == grade.Id);
                    if (gradePrices == null)
                    {
                        var gradeName = _core.GradeNames.FirstOrDefault(x => x.Grade == grade.Id);
                        gradePrices = new GradePrices
                        {
                            Grade = grade.Id,
                            GradeName = gradeName?.Name ?? string.Empty,
                            VatRate = gradeName?.VatRate ?? 0,
                            Prices = new List<PumpPrices>()
                        };
                        if (_core.GradePriceToSet.TryGetValue(grade.Id, out float priceToSet))
                        {
                            gradePrices.PriceToSet = priceToSet / (float) 10.0;
                        }
                        else
                        {
                            gradePrices.PriceToSet = (float)grade.Price;
                        }

                        values.Add(gradePrices);
                    }

                    gradePrices.Prices.Add(new PumpPrices {Pump = prices.Key, Price = (float)grade.Price});
                }
            }

            values.Sort((x, y) => x.Grade.CompareTo(y.Grade));

            return values;
        }

        public DivertDetails GetDivertDetails()
        {
            bool isDiverted = IsOptServiceDiverted(out IPAddress address, out int fromOptPort, out int toOptPort, out int heartbeatPort,
                out int mediaChannelPort);
            return new DivertDetails
            {
                IsDiverted = isDiverted,
                IpAddress = address == null ? string.Empty : address.ToString(),
                FromOptPort = fromOptPort,
                ToOptPort = toOptPort,
                HeartbeatPort = heartbeatPort,
                MediaChannelPort = mediaChannelPort,
                MediaChannel = _core.MediaChannel
            };
        }

        public IList<InfoMessageDetails> GetInfoDetails()
        {
            var result = DoAction(() =>
            {
                var infoMessages = _core.ControllerWorker.GetInfo() ?? new List<InfoMessage>();

                var results = infoMessages
                    .Where(x => x != null && !string.IsNullOrWhiteSpace(x.Message))
                    .Select(x => new InfoMessageDetails { Time = x.Time, Message = x.Message })
                    .OrderByDescending(x => x.Time)
                    .ToList();

                return Result.Success(results);
            }, LoggingReference);

            return result.IsSuccess ? result.Value : new List<InfoMessageDetails>();
        }

        public VersionInfo GetVersionInfo()
        {
            var helper = _serviceFilesHelper;
            var versionInfo = new VersionInfo
            {
                Current = helper.Current,
                Rollback = helper.Rollback,
                Upgrade = helper.Upgrade,
                PumpIntegrator = helper.PumpIntegrator,
                OfflineFileService = helper.OfflineFileService,
                UploadedFileNames = helper.UploadedFileNames,
                WhitelistFiles = helper.WhitelistFiles,
                LayoutFiles = helper.LayoutFiles,
                UpgradeFiles = helper.UpgradeFiles,
                SoftwareFiles = helper.SoftwareFiles,
                MediaFiles = helper.MediaFiles,
                PlaylistFiles = helper.PlaylistFiles,
                DatabaseBackupFiles = helper.DatabaseBackupFiles,
                OptLogFiles = helper.OptLogFiles
            };

            return versionInfo;
        }       

        public string FetchReceipt(long transactionNumber)
        {
            return _core.FetchReceipt(transactionNumber);
        }

        public string PrintReceipt(long transactionNumber)
        {
            return _core.PrintReceipt(transactionNumber);
        }

        public string SaveReceipt(long transactionNumber)
        {
            return _core.SaveReceipt(transactionNumber);
        }
        
        public DateTime GetShiftEndTime()
        {
            return _core.ShiftEndTime;
        }

        public DateTime GetDayEndTime()
        {
            return _core.DayEndTime;
        }

        public DateTime? GetNextDayEnd()
        {
            return _core.ControllerWorker.NextDayEnd;
        }

        public string SetNextDayEnd(DateTime? dayEnd)
        {
            return _core.ControllerWorker.SetNextDayEnd(dayEnd);
        }

        public LogIntervalDetails GetLogInterval()
        {
            int interval = _core.AllOpts.LogInterval;
            int hours = interval / 3600;
            int minutes = interval % 3600 / 60;
            int seconds = interval % 60;
            return new LogIntervalDetails {Hours = hours, Minutes = minutes, Seconds = seconds};
        }

        public string SetLogInterval(int hours, int minutes, int seconds)
        {
            int interval = (hours * 60 + minutes) * 60 + seconds;
            if (interval == 0)
            {
                interval = 86400;
            }

            return _core.ControllerWorker.SetLogInterval(interval);
        }

        public bool GetUnmanned()
        {
            return _core.UnmannedPseudoPos;
        }

        public bool GetPosConnected()
        {
            return _core.ControllerWorker.GetConnections().HydraPos > 0;
        }

        public bool GetPrinterEnabled()
        {
            return _core.UnmannedPseudoPos && _core.PrinterConfig.Enabled && !_core.IsPrinterBusy;
        }

        public bool GetAsdaDayEndReport()
        {
            return _core.AsdaDayEndReport;
        }

        public PrinterDetails GetPrinterDetails()
        {
            PrinterConfig config = _core.PrinterConfig;
            return new PrinterDetails
            {
                Enabled = config.Enabled, PortName = config.PortName, BaudRate = config.BaudRate, Handshake = config.Handshake.ToString(),
                StopBits = config.StopBits.ToString(), DataBits = config.DataBits, IsPrinterBusy = _core.IsPrinterBusy
            };
        }

        public IEnumerable<FuelTransaction> GetFuelTransactions(DateTime startTime, DateTime endTime)
        {
            return _core.GetFuelTransactions(startTime, endTime);
        }

        public IEnumerable<OtherEvent> GetOtherEvents(DateTime startTime, DateTime endTime)
        {
            return _core.GetOtherEvents(startTime, endTime);
        }

        public string ShowDomsState()
        {
            _showDomsPreparedSetup = false;
            _showDomsFetchedSetup = false;
            _showDomsTcpSetup = false;
            _hubContext.Clients.All.refreshDoms(GetDomsDetails());
            return null;
        }

        public string ShowDomsFetched()
        {
            _showDomsPreparedSetup = false;
            _showDomsFetchedSetup = true;
            _showDomsTcpSetup = false;
            _hubContext.Clients.All.refreshDoms(GetDomsDetails());
            return null;
        }

        public string ShowDomsPrepared()
        {
            _showDomsPreparedSetup = true;
            _showDomsFetchedSetup = false;
            _showDomsTcpSetup = false;
            _hubContext.Clients.All.refreshDoms(GetDomsDetails());
            return null;
        }

        public string ShowDomsTcp()
        {
            _showDomsPreparedSetup = false;
            _showDomsFetchedSetup = false;
            _showDomsTcpSetup = true;
            _hubContext.Clients.All.refreshDoms(GetDomsDetails());
            return null;
        }

        // TODO: Move from here intp Forecourt.Pump namepsace (or SLIB), and ripple through (Pump) interfaces! - see ADO#613151
        public DomsDetails GetDomsDetails()
        {
            var state = _core.DomsState;
            if (_showDomsFetchedSetup || _showDomsPreparedSetup || _showDomsTcpSetup)
            {
                var setup = _showDomsTcpSetup ? _core.DomsTcpSetup :
                    _showDomsPreparedSetup ? _core.DomsPreparedSetup : _core.DomsFetchedSetup;
                return setup == null ? null : new DomsDetails
                {
                    IsFetchedSetup = _showDomsFetchedSetup,
                    IsPreparedSetup = _showDomsPreparedSetup,
                    IsTcpSetup = _showDomsTcpSetup,
                    IpAddress = _core.DomsIpAddress.ToString(),
                    LoginString = _core.DomsLoginString,
                    Enabled = setup.Enabled,
                    Connected = setup.Connected,
                    ForecourtPriceSetId = setup.PriceSetId,
                    ForecourtPriceSetTimestamp = setup.PriceSetTimestamp,
                    PriceSet = new DomsPriceSetDetails
                    {
                        Id = setup.PriceSetId,
                        Timestamp = setup.PriceSetTimestamp,
                        Groups = new List<DomsPriceGroupDetails>(setup.AllPriceGroups().Select(x => new DomsPriceGroupDetails
                        {
                            Id = x,
                            Grades = new List<DomsGradeDetails>(setup.AllGradeIds().Select(y => new DomsGradeDetails
                                {GradeId = y, Price = setup.Price(y, x), Text = setup.GradeName(y), Colour = setup.GradeColour(y)}))
                        }))
                    },
                    FuellingPoints = new List<DomsFuellingPointDetails>(setup.AllPumps().Select(x => new DomsFuellingPointDetails
                    {
                        Id = x.FpId, ActSmId = x.ActiveSmId, MainState = x.MainState.ToString(), SubState = x.SubStates, LockId = x.LockId,
                        GradeId = x.GradeId,
                        Transactions = new List<DomsTransactionDetails>(x.AllTransactions.Select(y => new DomsTransactionDetails
                        {
                            FpId = x.FpId, SeqNo = y.SequenceNumber, GradeId = y.GradeId, CashAmount = y.ActualAmount,
                            ActualVolume = y.ActualVolume,
                            TransLockId = y.TransLockId
                        })),
                        Grades = new List<DomsGradeDetails>(x.AllGradeOptions().Select(y => new DomsGradeDetails
                        {
                            OptionId = y, GradeId = x.OptionGradeId(y), Text = setup.GradeName(x.OptionGradeId(y)),
                            Colour = setup.GradeColour(x.OptionGradeId(y)), Price = x.GradePriceFloat(x.OptionGradeId(y))
                        }))
                    })),                    
                    Grades = new List<DomsGradeDetails>(setup.AllGradeIds().Select(x => new DomsGradeDetails
                        {GradeId = x, Text = setup.GradeName(x), Colour = setup.GradeColour(x)})),
                    TankGauges = new List<DomsTankGaugeDetails>(),
                    PricePoles = new List<DomsPricePoleDetails>()
                };
            }
            else
            {
                return new DomsDetails
                {
                    IsFetchedSetup = _showDomsFetchedSetup,
                    IsPreparedSetup = _showDomsPreparedSetup,
                    IsTcpSetup = _showDomsTcpSetup,
                    IpAddress = _core.DomsIpAddress.ToString(), LoginString = _core.DomsLoginString, Enabled = _core.DomsStateEnabled,
                    Connected = _core.DomsStateConnected,
                    OperationMode = state.OperationMode,
                    FcStatus1 = state.FcStatus1,
                    FcStatus2 = state.FcStatus2,
                    PosId = state.PosId,
                    PosCountryCode = state.PosCountryCode,
                    PosVersionId = state.PosVersionId,
                    MajorVersion = state.MajorVersion,
                    MinorVersion = state.MinorVersion,
                    VatRate = state.VatRate,
                    ForecourtPriceSetId = state.ForecourtPriceSetId,
                    ForecourtPriceSetTimestamp = state.ForecourtPriceSetTimestamp,
                    PriceSet = new DomsPriceSetDetails
                    {
                        Id = state.PriceSet.Id, Timestamp = state.PriceSet.Timestamp,
                        Groups = new List<DomsPriceGroupDetails>(state.PriceSet.Groups.Select(x => new DomsPriceGroupDetails
                        {
                            Id = x.Id,
                            Grades = new List<DomsGradeDetails>(x.GradePrices.Select(y => new DomsGradeDetails
                                {GradeId = y.Id, Price = y.Price, Text = y.Text}))
                        }))
                    },
                    FuellingPoints = new List<DomsFuellingPointDetails>(state.FuellingPointList.Select(x => new DomsFuellingPointDetails
                    {
                        Id = x.Id, GradeId = x.GradeId, PumpTotalFpId = x.PumpTotalFpId, PumpTotalVolume = x.PumpTotalVolume,
                        PumpTotalCash = x.PumpTotalCash, CachedStatusValid = x.CachedStatusValid, Descriptor = x.Descriptor,
                        IsSupplStatusSet = x.IsSupplStatusSet, FuellingDataPresent = x.FuellingDataPresent, InfoPresent = x.InfoPresent,
                        GetPaymentControlParm = x.GetPaymentControlParms, DataFpId = x.DataFpId, Money = x.Money, Volume = x.Volume,
                        ActSmId = x.ActSmId, MainState = x.MainState.ToString(), SubState = x.SubState,
                        LockId = x.LockId,
                        GradeTotals = new List<DomsGradeTotalDetails>(x.PumpGradeTotalList.Select(y => new DomsGradeTotalDetails
                            {GradeId = y.GradeId, OptionNumber = y.OptionNumber, Volume = y.Volume})),
                        Transactions = new List<DomsTransactionDetails>(x.TransactionList.Select(y => new DomsTransactionDetails
                        {
                            FpId = y.FpId, AuthId = y.AuthId, SeqNo = y.SeqNo, GradeId = y.GradeId, GradeDescriptor = y.GradeDescriptor,
                            GradeOptionNo = y.GradeOptionNo, Money = y.Money, MoneyDue = y.MoneyDue, CashAmount = y.CashAmount,
                            Price = y.Price,
                            ActualPrice = y.ActualPrice, Volume = y.Volume, ActualVolume = y.ActualVolume
                        })),
                        Grades = new List<DomsGradeDetails>(x.GradeList.Select(y => new DomsGradeDetails
                            {GradeId = y.Id, Text = y.Text, Price = y.Price}))
                    })),
                    Grades = new List<DomsGradeDetails>(state.GradeList.Select(x => new DomsGradeDetails
                        {GradeId = x.Id, Text = x.Text, Price = x.Price})),
                    TankGauges = new List<DomsTankGaugeDetails>(state.TankGaugeList.Select(x => new DomsTankGaugeDetails
                    {
                        Id = x.Id,
                        Data = new List<DomsTankGaugeDataDetails>(x.DataList.Select(y => new DomsTankGaugeDataDetails
                        {
                            Description = y.Description, IsHeight = y.IsHeight, IsWater = y.IsWater, IsDip = y.IsDip, IsTemp = y.IsTemp,
                            Value = y.Value
                        }))
                    })),
                    PricePoles = new List<DomsPricePoleDetails>(state.PricePoleList.Select(x => new DomsPricePoleDetails {Id = x.Id}))
                };
            }
        }

        public string ClosePump(byte pump, bool closed)
        {
            GetLogger().Debug($"{(closed ? "Close" : "Open")} Pump {pump}");
            return closed ? _core.ControllerWorker.ClosePump(pump) : _core.ControllerWorker.OpenPump(pump);
        }

        public string ForceClosePump(byte pump)
        {
            GetLogger().Debug($"Force Close Pump {pump}");
            return _core.ControllerWorker.ForceClosePump(pump);
        }

        public string ForcePumpOutside(byte pump)
        {
            GetLogger().Debug($"Force Pump {pump} Outside");
            return _core.ControllerWorker.ForcePumpOutside(pump);
        }

        public string SetMaxFillOverrideForFuelCards(byte pump, bool flag)
        {
            GetLogger().Debug($"{(flag ? "Set" : "Clear")} Max Fill Override For Fuel Cards, pump {pump}");
            return _core.ControllerWorker.SetMaxFillOverrideForFuelCards(pump, flag);
        }

        public string SetMaxFillOverrideForPaymentCards(byte pump, bool flag)
        {
            GetLogger().Debug($"{(flag ? "Set" : "Clear")} Max Fill Override For Payment Cards, pump {pump}");
            return _core.ControllerWorker.SetMaxFillOverrideForPaymentCards(pump, flag);
        }

        public string MapToTid(byte pump, string tid)
        {
            GetLogger().Debug($"Map to TID, Pump {pump}, {(tid == null ? "No TID" : $"TID {tid}")}");
            return _core.ControllerWorker.MapToTid(pump, tid);
        }

        public Result MapToOpt(byte pump, string opt, string reference = null)
        {
            return DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Debug, HeaderParameters, () => new[] {$" Pump: {pump}; OPT: {(string.IsNullOrWhiteSpace(opt) ? "No OPT" : opt)}"});

                return _core.ControllerWorker.MapToOpt(pump, opt, LoggingReference);
            }, reference);
        }

        public string SetPrice(byte grade, int price)
        {
            GetLogger().Debug($"Set Price, Grade {grade}, Price {price}");
            bool result = _core.ControllerWorker.SetGradePrice(grade, price).IsSuccess;
            GetLogger().Debug($"Set Price {(result ? "Success" : "Failure")}");
            if (result)
            {
                return null;
            }
            else
            {
                return "Setting Price Failed";
            }
        }

        public string SetGradeName(byte grade, string name)
        {
            GetLogger().Debug($"Set Grade Name, Grade {grade}, Name {name}");
            return _core.ControllerWorker.SetGradeName(grade, name);
        }

        public string SetGradeVatRate(byte grade, float vatRate)
        {
            GetLogger().Debug($"Set Grade VAT Rate, Grade {grade}, VAT Rate {vatRate}");
            return _core.ControllerWorker.SetGradeVatRate(grade, vatRate);
        }

        public Result SetDefaultMode(byte pump, bool kioskOnly, bool outsideOnly, string reference = null)
        {
            return DoAction(() =>
            {
                string modeString;
                if (kioskOnly)
                {
                    modeString = "Kiosk Only";
                }
                else if (outsideOnly)
                {
                    modeString = "Outside Only";
                }
                else
                {
                    modeString = "Mixed";
                }

                GetLogger().Debug($"Set Default Mode, Pump {pump}, Mode {modeString}");
                return _core.ControllerWorker.SetDefaultMode(pump, kioskOnly, outsideOnly, LoggingReference);
            }, reference);
        }

        public string SetAutoAuth(bool isOn)
        {
            GetLogger().Debug($"Set Auto Auth {OnOff(isOn)}");
            return _core.ControllerWorker.SetAutoAuth(isOn);
        }

        public string SetForwardFuelPriceUpdate(bool isOn)
        {
            GetLogger().Debug($"Set Forward Fuel Price Update {OnOff(isOn)}");
            return _core.ControllerWorker.SetForwardFuelPriceUpdate(isOn);
        }

        public string SetMediaChannel(bool isOn)
        {
            GetLogger().Debug($"Set Media Channel {OnOff(isOn)}");
            return _core.ControllerWorker.SetMediaChannel(isOn);
        }

        public string SetUnmannedPseudoPos(bool isOn)
        {
            GetLogger().Debug($"Set Unamanned Pseudo POS {OnOff(isOn)}");
            return _core.ControllerWorker.SetUnmannedPseudoPos(isOn);
        }

        public string SetAsdaDayEndReport(bool isAsda)
        {
            GetLogger().Debug($"Set Asda Day End Report {OnOff(isAsda)}");
            return _core.ControllerWorker.SetAsdaDayEndReport(isAsda);
        }

        public string SetPrinterEnabled(bool isEnabled)
        {
            GetLogger().Debug($"Set Printer Enabled {OnOff(isEnabled)}");
            return _core.SetPrinterEnabled(isEnabled);
        }

        public string SetPrinterPortName(string portName)
        {
            GetLogger().Debug($"Set Printer Port Name {portName}");
            return _core.SetPrinterPortName(portName);
        }

        public string SetPrinterBaudRate(int baudRate)
        {
            GetLogger().Debug($"Set Printer Baud Rate {baudRate}");
            return _core.SetPrinterBaudRate(baudRate);
        }

        public string SetPrinterHandshake(Handshake handshake)
        {
            GetLogger().Debug($"Set Printer Handshake {handshake}");
            return _core.SetPrinterHandshake(handshake);
        }

        public string SetPrinterStopBits(StopBits stopBits)
        {
            GetLogger().Debug($"Set Printer Stop Bits {stopBits}");
            return _core.SetPrinterStopBits(stopBits);
        }

        public string SetPrinterDataBits(int dataBits)
        {
            GetLogger().Debug($"Set Printer Data Bits {dataBits}");
            return _core.SetPrinterDataBits(dataBits);
        }

        private static string OnOff(bool isOn)
        {
            return isOn ? "On" : "Off";
        }

        public string SetContactlessAllowed(bool isAllowed)
        {
           GetLogger().Debug($"Set Contactless {(isAllowed ? string.Empty : "Not ")}Allowed");
            return _core.ControllerWorker.SetContactlessAllowed(isAllowed);
        }

        public string SetContactlessCardPreAuth(uint limit)
        {
            GetLogger().Debug($"Set Contactless Card pre-auth amount {limit}");
            return _core.ControllerWorker.SetContactlessCardPreAuth(limit);
        }

        public string SetContactlessDevicePreAuth(uint limit)
        {
             GetLogger().Debug($"Set Contactless Device pre-auth amount {limit}");
            return _core.ControllerWorker.SetContactlessDevicePreAuth(limit);
        }

        public string SetContactlessTtq(string ttq)
        {
            GetLogger().Debug($"Set Contactless TTQ value {ttq}");
            return _core.ControllerWorker.SetContactlessTtq(ttq);
        }

        public string SetContactlessSingleButton(bool showSingleButton)
        {
            GetLogger().Debug($"Set Contactless Single Button {(showSingleButton ? string.Empty : "Not ")}Enabled");
            return _core.ControllerWorker.SetContactlessSingleButton(showSingleButton);
        }

        public string SetReceiptHeaders(string optIdString, string[] headers)
        {
            if (!AreValidReceiptLines(headers))
            {
                return "Invalid Receipt Header";
            }

            var header = GetReceiptLine(headers);
            GetLogger().Debug($"Set Receipt Header, OPT {optIdString}, Header {header}");
            return _core.ControllerWorker.SetReceiptHeader(optIdString, header);
        }

        public string SetReceiptFooters(string optIdString, string[] footers)
        {
            if (!AreValidReceiptLines(footers))
            {
                return "Invalid Receipt Footer";
            }

            var footer = GetReceiptLine(footers);
            GetLogger().Debug($"Set Receipt Footer, OPT {optIdString}, Footer {footer}");
            return _core.ControllerWorker.SetReceiptFooter(optIdString, footer);
        }

        public string SetPlaylistFileName(string optIdString, string filename)
        {
            GetLogger().Debug($"Set Playlist File Name, OPT {optIdString}, File Name {filename}");
            return _core.ControllerWorker.SetPlaylistFileName(optIdString, filename);
        }

        public string RestartOpt(string optIdString)
        {
            GetLogger().Debug($"Restart OPT {optIdString}");
            return _core.ControllerWorker.RestartOpt(optIdString);
        }

        public string RefreshOpt(string optIdString = null)
        {
            GetLogger().Debug($"Restart {(optIdString.IsNullOrWhiteSpace() ? "All OPTs" : "OPT {optIdString}")}");
            return _core.ControllerWorker.RefreshOpt(optIdString);
        }

        public string RequestOptLog(string optIdString = null)
        {
            GetLogger().Debug($"Request log {(optIdString.IsNullOrWhiteSpace() ? "All OPTs" : "OPT {optIdString}")}");
            return _core.ControllerWorker.RequestOptLog(optIdString);
        }

        public string EngineerResetOpt(string optIdString)
        {
            GetLogger().Debug($"Engineer reset OPT {optIdString}");
            return _core.ControllerWorker.EngineerResetOpt(optIdString);
        }

        public Result SetPaymentTimeout(PaymentTimeoutType mode, int timeout)
        {
            return DoAction(() =>
            {
                var result = _hydraDb.SetPaymentTimeout(mode, timeout);
                if (!result.IsSuccess)
                {
                    return result;
                }

                return Result.SuccessIf(_core.ControllerWorker.SetPaymentTimeout($"{mode}", timeout), "SetTimeout failed");
            }, null);
        }

        public string SetPaymentTimeout(string mode, int timeout)
        {
            GetLogger().Debug($"Set Payment Timeout, Mode {mode}, Timeout {timeout}");
            bool result = _core.ControllerWorker.SetPaymentTimeout(mode, timeout);
            GetLogger().Debug($"Set Payment Timeout {(result ? "Success" : "Failure")}");
            if (result)
            {
                return null;
            }
            else
            {
                return "Setting Payment Timeout Failed";
            }
        }

        public string SetReceiptTimeout(int timeout)
        {
            GetLogger().Debug($"Set Receipt Timeout {timeout}");
            return _core.ControllerWorker.SetReceiptTimeout(timeout);
        }

        public string SetReceiptMaxCount(int count)
        {
            GetLogger().Debug($"Set Receipt Max Count {count}");
            return _core.ControllerWorker.SetReceiptMaxCount(count);
        }

        public void UpgradeService()
        {
            GetLogger().Debug("Upgrading Service");
            _core.ControllerWorker.UpgradeService();
        }

        public void RollbackService()
        {
            GetLogger().Debug("Rolling Service Back");
            _core.ControllerWorker.RollbackService();
        }

        public void RestartService()
        {
            GetLogger().Debug("Restarting Service");
            _core.ControllerWorker.RestartService();
        }

        public string SaveFile(string fileName)
        {
            GetLogger().Debug($"Save file {fileName}");
            return _core.ControllerWorker.SaveFile(fileName);
        }

        public string SaveSoftwareFile(string fileName)
        {
            GetLogger().Debug($"Save software file {fileName}");
            return _core.ControllerWorker.SaveSoftwareFile(fileName);
        }

        public string SaveWhitelistFile(string fileName)
        {
            GetLogger().Debug($"Save whitelist file {fileName}");
            return _core.ControllerWorker.SaveWhitelistFile(fileName);
        }

        public string SaveLayoutFile(string fileName)
        {
            GetLogger().Debug($"Save layout file {fileName}");
            return _core.ControllerWorker.SaveLayoutFile(fileName);
        }

        public string SaveMediaFile(string fileName)
        {
            GetLogger().Debug($"Save media file {fileName}");
            return _core.ControllerWorker.SaveMediaFile(fileName);
        }

        public string SavePlaylistFile(string fileName)
        {
            GetLogger().Debug($"Save playlist file {fileName}");
            return _core.ControllerWorker.SavePlaylistFile(fileName);
        }

        public string SaveContactlessPropertiesFile(string fileName)
        {
            GetLogger().Debug($"Save contactless properties file {fileName}");
            return _core.ControllerWorker.SaveContactlessPropertiesFile(fileName);
        }

        public string RemoveFile(string fileName)
        {
            GetLogger().Debug($"Remove file {fileName}");
            return _core.ControllerWorker.RemoveFile(fileName);
        }

        public string RemoveWhitelistFile(string fileName)
        {
            GetLogger().Debug($"Remove whitelist file {fileName}");
            return _core.ControllerWorker.RemoveWhitelistFile(fileName);
        }

        public string RemoveLayoutFile(string fileName)
        {
            GetLogger().Debug($"Remove layout file {fileName}");
            return _core.ControllerWorker.RemoveLayoutFile(fileName);
        }

        public string RemoveUpgradeFile(string fileName)
        {
            GetLogger().Debug($"Remove upgrade file {fileName}");
            return _core.ControllerWorker.RemoveUpgradeFile(fileName);
        }

        public string RemoveMediaFile(string fileName)
        {
            GetLogger().Debug($"Remove media file {fileName}");
            return _core.ControllerWorker.RemoveMediaFile(fileName);
        }

        public string RemoveSoftwareFile(string fileName)
        {
            GetLogger().Debug($"Remove software file {fileName}");
            return _core.ControllerWorker.RemoveSoftwareFile(fileName);
        }

        public string RemovePlaylistFile(string fileName)
        {
            GetLogger().Debug($"Remove playlist file {fileName}");
            return _core.ControllerWorker.RemovePlaylistFile(fileName);
        }

        public string RemoveDatabaseBackupFile(string fileName)
        {
            GetLogger().Debug($"Remove database backup file {fileName}");
            return _core.ControllerWorker.RemoveDatabaseBackupFile(fileName);
        }

        public string RemoveOptLogFile(string fileName)
        {
            GetLogger().Debug($"Remove OPT log file {fileName}");
            return _core.ControllerWorker.RemoveOptLogFile(fileName);
        }

        public string DatabaseBackup()
        {
            GetLogger().Debug("Backup database");
            return _core.ControllerWorker.DatabaseBackup();
        }

        public string DatabaseRestore(string fileName)
        {
            GetLogger().Debug($"Restore database from file {fileName}");
            return _core.ControllerWorker.DatabaseRestore(fileName);
        }

        public StatusCodeResult<object> ConfigExtract()
        {
            DoDeferredLogging(LogLevel.Debug, "ConfigExtract", () => new[] { $"Extract configuration" });
            return ExtractConfiguration();
        }

        public string SetFromOptPort(int port)
        {
            GetLogger().Debug($"Set From OPT Port {port}");
            Endpoints endpoints = GetEndpoints();
            return _core.ControllerWorker.SetServicePorts(port, endpoints.ToOpt.Port, endpoints.Heartbeat.Port, endpoints.HydraPos.Port,
                endpoints.RetalixPos.Port, endpoints.ThirdPartyPos.Port, endpoints.MediaChannel.Port);
        }

        public string SetToOptPort(int port)
        {
            GetLogger().Debug($"Set To OPT Port {port}");
            Endpoints endpoints = GetEndpoints();
            return _core.ControllerWorker.SetServicePorts(endpoints.FromOpt.Port, port, endpoints.Heartbeat.Port, endpoints.HydraPos.Port,
                endpoints.RetalixPos.Port, endpoints.ThirdPartyPos.Port, endpoints.MediaChannel.Port);
        }

        public string SetHeartbeatPort(int port)
        {
            GetLogger().Debug($"Set Heartbeat Port {port}");
            Endpoints endpoints = GetEndpoints();
            return _core.ControllerWorker.SetServicePorts(endpoints.FromOpt.Port, endpoints.ToOpt.Port, port, endpoints.HydraPos.Port,
                endpoints.RetalixPos.Port, endpoints.ThirdPartyPos.Port, endpoints.MediaChannel.Port);
        }

        public string SetHydraPosPort(int port)
        {
            GetLogger().Debug($"Set Hydra POS Port {port}");
            Endpoints endpoints = GetEndpoints();
            return _core.ControllerWorker.SetServicePorts(endpoints.FromOpt.Port, endpoints.ToOpt.Port, endpoints.Heartbeat.Port, port,
                endpoints.RetalixPos.Port, endpoints.ThirdPartyPos.Port, endpoints.MediaChannel.Port);
        }

        public string SetRetalixPosPort(int port)
        {
            GetLogger().Debug($"Set Retalix POS Port {port}");
            Endpoints endpoints = GetEndpoints();
            return _core.ControllerWorker.SetServicePorts(endpoints.FromOpt.Port, endpoints.ToOpt.Port, endpoints.Heartbeat.Port,
                endpoints.HydraPos.Port, port, endpoints.ThirdPartyPos.Port, endpoints.MediaChannel.Port);
        }

        public string SetThirdPartyPosPort(int port)
        {
            GetLogger().Debug($"Set Third Party POS Port {port}");
            Endpoints endpoints = GetEndpoints();
            return _core.ControllerWorker.SetServicePorts(endpoints.FromOpt.Port, endpoints.ToOpt.Port, endpoints.Heartbeat.Port,
                endpoints.HydraPos.Port, endpoints.RetalixPos.Port, port, endpoints.MediaChannel.Port);
        }

        public string SetMediaChannelPort(int port)
        {
            GetLogger().Debug($"Set Media Channel Port {port}");
            Endpoints endpoints = GetEndpoints();
            return _core.ControllerWorker.SetServicePorts(endpoints.FromOpt.Port, endpoints.ToOpt.Port, endpoints.Heartbeat.Port,
                endpoints.HydraPos.Port, endpoints.RetalixPos.Port, endpoints.ThirdPartyPos.Port, port);
        }

        public string SetAnprPort(int port)
        {
            GetLogger().Debug($"Set ANPR Port {port}");
            Endpoints endpoints = GetEndpoints();
            return _core.ControllerWorker.SetAnpr(endpoints.Anpr.Address, port);
        }

        /// <inheritdoc />
        public Result<StatusCodeResult> SetPumpControllerAddressHttp(IPAddress address, int port = 0)
        {
            var result = _controllerWorker.SetPumpControllerAddress(address, port == 0 ? GetEndpoints().Pump.Port : port);
            return Result.Success(result.IsSuccess ? StatusCodeResult.Success : StatusCodeResult.Specific(HttpStatusCode.BadRequest));
        }

        /// <inheritdoc />
        public Result<StatusCodeResult> SetPumpControllerLogonInfoHttp(string loginInfo)
        {
            var result = _controllerWorker.SetPumpControllerLogonInfo(loginInfo);
            return Result.Success(result.IsSuccess ? StatusCodeResult.Success : StatusCodeResult.Specific(HttpStatusCode.BadRequest));
        }

        public string SetAnprAddress(IPAddress address)
        {
            GetLogger().Debug($"Set ANPR Address {address}");
            Endpoints endpoints = GetEndpoints();
            return _core.ControllerWorker.SetAnpr(address, endpoints.Anpr.Port);
        }

        public string SetCarWashPort(int port)
        {
            GetLogger().Debug($"Set Car Wash Port {port}");
            Endpoints endpoints = GetEndpoints();
            return _core.ControllerWorker.SetCarWash(endpoints.CarWash.Address, port);
        }

        public string SetCarWashAddress(IPAddress address)
        {
            GetLogger().Debug($"Set Car Wash Address {address}");
            Endpoints endpoints = GetEndpoints();
            return _core.ControllerWorker.SetCarWash(address, endpoints.CarWash.Port);
        }

        public Result<StatusCodeResult> SetTankGaugeAddressHttp(IPAddress address, int port = 0)
        {
            var result = _controllerWorker.SetTankGaugeAddress(address, port == 0 ? GetEndpoints().TankGauge.Port : port);
            return Result.Success(result.IsSuccess ? StatusCodeResult.Success : StatusCodeResult.Specific(HttpStatusCode.BadRequest));
        }

        public string SetHydraMobilePort(int port)
        {
            GetLogger().Debug($"Set Hydra Mobile Port {port}");
            Endpoints endpoints = GetEndpoints();
            return _core.ControllerWorker.SetHydraMobile(endpoints.HydraMobile.Address, port);
        }

        public string SetHydraMobileAddress(IPAddress address)
        {
            GetLogger().Debug($"Set Hydra Mobile Address {address}");
            Endpoints endpoints = GetEndpoints();
            return _core.ControllerWorker.SetHydraMobile(address, endpoints.HydraMobile.Port);
        }

        private static string CombineResults(string result1, string result2)
        {
            if (result1 == null)
            {
                return result2;
            }
            else if (result2 == null)
            {
                return result1;
            }
            else
            {
                return $"{result1}, {result2}";
            }
        }

        public string SetAllEndpoints
        (int fromOptPort, int toOptPort, int heartbeatPort, int hydraPosPort, int retalixPosPort, int thirdPartyPosPort,
            int mediaChannelPort, IPAddress siteControllerAddress, int siteControllerPort, IPAddress anprAddress, int anprPort,
            IPAddress carWashAddress, int carWashPort, IPAddress tankGaugeAddress, int tankGaugePort, IPAddress hydraMobileAddress,
            int hydraMobilePort)
        {
            Endpoints endpoints = GetEndpoints();
            string result1 = null;
            string result2 = null;
            string result3 = null;
            string result4 = null;
            string result5 = null;
            string result6 = null;
            if (fromOptPort != endpoints.FromOpt.Port || toOptPort != endpoints.ToOpt.Port || heartbeatPort != endpoints.Heartbeat.Port ||
                hydraPosPort != endpoints.HydraPos.Port || retalixPosPort != endpoints.RetalixPos.Port ||
                thirdPartyPosPort != endpoints.ThirdPartyPos.Port || mediaChannelPort != endpoints.MediaChannel.Port)
            {
                result1 = _controllerWorker.SetServicePorts(fromOptPort, toOptPort, heartbeatPort, hydraPosPort, retalixPosPort,
                    thirdPartyPosPort, mediaChannelPort);
            }

            if (!siteControllerAddress.Equals(endpoints.Pump.Address) || siteControllerPort != endpoints.Pump.Port)
            {
                var result = _controllerWorker.SetPumpControllerAddress(siteControllerAddress, siteControllerPort);
                result2 = result.IsSuccess ? null : result.Error;
            }

            if (!anprAddress.Equals(endpoints.Anpr.Address) || anprPort != endpoints.Anpr.Port)
            {
                result3 = _controllerWorker.SetAnpr(anprAddress, anprPort);
            }

            if (!carWashAddress.Equals(endpoints.CarWash.Address) || carWashPort != endpoints.CarWash.Port)
            {
                result4 = _controllerWorker.SetCarWash(carWashAddress, carWashPort);
            }

            if (!tankGaugeAddress.Equals(endpoints.TankGauge.Address) || tankGaugePort != endpoints.TankGauge.Port)
            {
                var result = _controllerWorker.SetTankGaugeAddress(tankGaugeAddress, tankGaugePort);
                result5 = result.IsSuccess ? null : result.Error;
            }

            if (!hydraMobileAddress.Equals(endpoints.HydraMobile.Address) || hydraMobilePort != endpoints.HydraMobile.Port)
            {
                result6 = _controllerWorker.SetHydraMobile(hydraMobileAddress, hydraMobilePort);
            }

            return CombineResults(result1, CombineResults(result2, CombineResults(result3, CombineResults(result4, CombineResults(result5, result6)))));
        }

        public string SetRetalixPosPrimaryIpAddress(IPAddress address)
        {
            return _core.ControllerWorker.SetRetalixPosPrimaryIpAddress(address);
        }

        public string SetServiceAddress(IPAddress address)
        {
            GetLogger().Debug($"Set Service Address {address}");
            return _core.ControllerWorker.SetServiceAddress(address);
        }

        public string AddEsocket(IPAddress address, int port)
        {
            GetLogger().Debug($"Add eSocket.POS {address}:{port}");
            return _core.ControllerWorker.AddEsocket(address, port);
        }

        public string RemoveEsocket(IPAddress address, int port)
        {
            GetLogger().Debug($"Remove eSocket.POS {address}:{port}");
            return _core.ControllerWorker.RemoveEsocket(address, port);
        }

        public string DivertOptService(IPAddress address, int fromOptPort, int toOptPort, int heartbeatPort, int mediaChannelPort)
        {
            GetLogger().Debug($"Divert HydraOPT Service: IP Address {address}," + $" From OPT Port {fromOptPort}," + $" To OPT Port {toOptPort}," +
                          $" Heartbeat Port {heartbeatPort}" + $" Media Channel Port {mediaChannelPort}");
            return _core.ControllerWorker.DivertOptService(address, fromOptPort, toOptPort, heartbeatPort, mediaChannelPort);
        }

        public string CancelDivertOptService()
        {
            GetLogger().Debug("Cancel Divert HydraOPT Service");
            return _core.ControllerWorker.CancelDivertOptService();
        }

        /// <summary>
        /// Get the most recent alternative OPT settings.
        /// </summary>
        /// <param name="address">Alternative IP Address.</param>
        /// <param name="fromOptPort">Alternative From OPT Port.</param>
        /// <param name="toOptPort">Alternative To OPT Port.</param>
        /// <param name="heartbeatPort">Alternative Heartbeat Port.</param>
        /// <param name="mediaChannelPort">Alternative Heartbeat Port.</param>
        /// <returns>True if using alternative OPT settings.</returns>
        private bool IsOptServiceDiverted
            (out IPAddress address, out int fromOptPort, out int toOptPort, out int heartbeatPort, out int mediaChannelPort)
        {
            GetLogger().Debug("Is HydraOPT Service Diverted");
            return _core.ControllerWorker.IsOptServiceDiverted(out address, out fromOptPort, out toOptPort, out heartbeatPort,
                out mediaChannelPort);
        }

        public string SetConfigBatch(bool isBatch)
        {
            GetLogger().Debug($"Set Config Batch to {isBatch}");
            return _core.ControllerWorker.SetConfigBatch(isBatch);
        }

        public string AddLoyalty(string name)
        {
            return _core.ControllerWorker.AddGenericLoyalty(name);
        }

        public string DeleteLoyalty(string name)
        {
            return _core.ControllerWorker.DeleteGenericLoyalty(name);
        }

        public string SetLoyaltyPresent(string name, bool present)
        {
            return _core.ControllerWorker.SetGenericLoyaltyPresent(name, present);
        }

        public string SetLoyaltyTerminalSiteId(string name, string siteId)
        {
            GetLogger().Debug($"Set {name} Loyalty Terminal Site ID {siteId}");
            _core.GenericOptConfig.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
            if (loyalty == null)
            {
                return $"Unknown Loyalty Scheme {name}";
            }

            loyalty = new GenericLoyalty(
                new LoyaltyTerminal(siteId, loyalty.Terminal.TerminalId, loyalty.Terminal.Footer1, loyalty.Terminal.Footer2,
                    loyalty.Terminal.Timeout, loyalty.Terminal.ApiKey, loyalty.Terminal.HttpHeader), loyalty.Hosts, loyalty.Hostnames,
                loyalty.Iins, loyalty.TariffMappings, loyalty.IsPresent);
            return _core.ControllerWorker.SetGenericLoyalty(name, loyalty);
        }

        public string SetLoyaltyTerminalTerminalId(string name, string terminalId)
        {
            GetLogger().Debug($"Set {name} Loyalty Terminal Terminal ID {terminalId}");
            _core.GenericOptConfig.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
            if (loyalty == null)
            {
                return $"Unknown Loyalty Scheme {name}";
            }

            loyalty = new GenericLoyalty(
                new LoyaltyTerminal(loyalty.Terminal.SiteId, terminalId, loyalty.Terminal.Footer1, loyalty.Terminal.Footer2,
                    loyalty.Terminal.Timeout, loyalty.Terminal.ApiKey, loyalty.Terminal.HttpHeader), loyalty.Hosts, loyalty.Hostnames,
                loyalty.Iins, loyalty.TariffMappings, loyalty.IsPresent);
            return _core.ControllerWorker.SetGenericLoyalty(name, loyalty);
        }

        public string SetLoyaltyTerminalFooter1(string name, string footer1)
        {
            GetLogger().Debug($"Set {name} Loyalty Terminal Footer 1 {footer1}");
            _core.GenericOptConfig.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
            if (loyalty == null)
            {
                return $"Unknown Loyalty Scheme {name}";
            }

            loyalty = new GenericLoyalty(
                new LoyaltyTerminal(loyalty.Terminal.SiteId, loyalty.Terminal.TerminalId, footer1, loyalty.Terminal.Footer2,
                    loyalty.Terminal.Timeout, loyalty.Terminal.ApiKey, loyalty.Terminal.HttpHeader), loyalty.Hosts, loyalty.Hostnames,
                loyalty.Iins, loyalty.TariffMappings, loyalty.IsPresent);
            return _core.ControllerWorker.SetGenericLoyalty(name, loyalty);
        }

        public string SetLoyaltyTerminalFooter2(string name, string footer2)
        {
            GetLogger().Debug($"Set {name} Loyalty Terminal Footer 2 {footer2}");
            _core.GenericOptConfig.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
            if (loyalty == null)
            {
                return $"Unknown Loyalty Scheme {name}";
            }

            loyalty = new GenericLoyalty(
                new LoyaltyTerminal(loyalty.Terminal.SiteId, loyalty.Terminal.TerminalId, loyalty.Terminal.Footer1, footer2,
                    loyalty.Terminal.Timeout, loyalty.Terminal.ApiKey, loyalty.Terminal.HttpHeader), loyalty.Hosts, loyalty.Hostnames,
                loyalty.Iins, loyalty.TariffMappings, loyalty.IsPresent);
            return _core.ControllerWorker.SetGenericLoyalty(name, loyalty);
        }

        public string SetLoyaltyTerminalTimeout(string name, int timeout)
        {
            GetLogger().Debug($"Set {name} Loyalty Terminal Timeout {timeout}");
            _core.GenericOptConfig.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
            if (loyalty == null)
            {
                return $"Unknown Loyalty Scheme {name}";
            }

            loyalty = new GenericLoyalty(
                new LoyaltyTerminal(loyalty.Terminal.SiteId, loyalty.Terminal.TerminalId, loyalty.Terminal.Footer1,
                    loyalty.Terminal.Footer2, timeout, loyalty.Terminal.ApiKey, loyalty.Terminal.HttpHeader), loyalty.Hosts,
                loyalty.Hostnames, loyalty.Iins, loyalty.TariffMappings, loyalty.IsPresent);
            return _core.ControllerWorker.SetGenericLoyalty(name, loyalty);
        }

        public string SetLoyaltyTerminalApiKey(string name, string apiKey)
        {
            GetLogger().Debug($"Set {name} Loyalty Terminal API Key {apiKey}");
            _core.GenericOptConfig.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
            if (loyalty == null)
            {
                return $"Unknown Loyalty Scheme {name}";
            }

            loyalty = new GenericLoyalty(
                new LoyaltyTerminal(loyalty.Terminal.SiteId, loyalty.Terminal.TerminalId, loyalty.Terminal.Footer1,
                    loyalty.Terminal.Footer2, loyalty.Terminal.Timeout, apiKey, loyalty.Terminal.HttpHeader), loyalty.Hosts,
                loyalty.Hostnames, loyalty.Iins, loyalty.TariffMappings, loyalty.IsPresent);
            return _core.ControllerWorker.SetGenericLoyalty(name, loyalty);
        }

        public string SetLoyaltyTerminalHttpHeader(string name, string httpHeader)
        {
            GetLogger().Debug($"Set {name} Loyalty Terminal HTTP Header {httpHeader}");
            _core.GenericOptConfig.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
            if (loyalty == null)
            {
                return $"Unknown Loyalty Scheme {name}";
            }

            loyalty = new GenericLoyalty(
                new LoyaltyTerminal(loyalty.Terminal.SiteId, loyalty.Terminal.TerminalId, loyalty.Terminal.Footer1,
                    loyalty.Terminal.Footer2, loyalty.Terminal.Timeout, loyalty.Terminal.ApiKey, httpHeader), loyalty.Hosts,
                loyalty.Hostnames, loyalty.Iins, loyalty.TariffMappings, loyalty.IsPresent);
            return _core.ControllerWorker.SetGenericLoyalty(name, loyalty);
        }

        public string SetLoyaltyHostsAddHost(string name, IPAddress address, int port)
        {
            GetLogger().Debug($"Set {name} Loyalty Hosts Add Host {address}:{port}");
            _core.GenericOptConfig.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
            if (loyalty == null)
            {
                return $"Unknown Loyalty Scheme {name}";
            }

            IList<GenericEndPoint> hosts = new List<GenericEndPoint>(loyalty.Hosts)
            {
                new GenericEndPoint(address, port)
            };
            loyalty = new GenericLoyalty(loyalty.Terminal, hosts, loyalty.Hostnames, loyalty.Iins, loyalty.TariffMappings, loyalty.IsPresent);
            return _core.ControllerWorker.SetGenericLoyalty(name, loyalty);
        }

        public string SetLoyaltyHostsRemoveHost(string name, IPAddress address, int port)
        {
            GetLogger().Debug($"Set {name} Loyalty Hosts Remove Host {address}:{port}");
            _core.GenericOptConfig.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
            if (loyalty == null)
            {
                return $"Unknown Loyalty Scheme {name}";
            }

            IList<GenericEndPoint> hosts =
                new List<GenericEndPoint>(loyalty.Hosts.Where(x => !x.IpAddress.Equals(address.ToString()) || x.Port != port));
            loyalty = new GenericLoyalty(loyalty.Terminal, hosts, loyalty.Hostnames, loyalty.Iins, loyalty.TariffMappings, loyalty.IsPresent);
            return _core.ControllerWorker.SetGenericLoyalty(name, loyalty);
        }

        public string SetLoyaltyHostnamesAddHostname(string name, string hostname)
        {
            GetLogger().Debug($"Set {name} Loyalty Hostnames Add Hostname {hostname}");
            _core.GenericOptConfig.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
            if (loyalty == null)
            {
                return $"Unknown Loyalty Scheme {name}";
            }

            IList<string> hostnames = new List<string>(loyalty.Hostnames)
            {
                hostname
            };
            loyalty = new GenericLoyalty(loyalty.Terminal, loyalty.Hosts, hostnames, loyalty.Iins, loyalty.TariffMappings, loyalty.IsPresent);
            return _core.ControllerWorker.SetGenericLoyalty(name, loyalty);
        }

        public string SetLoyaltyHostnamesRemoveHostname(string name, string hostname)
        {
            GetLogger().Debug($"Set {name} Loyalty Hostnames Remove Hostname {hostname}");
            _core.GenericOptConfig.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
            if (loyalty == null)
            {
                return $"Unknown Loyalty Scheme {name}";
            }

            IList<string> hostnames = new List<string>(loyalty.Hostnames.Where(x => !x.Equals(hostname)));
            loyalty = new GenericLoyalty(loyalty.Terminal, loyalty.Hosts, hostnames, loyalty.Iins, loyalty.TariffMappings, loyalty.IsPresent);
            return _core.ControllerWorker.SetGenericLoyalty(name, loyalty);
        }

        public string SetLoyaltyIinsAddIin(string name, string low, string high)
        {
            GetLogger().Debug($"Set {name} Loyalty IINs Add IIN {low}{(low == high ? string.Empty : $" to {high}")}");
            _core.GenericOptConfig.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
            if (loyalty == null)
            {
                return $"Unknown Loyalty Scheme {name}";
            }

            IList<LoyaltyIin> iins = new List<LoyaltyIin>(loyalty.Iins)
            {
                new LoyaltyIin(low, high)
            };
            loyalty = new GenericLoyalty(loyalty.Terminal, loyalty.Hosts, loyalty.Hostnames, iins, loyalty.TariffMappings, loyalty.IsPresent);
            return _core.ControllerWorker.SetGenericLoyalty(name, loyalty);
        }

        public string SetLoyaltyIinsRemoveIin(string name, string low, string high)
        {
            GetLogger().Debug($"Set {name} Loyalty IINs Remove IIN {low}{(low == high ? string.Empty : $" to {high}")}");
            _core.GenericOptConfig.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
            if (loyalty == null)
            {
                return $"Unknown Loyalty Scheme {name}";
            }

            IList<LoyaltyIin> iins = new List<LoyaltyIin>(loyalty.Iins.Where(x => !x.Low.Equals(low) || !x.High.Equals(high)));
            loyalty = new GenericLoyalty(loyalty.Terminal, loyalty.Hosts, loyalty.Hostnames, iins, loyalty.TariffMappings, loyalty.IsPresent);
            return _core.ControllerWorker.SetGenericLoyalty(name, loyalty);
        }

        public string SetTariffMappingsAddMapping(byte grade, string productCode, bool fuelCardsOnly)
        {
            GetLogger().Debug($"Set Tariff Mappings Add Mapping {grade} => {productCode}{(fuelCardsOnly ? "Fuel Cards Only" : string.Empty)}");
            GenericOptConfig config = _core.GenericOptConfig;
            IList<TariffMapping> mappings = new List<TariffMapping>(config.TariffMappings)
            {
                new TariffMapping(grade, productCode, fuelCardsOnly)
            };
            return _core.ControllerWorker.SetTariffMappings(mappings);
        }

        public string SetTariffMappingsRemoveMapping(byte grade, string productCode)
        {
            GetLogger().Debug($"Set Tariff Mappings Remove Mapping {grade} => {productCode}");
            GenericOptConfig config = _core.GenericOptConfig;
            IList<TariffMapping> mappings =
                new List<TariffMapping>(config.TariffMappings.Where(x => !x.Grade.Equals(grade) || !x.ProductCode.Equals(productCode)));
            return _core.ControllerWorker.SetTariffMappings(mappings);
        }

        public string SetTariffMappingFuelCardsOnly(byte grade, bool flag)
        {
            GetLogger().Debug($"{(flag ? "Set" : "Clear")} Tariff Mapping Fuel Cards Only for grade {grade}");
            return _core.ControllerWorker.SetTariffMappingFuelCardsOnly(grade, flag);
        }

        public string AddPredefinedAmount(int amount)
        {
            GetLogger().Debug($"Add Predefined Amount {amount}");
            GenericOptConfig config = _core.GenericOptConfig;
            IList<int> amounts = new List<int>(config.PredefinedAmounts)
            {
                amount
            };
            return _core.ControllerWorker.SetPredefinedAmounts(amounts);
        }

        public string RemovePredefinedAmount(int amount)
        {
            GetLogger().Debug($"Remove Predefined Amount {amount}");
            GenericOptConfig config = _core.GenericOptConfig;
            IList<int> amounts = new List<int>(config.PredefinedAmounts.Where(x => x != amount));
            return _core.ControllerWorker.SetPredefinedAmounts(amounts);
        }

        public string SetLoyaltyMappingsAddMapping(string name, string productCode, string loyaltyCode)
        {
            GetLogger().Debug($"Set {name} Loyalty Mappings Add Mapping {productCode} => {loyaltyCode}");
            _core.GenericOptConfig.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
            if (loyalty == null)
            {
                return $"Unknown Loyalty Scheme {name}";
            }

            IList<LoyaltyMapping> mappings = new List<LoyaltyMapping>(loyalty.TariffMappings)
            {
                new LoyaltyMapping(productCode, loyaltyCode)
            };
            loyalty = new GenericLoyalty(loyalty.Terminal, loyalty.Hosts, loyalty.Hostnames, loyalty.Iins, mappings, loyalty.IsPresent);
            return _core.ControllerWorker.SetGenericLoyalty(name, loyalty);
        }

        public string SetLoyaltyMappingsRemoveMapping(string name, string productCode, string loyaltyCode)
        {
            GetLogger().Debug($"Set {name} Loyalty Mappings Remove Mapping {productCode} => {loyaltyCode}");
            _core.GenericOptConfig.LoyaltyList.TryGetValue(name, out GenericLoyalty loyalty);
            if (loyalty == null)
            {
                return $"Unknown Loyalty Scheme {name}";
            }

            IList<LoyaltyMapping> mappings =
                new List<LoyaltyMapping>(loyalty.TariffMappings.Where(x =>
                    !x.ProductCode.Equals(productCode) || !x.LoyaltyCode.Equals(loyaltyCode)));
            loyalty = new GenericLoyalty(loyalty.Terminal, loyalty.Hosts, loyalty.Hostnames, loyalty.Iins, mappings, loyalty.IsPresent);
            return _core.ControllerWorker.SetGenericLoyalty(name, loyalty);
        }

        public string AddWash
            (byte programId, string productCode, string description, float price, float vatRate, short category, short subcategory)
        {
            GetLogger().Debug(
                $"Add Wash: Program Id {programId}, Product Code {productCode}, Description {description}, Price {price}, VAT Rate {vatRate}");
            return _core.ControllerWorker.AddWash(new Wash(programId, productCode, description, $"{price}", $"{vatRate}", category,
                subcategory));
        }

        public string RemoveWashByProgramId(byte programId)
        {
            GetLogger().Debug($"Remove Wash by Program Id {programId}");
            return _core.ControllerWorker.RemoveWashByProgramId(programId);
        }

        public void ReloadOptConfiguration()
        {
            GetLogger().Debug("Reload OPT Configuration");
            _core.ControllerWorker.ReloadOptConfiguration();
        }

        public string PerformDayEnd()
        {
            GetLogger().Debug("Perform Day End");
            return _core.ControllerWorker.PerformDayEnd();
        }

        public string PerformShiftEnd()
        {
            GetLogger().Debug("Perform Shift End");
            return _core.ControllerWorker.PerformShiftEnd();
        }

        public string SetRetalixTransactionFileDirectory(string directory)
        {
            GetLogger().Debug($"Set Retalix Transaction File Directory to {directory}");
            return _core.ControllerWorker.SetRetalixTransactionFileDirectory(directory);
        }

        public string ClearRetalixTransactionFileDirectory()
        {
            GetLogger().Debug("Clear Retalix Transaction File Directory");
            return _core.ControllerWorker.SetRetalixTransactionFileDirectory();
        }

        public string SetTransactionFileDirectory(string directory)
        {
            GetLogger().Debug($"Set Transaction File Directory to {directory}");
            return _core.ControllerWorker.SetTransactionFileDirectory(directory);
        }

        public string SetWhitelistDirectory(string directory)
        {
            GetLogger().Debug($"Set Whitelist Directory to {directory}");
            return _core.ControllerWorker.SetWhitelistDirectory(directory);
        }

        public string SetLayoutDirectory(string directory)
        {
            GetLogger().Debug($"Set Layout Directory to {directory}");
            return _core.ControllerWorker.SetLayoutDirectory(directory);
        }

        public string SetSoftwareDirectory(string directory)
        {
            GetLogger().Debug($"Set Software Directory to {directory}");
            return _core.ControllerWorker.SetSoftwareDirectory(directory);
        }

        public string SetMediaDirectory(string directory)
        {
            GetLogger().Debug($"Set Media Directory to {directory}");
            return _core.ControllerWorker.SetMediaDirectory(directory);
        }

        public string SetPlaylistDirectory(string directory)
        {
            GetLogger().Debug($"Set Playlist Directory to {directory}");
            return _core.ControllerWorker.SetPlaylistDirectory(directory);
        }

        public string SetOptLogFileDirectory(string directory)
        {
            GetLogger().Debug($"Set OPT Log File Directory to {directory}");
            return _core.ControllerWorker.SetOptLogFileDirectory(directory);
        }

        public string SetLogFileDirectory(string directory)
        {
            GetLogger().Debug($"Set Log File Directory to {directory}");
            string result = _core.ControllerWorker.SetLogFileDirectory(directory);
            GetLogger().Info(
                $"Setting Log File Directory to {directory} {(string.IsNullOrEmpty(result) ? "Succeeded" : $"Failed - {result}")}");
            return result;
        }

        public string SetTraceFileDirectory(string directory)
        {
            GetLogger().Debug($"Set Trace File Directory to {directory}");
            return _core.ControllerWorker.SetTraceFileDirectory(directory);
        }

        public string SetJournalFileDirectory(string directory)
        {
            GetLogger().Debug($"Set Journal File Directory to {directory}");
            return _core.ControllerWorker.SetJournalFileDirectory(directory);
        }

        public string SetReceivedUpdateDirectory(string directory)
        {
            GetLogger().Debug($"Set Received Update Directory to {directory}");
            return _core.ControllerWorker.SetReceivedUpdateDirectory(directory);
        }

        public string SetContactlessPropertiesFile(string fileName)
        {
            GetLogger().Debug($"Set Contactless Properties File to {fileName}");
            return _core.ControllerWorker.SetContactlessPropertiesFile(fileName);
        }

        public string SetFuelDataUpdateFile(string fileName)
        {
            GetLogger().Debug($"Set Fuel Data Update File to {fileName}");
            return _core.ControllerWorker.SetFuelDataUpdateFile(fileName);
        }

        public string SetUpgradeFileDirectory(string directory)
        {
            GetLogger().Debug($"Set Upgrade File Directory to {directory}");
            return _core.ControllerWorker.SetUpgradeFileDirectory(directory);
        }

        public string SetRollbackFileDirectory(string directory)
        {
            GetLogger().Debug($"Set Rollback File Directory to {directory}");
            return _core.ControllerWorker.SetRollbackFileDirectory(directory);
        }

        public string SetDatabaseBackupDirectory(string directory)
        {
            GetLogger().Debug($"Set Database Backup Directory to {directory}");
            return _core.ControllerWorker.SetDatabaseBackupDirectory(directory);
        }

        public string SetEsocketConnectionString(string newConnectionString)
        {
            GetLogger().Debug($"Set eSocket.POS Connection String to {newConnectionString}");
            return _core.ControllerWorker.SetEsocketConnectionString(newConnectionString);
        }

        public string SetEsocketUseConnectionString(bool useConnectionString)
        {
            GetLogger().Debug($"Set eSocket.POS {(useConnectionString ? "Connection String" : "Config File")}");
            return _core.ControllerWorker.SetEsocketUseConnectionString(useConnectionString);
        }

        public string SetEsocketConfigFile(string fileName)
        {
            GetLogger().Debug($"Set eSocket.POS Config File to {fileName}");
            return _core.ControllerWorker.SetEsocketConfigFile(fileName);
        }

        public string SetEsocketKeystoreFile(string fileName)
        {
            GetLogger().Debug($"Set eSocket.POS Keystore File to {fileName}");
            return _core.ControllerWorker.SetEsocketKeystoreFile(fileName);
        }

        public string SetEsocketDbUrl(string url)
        {
            GetLogger().Debug($"Set eSocket.POS Database URL to {url}");
            return _core.ControllerWorker.SetEsocketDbUrl(url);
        }

        public string SetEsocketOverrideProperties(bool flag)
        {
            GetLogger().Debug($"Set eSocket.POS {(flag ? "" : "do not ")}override properties");
            return _core.ControllerWorker.SetEsocketOverrideProperties(flag);
        }

        public string SetEsocketOverrideKeystore(bool flag)
        {
            GetLogger().Debug($"Set eSocket.POS {(flag ? "" : "do not ")}override keystore");
            return _core.ControllerWorker.SetEsocketOverrideKeystore(flag);
        }

        public string SetEsocketOverrideUrl(bool flag)
        {
            GetLogger().Debug($"Set eSocket.POS {(flag ? "" : "do not ")}override url");
            return _core.ControllerWorker.SetEsocketOverrideUrl(flag);
        }

        public string SetEsocketOverrideContactless(bool flag)
        {
            GetLogger().Debug($"Set eSocket.POS {(flag ? "" : "do not ")}override contactless properties file");
            return _core.ControllerWorker.SetEsocketOverrideContactless(flag);
        }

        public string SetReceiptLayoutMode(int mode)
        {
            GetLogger().Debug($"Set Receipt Layout Mode to {mode}");
            return _core.ControllerWorker.SetReceiptLayoutMode(mode);
        }

        public string SetSiteName(string name)
        {
            GetLogger().Debug($"Set Site Name to {name}");
            return _core.ControllerWorker.SetSiteName(name);
        }

        public string SetVatNumber(string number)
        {
            GetLogger().Debug($"Set VAT Number to {number}");
            return _core.ControllerWorker.SetVatNumber(number);
        }

        public string SetCurrencyCode(int number)
        {
            GetLogger().Debug($"Set Currency Code to {number}");
            return _core.ControllerWorker.SetCurrencyCode(number);
        }

        public string SetNozzleUpForKioskUse(bool flag)
        {
            GetLogger().Debug($"Set Nozzle Up For Kiosk Use to {flag}");
            return _core.ControllerWorker.SetNozzleUpForKioskUse(flag);
        }

        public string SetUseReplaceNozzleScreen(bool flag)
        {
            GetLogger().Debug($"Set Use Replace Nozzle Screen to {flag}");
            return _core.ControllerWorker.SetUseReplaceNozzleScreen(flag);
        }

        /// <inheritdoc cref="IWeb.GetMaxFillOverride"/>
        public uint GetMaxFillOverride()
        {
            GetLogger().Debug($"Get Max Fill Override");
            return _core.AdvancedConfig.MaxFillOverride;
        }

        public string SetMaxFillOverride(uint maxFillOverride)
        {
            GetLogger().Debug($"Set Max Fill Override to {maxFillOverride}");
            return _core.ControllerWorker.SetMaxFillOverride(maxFillOverride);
        }

        public string AddDiscountCard(string iin, string name, string type, float value, byte grade)
        {
            GetLogger().Debug($"Add Discount Card, IIN {iin}, Name {name}, Type {type}, Value {value}, Grade {grade}");
            return _core.ControllerWorker.AddDiscountCard(iin, name, type, value, grade);
        }

        public string RemoveDiscountCard(string iin)
        {
            GetLogger().Debug($"Remove Discount Card, IIN {iin}");
            return _core.ControllerWorker.RemoveDiscountCard(iin);
        }

        public string AddDiscountWhitelist(string iin, string pan)
        {
            GetLogger().Debug($"Add Discount Whitelist, IIN {iin}, PAN {pan}");
            return _core.ControllerWorker.AddDiscountWhitelist(iin, pan);
        }

        public string RemoveDiscountWhitelist(string iin, string pan)
        {
            GetLogger().Debug($"Remove Discount Whitelist, IIN {iin}, PAN {pan}");
            return _core.ControllerWorker.RemoveDiscountWhitelist(iin, pan);
        }

        public string SetTillNumber(short number)
        {
            GetLogger().Debug($"Set Till Number to {number}");
            return _core.SetTillNumber(number);
        }

        public string SetFuelCategory(short category)
        {
            GetLogger().Debug($"Set Fuel Category to {category}");
            return _core.SetFuelCategory(category);
        }

        public string SetCardReference(string name, int reference)
        {
            GetLogger().Debug($"Set Card Reference for {name} to {reference}");
            return _core.SetCardReference(name, reference);
        }

        public string ClearCardReference(string name)
        {
            GetLogger().Debug($"Clear Card Reference for {name}");
            return _core.ClearCardReference(name);
        }

        public string SetAcquirerReference(string cardName, string acquirerName)
        {
            GetLogger().Debug($"Set Acquirer Reference for {cardName} to {acquirerName}");
            return _core.SetAcquirerReference(cardName, acquirerName);
        }

        public string ClearAcquirerReference(string cardName)
        {
            GetLogger().Debug($"Clear Acquirer Reference for {cardName}");
            return _core.ClearAcquirerReference(cardName);
        }

        public string SetFuelCard(string cardName, bool isFuelCard)
        {
            GetLogger().Debug($"Set Fuel Card flag for {cardName} to {OnOff(isFuelCard)}");
            return _core.SetFuelCard(cardName, isFuelCard);
        }

        public string SetExternalName(string cardName, string externalCardName)
        {
            GetLogger().Debug($"Set External Card name for {cardName} to {externalCardName}");
            return _core.SetExternalName(cardName, externalCardName);
        }

        public string ClearExternalName(string cardName)
        {
            GetLogger().Debug($"Clear External Name for {cardName}");
            return _core.ClearExternalName(cardName);
        }

        public string RemoveLocalAccountCustomer(string customerReference)
        {
            GetLogger().Debug($"Remove Local Account Customer {customerReference}");
            return _core.ControllerWorker.RemoveLocalAccountCustomer(customerReference);
        }

        public string AddLocalAccountCustomer
        (string customerReference, string name, bool transactionsAllowed, uint transactionLimit, bool fuelOnly, bool registrationEntry,
            bool mileageEntry, bool prepayAccount, bool lowCreditWarning, bool maxCreditReached, bool pin, bool printValue,
            bool allowLoyalty)
        {
            GetLogger().Debug($"Add Local Account Customer, Reference {customerReference}, Name {name}");
            return _core.ControllerWorker.AddLocalAccountCustomer(customerReference, name, transactionsAllowed, transactionLimit, fuelOnly,
                registrationEntry, mileageEntry, prepayAccount, lowCreditWarning, maxCreditReached, pin, printValue, allowLoyalty);
        }

        /// <inheritdoc/>
        public StatusCodeResult SetLocalAccountCustomerFlags(LocalAccountCustomerSetFlagsRequest request, string reference = null)
        {
            var result = DoAction(() =>
            {
                var existing = GetLocalAccountCustomers()?.FirstOrDefault(x =>
                    string.Equals(x.CustomerReference, request.CustomerReference, StringComparison.OrdinalIgnoreCase));

                if (existing == null)
                {
                    return Result.Failure($"Invalid local account customer reference: {request.CustomerReference}");
                }

                var dbResult = _hydraDb.UpdateLocalAccountCustomer((LocalAccountCustomerFlags)request, reference.ToMessageTracking());
                if (dbResult.IsSuccess)
                {
                    _controllerWorker.PushChange(EventType.LocalAccountsChanged, existing.CustomerReference);
                }

                DoDeferredLogging(LogLevel.Debug, HeaderParameters, () => new[]
                {
                    $"Name:{existing.Name}, Reference:{existing.CustomerReference}, IsPinRequired:{request.IsPinRequired}, ShouldPrintValue:{request.ShouldPrintValue}, IsLoyaltyAllowed:{request.IsLoyaltyAllowed}"
                }, reference: reference);

                return dbResult;
            }, reference);

            return result.IsSuccess
                ? StatusCodeResult.Specific(HttpStatusCode.OK)
                : StatusCodeResult.Specific(HttpStatusCode.NotFound, new Exception(result.Error));
        }
        
        public string SetLocalAccountCustomerBalance(string customerReference, uint balance)
        {
            GetLogger().Debug($"Set Local Account Customer Balance, Reference {customerReference}, Balance {balance}");
            return _core.ControllerWorker.SetLocalAccountCustomerBalance(customerReference, balance);
        }

        public string AddLocalAccountCardWithoutRestrictions(string customerReference, string pan, string description, float discount)
        {
            GetLogger().Debug(
                $"Add Local Account Card Without Restrictions, Reference {customerReference}, Pan {pan}, Description {description}, Discount {discount}");
            return _core.ControllerWorker.AddLocalAccountCardWithoutRestrictions(customerReference, pan, description, discount);
        }

        public string AddLocalAccountCardWithRestrictions
        (string customerReference, string pan, string description, float discount, bool unleaded, bool diesel, bool lpg, bool lrp,
            bool gasOil, bool adBlue, bool kerosene, bool oil, bool avgas, bool jet, bool mogas, bool valeting, bool otherMotorRelatedGoods,
            bool shopGoods)
        {
            GetLogger().Debug(
                $"Add Local Account Card With Restrictions, Reference {customerReference}, Pan {pan}, Description {description}, Discount {discount}");
            return _core.ControllerWorker.AddLocalAccountCardWithRestrictions(customerReference, pan, description, discount, unleaded,
                diesel, lpg, lrp, gasOil, adBlue, kerosene, oil, avgas, jet, mogas, valeting, otherMotorRelatedGoods, shopGoods);
        }

        public string SetLocalAccountCardHot(string pan, bool hot)
        {
            GetLogger().Debug($"Set Local Account Card {(hot ? "Hot" : "OK")}, Pan {pan}");
            return _core.ControllerWorker.SetLocalAccountCardHot(pan, hot);
        }

        public string RemoveLocalAccountCard(string pan)
        {
            GetLogger().Debug($"Remove Local Account Card, Pan {pan}");
            return _core.ControllerWorker.RemoveLocalAccountCard(pan);
        }

        public string CheckDomsState()
        {
            GetLogger().Debug("Check DOMS State");
            return _core.ControllerWorker.CheckDomsState();
        }

        public string ResetDoms()
        {
            GetLogger().Debug("Reset DOMS");
            return _core.ControllerWorker.ResetDoms();
        }

        public string MasterResetDoms()
        {
            GetLogger().Debug("Master Reset DOMS");
            return _core.ControllerWorker.MasterResetDoms();
        }

        public string ReconnectDoms()
        {
            GetLogger().Debug("Reconnect DOMS");
            return _core.ControllerWorker.ReconnectDoms();
        }

        public string ClearDomsTransaction(byte fpId, int seqNo)
        {
            GetLogger().Debug($"Clear DOMS Transaction FP {fpId}, Seq {seqNo}");
            return _core.ControllerWorker.ClearDomsTransaction(fpId, seqNo);
        }

        public string AuthoriseDoms(byte fpId, uint limit)
        {
            GetLogger().Debug($"Authorise DOMS FP {fpId}, limit {limit}");
            return _core.ControllerWorker.AuthoriseDoms(fpId, limit);
        }

        public string DomsEmergencyStop(byte fpId)
        {
            GetLogger().Debug($"DOMS Emergency Stop FP {fpId}");
            return _core.ControllerWorker.DomsEmergencyStop(fpId);
        }

        public string DomsCancelEmergencyStop(byte fpId)
        {
            GetLogger().Debug($"DOMS Cancel Emergency Stop FP {fpId}");
            return _core.ControllerWorker.DomsCancelEmergencyStop(fpId);
        }

        public string SetFuellingIndefiniteWait(bool flag)
        {
            GetLogger().Debug($"Set Fuelling Indefinite Wait {flag}");
            return _core.ControllerWorker.SetFuellingIndefiniteWait(flag);
        }

        public string SetFuellingWaitMinutes(int minutes)
        {
            GetLogger().Debug($"Set Fuelling Wait Minutes {minutes}");
            return _core.ControllerWorker.SetFuellingWaitMinutes(minutes);
        }

        public string SetFuellingBackoffAuth(int backoff)
        {
            GetLogger().Debug($"Set Fuelling Backoff Auth {backoff}");
            return _core.ControllerWorker.SetFuellingBackoffAuth(backoff);
        }

        public string SetFuellingBackoffPreAuth(int backoff)
        {
            GetLogger().Debug($"Set Fuelling Backoff Pre Auth {backoff}");
            return _core.ControllerWorker.SetFuellingBackoffPreAuth(backoff);
        }

        public string SetFuellingBackoffStopStart(int backoff)
        {
            GetLogger().Debug($"Set Fuelling Backoff Stop Start {backoff}");
            return _core.ControllerWorker.SetFuellingBackoffStopStart(backoff);
        }

        public string SetFuellingBackoffStopOnly(int backoff)
        {
            GetLogger().Debug($"Set Fuelling Backoff Stop Only {backoff}");
            return _core.ControllerWorker.SetFuellingBackoffStopOnly(backoff);
        }

        public string SetPosClaimNumber(byte number)
        {
            GetLogger().Debug($"Set POS Claim Number {number}");
            return _core.ControllerWorker.SetPosClaimNumber(number);
        }

        public string SetFilePruneDays(int days)
        {
            GetLogger().Debug($"Set File Prune Days {days}");
            return _core.SetFilePruneDays(days);
        }

        public string SetTransactionPruneDays(int days)
        {
            GetLogger().Debug($"Set Transaction Prune Days {days}");
            return _core.SetTransactionPruneDays(days);
        }

        public string SetReceiptPruneDays(int days)
        {
            GetLogger().Debug($"Set Receipt Prune Days {days}");
            return _core.SetReceiptPruneDays(days);
        }

        public string CheckPassword(string username, string password)
        {
            GetLogger().Debug("Check Password");
            return _core.ControllerWorker.CheckPassword(username, password);
        }

        /// <inheritdoc />
        public void SendChatMessage(string name, string message)
        {
            _hubContext.Clients.All.addMessage(name, message);
        }

        /// <summary>
        /// Gets the ShiftEnd details
        /// </summary>
        /// <returns>The ShiftEnd details object</returns>
        public ShiftEndDetails GetShiftEndDetails()
        {
            GetLogger().Debug($"Getting ShiftEnd details");
            return new ShiftEndDetails
            {
                ShiftEndTime = GetShiftEndTime(),
                DayEndTime = GetDayEndTime(),
                NextDayEnd = GetNextDayEnd(),
                LogInterval = GetLogInterval(),
                Unmanned = GetUnmanned(),
                AsdaDayEndReport = GetAsdaDayEndReport(),
                PrinterDetails = GetPrinterDetails()
            };
        }

        /// <inheritdoc />
        public Result SetIntegrationType(IntegrationType integrationType, string value)
        {
            return DoAction(() =>
            {
                var result = integrationType switch
                {
                    IntegrationType.Site => _core.ControllerWorker.SetSiteType(value, LoggingReference),
                    IntegrationType.Pos => _core.ControllerWorker.SetPosType(value, LoggingReference),
                    IntegrationType.Pump => _core.ControllerWorker.SetPumpType(value, LoggingReference),
                    IntegrationType.MobilePayment => _core.ControllerWorker.SetMobilePaymentType(value, LoggingReference),
                    IntegrationType.MobilePos => _core.ControllerWorker.SetMobilePosType(value, LoggingReference),
                    IntegrationType.BackOffice => _core.ControllerWorker.SetBosType(value, LoggingReference),
                    IntegrationType.SecAuth => _core.ControllerWorker.SetSecAuthType(value, LoggingReference),
                    IntegrationType.PaymentConfig => _core.ControllerWorker.SetPaymentConfigType(value, LoggingReference),
                    _ => Result.Failure($"Un-processed IntegrationType: {integrationType}")
                };

                if (result.IsSuccess)
                {
                    SendChatMessage(nameof(Web), $"Set {integrationType} Type {value}");
                }

                return result;
            }, null);
        }

        /// <inheritdoc />
        public bool IsPumpControllerEnabled()
        {
            return DoAction<bool>(() => Result.Success(_core.IsPumpControllerEnabled()), null).Value;
        }

        /// <inheritdoc />
        public string SetLocalAccountsEnabled(bool enabled)
        {
            GetLogger().Debug($"Set local accounts enabled to {enabled}");

            return _core.ControllerWorker.SetLocalAccountsEnabled(enabled);
        }

        /// <summary>
        /// Retrieves the categories configuration
        /// </summary>
        /// <returns>List of categories</returns>
        private IList<CategoryConfiguration> GetCategoriesConfiguration()
        {
            var categories = _core?.GetCategoriesConfiguration.OrderBy(x => x.Category).ToList();

            if (categories == null)
            {
                GetLogger().Warn(HeaderException, () => new[] {"Configuration categories are empty"});
                return null;
            }

            return categories;
        }

        /// <inheritdoc cref="IWeb.SetCategories(IEnumerable{CategoryConfiguration})"/>
        public Result SetCategories(IEnumerable<CategoryConfiguration> categories)
        {
            GetLogger().Debug($"Set Categories {categories}");

            var result = _core.SetCategories(categories);
            if (result.IsSuccess)
            {
                ControllerWorkerOnPushChangeEvent(EventType.AdvancedConfigChanged);
            }            

            return result;
        }

        /// <inheritdoc />
        protected override void DoDisposeUnHookInstances()
        {
            _core = null;
            _integratorFactories = null;

            base.DoDisposeUnHookInstances();
        }

        /// <inheritdoc />
        public Result<StatusCodeResult> ConnectionsDetailsHttp()
        {
            var result = ConnectionsDetails();

            return Result.Success(result == null ?
                StatusCodeResult.Specific(HttpStatusCode.NotFound) :
                StatusCodeResult<ConnectionDetails>.Specific(HttpStatusCode.OK, result));
        }

        public StatusCodeResult<configType> GetOptSentConfig(string idString = null, string reference = null)
        {
            var result = DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Debug, HeaderParameters, () => new[] { $"idString: {idString}" }, reference: reference);

                var opt = _core.AllOpts.AllOpts.FirstOrDefault(x => x.IdString.Equals(idString));
                if (opt == null)
                {
                    return Result.Failure<configType>($"Invalid IdString: {idString}");
                }

                return Result.Success(opt.SentConfig);
            }, reference);

            return !result.IsSuccess ?
                StatusCodeResult<configType>.Specific(HttpStatusCode.NotFound) :
                StatusCodeResult<configType>.Success(result.Value);
        }

        public StatusCodeResult SendOptUnBlock(string idString = null, string reference = null)
        {
            var result = DoAction(() =>
            {
                DoDeferredLogging(LogLevel.Debug, HeaderParameters, () => new[] { $"idString: {idString}" }, reference: reference);

                var opt = _core.AllOpts.AllOpts.FirstOrDefault(x => x.IdString.Equals(idString));
                if (opt == null)
                {
                    return Result.Failure<configType>($"Invalid IdString: {idString}");
                }

                var send = opt.AddNotification(new OptUnblockNotificationRequest());
                if (send)
                {
                    GetWorker<IToOptWorker>().SendCurrentNotificationToOpt(opt, LoggingReference.ToMessageTracking());
                }

                return Result.Success();
            }, reference);

            return !result.IsSuccess ?
                StatusCodeResult.Specific(HttpStatusCode.NotFound) :
                StatusCodeResult.Success;
        }

        private StatusCodeResult<object> ExtractConfiguration()
        {
            var result = DoAction(() =>
            {
                var serviceFilesHelper = ServiceFilesHelper;
                var advancedConfigDetails = GetAdvancedConfig();
                var genericOPTConfigDetails = GetGenericOptConfig();
                var connectionDetails = ConnectionsDetails();
                var optDetails = GetOpts();
                var pumpDetails = GetPumps();
                var fuelPrices = GetPrices();

                var configData = new
                {
                    SiteInfo = _hydraDb.GetSiteInfo(),
                    VersionInfo = new
                    {
                        current = serviceFilesHelper.Current,
                        rollback = serviceFilesHelper.Rollback,
                        upgrade = serviceFilesHelper.Upgrade,
                        pumpIntegrator = serviceFilesHelper.PumpIntegrator,
                        offlineFileService = serviceFilesHelper.OfflineFileService
                    },
                    OPTs = optDetails.Results,
                    Pumps = pumpDetails.Results,
                    FuelPrices = fuelPrices,
                    Connections = connectionDetails,
                    AdvancedConfig = _hydraDb.AdvancedConfig,
                    BOSConfig = _hydraDb.BosConfig,
                    DOMSInfo = _hydraDb.GetDomsInfo(),
                    FileLocations = _hydraDb.GetFileLocations(),
                    PrinterConfig = _hydraDb.GetPrinterConfig(),
                    GenericOPTConfigDetails = genericOPTConfigDetails,
                    AdvancedConfigDetails = advancedConfigDetails
                };
                 return Result.Success(configData);
            }, null
            );

            return !result.IsSuccess ?
                StatusCodeResult<object>.Specific(HttpStatusCode.NoContent) :
                StatusCodeResult<object>.Success(result.Value);
        }
    }
}
