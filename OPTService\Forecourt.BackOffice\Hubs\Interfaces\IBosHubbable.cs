﻿using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Web.Hubs.Interfaces;
using Htec.Hydra.Core.Bos.Messages;

namespace Forecourt.Bos.Hubs.Interfaces
{
    /// <inheritdoc/>
    public interface IBosHubbable: IHubbable<BosMessage, IMessageTracking>, IBosHubbableIn
    {
    }

    /// <inheritdoc/>
    public interface IBosHubbableIn : IHubbableIn<BosMessage, IMessageTracking>
    {
    }
}
