﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;

namespace Forecourt.BackOffice.Models.EvoBook
{
    /// <summary>
    /// Represents an individual sale line item with pricing, tax, and promotion details for EvoBook transactions
    /// </summary>
    public class SaleLine
    {
        /// <summary>
        /// Sequential number identifying this line item within the transaction
        /// </summary>
        [JsonProperty("lineItemSequenceNumber")]
        public int LineItemSequenceNumber { get; set; }
        /// <summary>
        /// Indicates whether this sale line has been voided
        /// </summary>
        [JsonProperty("voidedFlag")]
        public bool VoidedFlag { get; set; }
        /// <summary>
        /// Timestamp when the sale line item processing started
        /// </summary>
        [JsonProperty("startTime")]
        public string StartTime { get; set; }
        /// <summary>
        /// Timestamp when the sale line item processing completed
        /// </summary>
        [JsonProperty("endTime")]
        public string EndTime { get; set; }
        /// <summary>
        /// Indicates whether this line item requires fiscal receipt printing
        /// </summary>
        [JsonProperty("fiscalReceipt")]
        public bool FiscalReceipt { get; set; }
        /// <summary>
        /// JSON object containing the sale item details and metadata
        /// </summary>
        [JsonProperty("saleItem")]
        public JObject SaleItem { get; set; }
        /// <summary>
        /// Quantity of the item being sold
        /// </summary>
        [JsonProperty("saleQuantity")]
        public double SaleQuantity { get; set; }
        /// <summary>
        /// Amount eligible for promotional discounts
        /// </summary>
        [JsonProperty("promotionEligible")]
        public double PromotionEligible { get; set; }
        /// <summary>
        /// Total promotional discount amount applied to this line
        /// </summary>
        [JsonProperty("promotionApplied")]
        public double PromotionApplied { get; set; }
        /// <summary>
        /// Final sale amount after all discounts and adjustments
        /// </summary>
        [JsonProperty("saleAmount")]
        public double SaleAmount { get; set; }
        /// <summary>
        /// Tax amount calculated for this sale line item
        /// </summary>
        [JsonProperty("saleTaxAmount")]
        public double SaleTaxAmount { get; set; }
        /// <summary>
        /// Original sale amount before any discounts or modifications
        /// </summary>
        [JsonProperty("originalSaleAmount")]
        public double OriginalSaleAmount { get; set; }
        /// <summary>
        /// Indicates whether this is a return transaction for the item
        /// </summary>
        [JsonProperty("itemReturn")]
        public bool ItemReturn { get; set; }
        /// <summary>
        /// Collection of price or system overrides applied to this line
        /// </summary>
        [JsonProperty("overrides")]
        public List<string> Overrides { get; set; }
        /// <summary>
        /// Collection of discount modifiers applied to this sale line
        /// </summary>
        [JsonProperty("discountModifiers")]
        public List<string> DiscountModifiers { get; set; }
        /// <summary>
        /// Collection of promotional modifiers applied to this sale line
        /// </summary>
        [JsonProperty("promotionModifiers")]
        public List<string> PromotionModifiers { get; set; }
        /// <summary>
        /// Collection of price change modifiers applied to this sale line
        /// </summary>
        [JsonProperty("priceChangeModifiers")]
        public List<string> PriceChangeModifiers { get; set; }
        /// <summary>
        /// Fuel sale details if this line item represents a fuel transaction
        /// </summary>
        [JsonProperty("fuelSale")]
        public FuelSale FuelSale { get; set; }
    }
}
