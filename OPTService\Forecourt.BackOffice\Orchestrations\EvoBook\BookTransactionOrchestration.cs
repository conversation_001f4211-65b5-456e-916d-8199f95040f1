﻿using CSharpFunctionalExtensions;
using Forecourt.BackOffice.Models;
using Forecourt.BackOffice.Models.EvoBook;
using Forecourt.BackOffice.Orchestrations.Interfaces;
using Forecourt.Bos.Configuration.EvoBook;
using Forecourt.Bos.HydraDb.Interfaces;
using Forecourt.Core.Helpers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Net.Http.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Foundation.Orchestrations;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Bos.Messages;
using Htec.Logger.Interfaces;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Forecourt.BackOffice.Orchestrations.EvoBook
{
    /// <summary>
    /// Orchestration that manages the process of booking a transaction to an external system
    /// </summary>
    /// <inheritdoc/>
    [HasConfiguration]
    public class BookTransactionOrchestration : OrderedOrchestration<BookTransactionModel, StatusCodeResult>, IBookTransactionOrchestration
    {
        private const string IgnoringCancelledZeroCashItem = "Ignoring Cancelled/Zero Cash Item";

        /// <summary>
        /// <see cref="IHydraDb"/> instance
        /// </summary>
        protected IHydraDb HydraDb { get; set; }

        /// <summary>
        /// Factory instance (<see cref="IHttpClientFactory"/>), for managing HttpClient instances
        /// </summary>
        private IHttpClientFactory _httpClientFactory { get; set; }

        private readonly IBosIntegratorInTransient<IMessageTracking, Result<StatusCodeResult>> _bosIntegratorInTransient;

        private readonly ConcurrentDictionary<long, (bool, DateTime)> _activeRuns = new();

        private readonly INotificationWorker<string> _notificationWorker;

        private readonly IVatCalculator _vatCalculator;

        private readonly object _syncLock = new();

        private IDictionary<int, DateTime> _nextSeqNumbers = new ConcurrentDictionary<int, DateTime>();

        private readonly object _syncLockNextSeqNumber = new();

        /// <inheritdoc/>
        public BookTransactionOrchestration(IHtecLogManager logManager, string loggerName, IConfigurationManager configurationManager, IHydraDb hydraDb,
            IBosIntegratorInTransient<IMessageTracking, Result<StatusCodeResult>> bosIntegratorInTransient, IHttpClientFactory httpClientFactory,
            INotificationWorker<string> notificationWorker, IVatCalculator vatCalculator) : base(logManager, loggerName, configurationManager)
        {
            HydraDb = hydraDb ?? throw new ArgumentNullException(nameof(hydraDb));
            _bosIntegratorInTransient = bosIntegratorInTransient ?? throw new ArgumentException(nameof(bosIntegratorInTransient));
            _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
            _notificationWorker = notificationWorker ?? throw new ArgumentNullException(nameof(notificationWorker));
            _vatCalculator = vatCalculator ?? throw new ArgumentNullException(nameof(vatCalculator));
        }

        /// <inheritdoc/>
        public async Task<StatusCodeResult> RunOrchestration(SendTransactionItem item, IOrchestratedCommand configDataMap, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            lock (_syncLock)
            {
                var isActive = _activeRuns.TryGetValue(item.Id, out var ar) && ar.Item1;
                if (isActive)
                {
                    var msg = $"{item.Id}; Started: {ar.Item2:dd/MM/yyyy HH:mm:ss.fff}";
                    DoDeferredLogging(LogLevel.Warn, "AlreadyActive.Item", () => new[] { msg }, reference: message.FullId);
                    return StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception($"Orchestration Already Active, ItemId: {msg}"));
                }

                _activeRuns[item.Id] = (true, DateTime.Now);
            }

            try
            {
                return await DoRunOrchestration(item, configDataMap, message);
            }
            finally
            {
                lock (_syncLock)
                {
                    _activeRuns.TryRemove(item.Id, out var itemRemoved);
                }
            }
        }

        private async Task<StatusCodeResult> DoRunOrchestration(SendTransactionItem item, IOrchestratedCommand configDataMap, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            var model = new BookTransactionModel
            {
                Item = item,
                ConfigDataMap = configDataMap,
                MessageTracking = message,
            };

            var mapping = (HydraToEvoBookMapping)model.ConfigDataMap;

            if (mapping == null || mapping.BosConfig == null)
            {
                string errorMessage = "BOS configuration not available";
                DoDeferredLogging(LogLevel.Warn, "MappingData", () => new[] { errorMessage }, null, message.IdAsString);
                return StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception(errorMessage));
            }

            var info = $"{item.Id}; TxnNumber: {item.Number}";
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "TransactionId", () => new[] { $"{info}; SendTransactionItem: {JsonConvert.SerializeObject(item)}" }, reference: message.IdAsString);

            var result = await ProcessSteps(model, checkFunction: (BookTransactionModel model, StatusCodeResult result) => { return result.IsSuccess; }).ConfigureAwait(false);

            if (result.IsSuccess || !mapping.BosConfig.BookTransactionIgnoreInvalid || result.Exception?.Message != IgnoringCancelledZeroCashItem)
            {
                DoDeferredLogging(LogLevel.Info, "Result.TransactionId", () => new[] { $"{info}; Result: {result.IsSuccess}; Step: {model.StepName}; {(result.IsSuccess ? string.Empty : $"Error: {result.Exception?.Message ?? "n/a"}")}" }, reference: message.IdAsString);
            }

            return result;
        }

        private async Task<StatusCodeResult> GetXxxClientHttp(BookTransactionModel model)
        {
            model.StepName = nameof(GetXxxClientHttp);
            model.ConfigHttpClient = _httpClientFactory.GetHttpClient();
            model.BosHttpClient = _httpClientFactory.GetHttpClient();

            return StatusCodeResult.Success;
        }

        /// <summary>
        /// Gets the transaction booking using HydraDb
        /// </summary>
        /// <param name="model">Book transaction model</param>
        /// <returns>Status code result</returns>
        private async Task<StatusCodeResult> GetTransactionBooking(BookTransactionModel model)
        {
            model.StepName = nameof(GetTransactionBooking);
            StatusCodeResult result;

            var booking = HydraDb.GetTransactionBooking(transId: model.Item.Id, txnNumber: model.Item.Number);

            if (booking.IsSuccess)
            {
                model.BookingState = booking.Value;
                model.BookingId = booking.Value.Id;
                model.Item.TillNumber = HydraDb.GetSiteInfo().TillNumber;

                if (int.TryParse(booking.Value.ExternalTransactionId, out int externalTransactionId))
                {
                    model.ExternalTransactionId = externalTransactionId;
                }

                if (model.ExternalTransactionId > 0)
                {
                    model.BusinessDate = booking.Value.BusinessDate.ToString("yyyy-MM-ddT00:00:00");
                    model.ShiftId = booking.Value.ShiftId;
                    model.PeriodId = booking.Value.PeriodId;
                }

                model.RetryCount = booking.Value.RetryCount;
                result = StatusCodeResult.Success;
            }
            else
            {
                DoDeferredLogging(LogLevel.Warn, HeaderException, () => new[] { $"Transaction booking state not found. Id: {model.Item.Id}, Number: {model.Item.Number}" }, null, model.LoggingReference);
                result = StatusCodeResult.Specific(HttpStatusCode.NotFound);
            }

            return result;
        }

        private bool IsSendTransactionItemValid(SendTransactionItem x)
        {
            return x != null &&
                x.Id > 0 &&
                !x.Number.IsNullOrWhiteSpace() &&
                x.Grade > 0 &&
                !x.GradeName.IsNullOrWhiteSpace() &&
                x.Volume > 0 &&
                x.AuthAmount > 0;
        }

        private async Task<StatusCodeResult> ValidateTransactionItem(BookTransactionModel model)
        {
            model.StepName = nameof(ValidateTransactionItem);

            var mapping = (HydraToEvoBookMapping)model.ConfigDataMap;
            var item = model.Item;

            var isValid = IsSendTransactionItemValid(model.Item);
            if (!isValid && mapping.BosConfig.BookTransactionIgnoreInvalid)
            {
                var booking = model.BookingState;
                var result = HydraDb.CompleteTransactionBooking(model.BookingId, model.Item.Id, DateTime.UtcNow);
                if (!result.IsSuccess)
                {
                    return StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception(result.Error));
                }

                DoDeferredLogging(LogLevel.Info, "Cancelled/ZeroCash.Transaction.Pseudo.Booked", () => new[] { $"TransId: {booking.TransactionId}; TxnNumber: {booking.TxnNumber}; Timestamp: {booking.TransactionDate}; TransId (External): {model.ExternalTransactionId};" });
                _notificationWorker.SendInformation($"Cancelled/Zero Cash Transaction (pseudo) Booked into External System: TransId: {booking.TransactionId}; TxnNumber: {booking.TxnNumber}; Timestamp: {booking.TransactionDate}; TransId (External): {model.ExternalTransactionId};");
                return StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception(IgnoringCancelledZeroCashItem));
            }

            if (item.TransactionStart == DateTime.MinValue)
            {
                item.TransactionStart = DateTime.Now;
            }

            if (item.TransactionFinish == DateTime.MinValue)
            {
                item.TransactionFinish = item.TransactionStart.AddSeconds(1);
            }

            if (item.ProductCode.IsNullOrWhiteSpace())
            {
                item.ProductCode = string.Empty;
            }

            if (item.GradeName.IsNullOrWhiteSpace())
            {
                item.GradeName = string.Empty;
            }

            return StatusCodeResult.Success;
        }

        /// <summary>
        /// Updates the transaction booking for the current SendTransactionItem, for possible later use in managing Offline Bookings
        /// </summary>
        /// <param name="model">Book transaction model</param>
        /// <returns>Status code result</returns>
        private async Task<StatusCodeResult> StoreSendTransactionItem(BookTransactionModel model)
        {
            model.StepName = nameof(StoreSendTransactionItem);

            var booking = model.BookingState;
            if (booking.SendTransactionItem.IsNullOrWhiteSpace() && model.Item != null)
            {
                booking.SendTransactionItem = JsonConvert.SerializeObject(model.Item);
                HydraDb.UpdateTransactionBooking(booking.Id, booking.TransactionId, model.Item);
            }

            return StatusCodeResult.Success;
        }

        /// <summary>
        /// Checks whether EvoBook is reachable
        /// </summary>
        /// <param name="model">Book transaction model</param>
        /// <returns>Status code result</returns>
        private async Task<StatusCodeResult> IsEvoBookAvailable(BookTransactionModel model)
        {
            model.StepName = nameof(IsEvoBookAvailable);

            var mapping = (HydraToEvoBookMapping)model.ConfigDataMap;

            var apiUrl = $"{mapping.EndPointsConfig.ApiBos}/v1/Ping";
            var result = await MakeApiCall<string>(async () => await model.BosHttpClient.GetAsync(apiUrl), model.LoggingReference);
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "Response", () => new[] { $"{result.Results}" }, null, model.LoggingReference);

            model.IsApiAvailable = result.IsSuccess;
            return result;
        }

        private async Task<StatusCodeResult> GetTransactionId(BookTransactionModel model)
        {
            model.StepName = nameof(GetTransactionId);

            if (model.ExternalTransactionId > 0 && model.ShiftId > 0 && model.PeriodId > 0)
            {
                // Already have a sequence number so can skip this step.
                return StatusCodeResult.Success;
            }

            var mapping = (HydraToEvoBookMapping)model.ConfigDataMap;

            var apiUrl = $"{mapping.EndPointsConfig.ApiConfig}/v1/SequenceNumber?company={mapping.BosConfig.CompanyId}&store={mapping.BosConfig.StoreId}&workstation={model.Item.TillNumber}";
            var result = await MakeApiCall<MadicSequenceNumber>(async () => await model.ConfigHttpClient.GetAsync(apiUrl), model.LoggingReference);

            if (result.IsSuccess && result.Results != null)
            {
                var madicSequenceNumberModel = result.Results;

                DoDeferredLogging(LogLevel.Info, "SequenceNumber", () => new[] { $"Company Id: {madicSequenceNumberModel.CompanyId}"
                    + $", Store Id: {madicSequenceNumberModel.StoreId}, Workstation Id: {madicSequenceNumberModel.WorkstationId}"
                    + $", Next Sequence Number: {madicSequenceNumberModel.NextSequenceNumber}"
                    + $", Shift Id: {madicSequenceNumberModel.ShiftId}, Period Id: {madicSequenceNumberModel.PeriodId}"
                    + $", Current Business Day {madicSequenceNumberModel.CurrentBusinessDay}" }, null, model.LoggingReference);

                if (!(madicSequenceNumberModel.CompanyId == mapping.BosConfig.CompanyId && madicSequenceNumberModel.StoreId == mapping.BosConfig.StoreId
                    && madicSequenceNumberModel.WorkstationId == model.Item.TillNumber))
                {
                    string message = $"Response does not match expected values. Company Id: {mapping.BosConfig.CompanyId}, Store Id: {mapping.BosConfig.StoreId}, Workstation Id: {model.Item.TillNumber}";
                    DoDeferredLogging(LogLevel.Warn, HeaderException, () => new[] { message }, null, model.LoggingReference);
                    result = StatusCodeResult<MadicSequenceNumber>.Specific(HttpStatusCode.BadRequest, null, new Exception(message));
                }
                else
                {
                    lock (_syncLockNextSeqNumber)
                    {
                        if (!_nextSeqNumbers.Keys.Contains(madicSequenceNumberModel.NextSequenceNumber))
                        {
                            _nextSeqNumbers[madicSequenceNumberModel.NextSequenceNumber] = DateTime.UtcNow;
                        }
                        else
                        {
                            var info = $"Workstation Id: {madicSequenceNumberModel.WorkstationId}; SequenceNumber: {madicSequenceNumberModel.NextSequenceNumber}";
                            DoDeferredLogging(LogLevel.Warn, "Duplicate.EvoSequenceNumber", () => new[] { info }, null, model.LoggingReference);
                            result = StatusCodeResult<MadicSequenceNumber>.Specific(HttpStatusCode.BadRequest, null, new Exception($"Duplicate Evo Sequence Number encountered: {info}"));
                        }

                        // Cleardown
                        var ticks = mapping.BosConfig.BookTransactionSeqNumberDuplicateCheckInterval.Ticks;
                        foreach(var key in _nextSeqNumbers.Keys.ToList())
                        {
                            var expiry = _nextSeqNumbers[key].AddTicks(ticks);

                            if (expiry < DateTime.UtcNow)
                            {
                                _nextSeqNumbers.Remove(key);
                            }
                        }
                    }
                }

                if (result.IsSuccess)
                {
                    model.ExternalTransactionId = madicSequenceNumberModel.NextSequenceNumber;
                    model.ShiftId = madicSequenceNumberModel.ShiftId;
                    model.PeriodId = madicSequenceNumberModel.PeriodId;
                    model.BusinessDate = madicSequenceNumberModel.CurrentBusinessDay;

                    DateTime businessDate = DateTime.Parse(model.BusinessDate);

                    HydraDb.UpdateTransactionBooking(model.BookingId, model.Item.Id, model.ExternalTransactionId.ToString(), model.ShiftId, model.PeriodId, businessDate);
                }
            }

            if (!result.IsSuccess)
            {
                model.RetryCount++;
                var resultUpdate = HydraDb.UpdateTransactionBookingHttp(model.BookingId, model.Item.Id, result.StatusCode, result.Exception != null ? result.Exception.Message : string.Empty,
                    model.RetryCount, DateTime.UtcNow.AddSeconds(HydraDb.BosConfig.BookTransactionRetryInterval.TotalSeconds));
                var booking = model.BookingState;
                _notificationWorker.SendInformation($"Unable to Book Transaction into External System: TransId: {booking.TransactionId}; TxnNumber: {booking.TxnNumber}; Timestamp: {booking.TransactionDate}; RetryCount: {model.RetryCount}");
                var scResult = resultUpdate.Value;
                if (!scResult.IsSuccess)
                {
                    return StatusCodeResult.Specific(HttpStatusCode.BadRequest, scResult.Exception);
                }
            }

            return result;
        }

        /// <summary>
        /// Construct the booking content
        /// </summary>
        /// <param name="model">Transaction booking model</param>
        /// <returns></returns>
        private async Task<StatusCodeResult> ConstructBookingDetails(BookTransactionModel model)
        {
            model.StepName = nameof(ConstructBookingDetails);

            var mapping = (HydraToEvoBookMapping)model.ConfigDataMap;

            if (mapping == null || mapping.BosConfig == null || mapping.HydraGrades == null || mapping.HydraVatRates == null || mapping.HydraCardReferences == null
                || mapping.ExternalGrades == null || mapping.ExternalVatRates == null || mapping.ExternalCardReferences == null)
            {
                string errorMessage = "Hydra to MADIC EvoBook configuration mapping data not available";
                DoDeferredLogging(LogLevel.Warn, HeaderException, () => new[] { errorMessage }, null, model.LoggingReference);
                return StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception(errorMessage));
            }

            var mapGrade = mapping.HydraGrades.Map.ContainsKey(model.Item.Grade) ? mapping.HydraGrades.Map[model.Item.Grade] : null;
            var hydraGrade = mapGrade?.Source ?? mapping.HydraGrades.DefaultSource;
            var madicGrade = mapGrade?.Target ?? mapping.HydraGrades.DefaultTarget;

            var mapVatRate = mapping.HydraVatRates.Map[hydraGrade?.VatRateId];
            var hydraVatRate = mapVatRate?.Source ?? mapping.HydraVatRates.DefaultSource;
            var madicTaxLevel = mapVatRate?.Target ?? mapping.HydraVatRates.DefaultTarget;

            var cardReference = mapping.HydraData.CardReferences.FirstOrDefault(x => model.Item.CardProductName != null && (x.CardProductName?.Contains(model.Item.CardProductName, StringComparison.InvariantCultureIgnoreCase) ?? false));
            var madicTender = cardReference != null && mapping.HydraCardReferences.Map.ContainsKey(cardReference.CardRef) ? 
                mapping.HydraCardReferences.Map[cardReference.CardRef].Target : 
                mapping.ExternalCardReferences.DefaultSource;

            if (cardReference == null)
            {
                return StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception($"No Hydra card reference defined: {model.Item.CardProductName}"));
            }

            if (hydraVatRate == null)
            {
                return StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception($"No Hydra vat rate defined: {hydraGrade?.VatRateId}"));
            }

            if (madicGrade == null)
            {
                return StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception($"No MADIC grade mapped: {model.Item.Grade}"));
            }

            if (madicTaxLevel == null)
            {
                return StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception($"No MADIC tax level mapped: {hydraGrade?.VatRateId}"));
            }

            if (madicTender == null)
            {
                return StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception($"No MADIC tender mapped: {model.Item.CardProductName}"));
            }

            // Check the MADIC VAT rate for the grade matches. 
            if (Math.Round(madicTaxLevel.TaxRate, 2) != Math.Round(hydraVatRate.Rate, 2))
            {
                return StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception($"Mismatch with tax rate. Hydra: {hydraVatRate?.Id}, MADIC {madicTaxLevel.TaxRate}"));
            }

            var saleTransaction = MadicSaleTransaction.MapFromBookTransactionModel(model, mapping.BosConfig, cardReference, madicGrade, madicTaxLevel, madicTender, _vatCalculator);

            model.BookingContent = JsonConvert.SerializeObject(saleTransaction);
            return StatusCodeResult.Success;
        }

        /// <summary>
        /// Post the transaction to EvoBook
        /// </summary>
        /// <param name="model">Transaction booking model</param>
        /// <returns></returns>
        private async Task<StatusCodeResult> RecordBookingDetails(BookTransactionModel model)
        {
            model.StepName = nameof(RecordBookingDetails);

            var mapping = (HydraToEvoBookMapping)model.ConfigDataMap;

            var apiUrl = $"{mapping.EndPointsConfig.ApiBos}/v1/Book/Sale?company={mapping.BosConfig.CompanyId}&store={mapping.BosConfig.StoreId}&workstation={model.Item.TillNumber}";

            var result = await MakeApiCall<string>(async () => await model.BosHttpClient.PostAsync(apiUrl, ToStringContent(model.BookingContent, Encoding.UTF8)), model.LoggingReference);

            var info = $"{model.Item.Id}; TxnNumber: {model.Item.Number}";
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "Response.TransactionId", () => new[] { $"{info}; Response: {result.IsSuccess}/{(!result.IsSuccess ? string.Empty : result.Results)}; Content: {model.BookingContent}" }, null, model.LoggingReference);

            var booking = model.BookingState;
            if (result.IsSuccess)
            {
                HydraDb.CompleteTransactionBooking(model.BookingId, model.Item.Id, DateTime.UtcNow);
                _notificationWorker.SendInformation($"Transaction Booked into External System: TransId: {booking.TransactionId}; TxnNumber: {booking.TxnNumber}; Timestamp: {booking.TransactionDate}; TransId (External): {model.ExternalTransactionId}; BusinessDay: {model.BusinessDate:dd/MM/yyyy}");
                return StatusCodeResult.Success;
            }
            else
            {
                model.RetryCount++;
                HydraDb.UpdateTransactionBookingHttp(model.BookingId, model.Item.Id, result.StatusCode, result.Exception != null ? result.Exception.Message : string.Empty,
                    model.RetryCount, DateTime.UtcNow.AddSeconds(HydraDb.BosConfig.BookTransactionRetryInterval.TotalSeconds));
                _notificationWorker.SendInformation($"Unable to Book Transaction into External System: TransId: {booking.TransactionId}; TxnNumber: {booking.TxnNumber}; Timestamp: {booking.TransactionDate}; RetryCount: {model.RetryCount}");
                return StatusCodeResult.Specific(result.StatusCode, result.Exception);
            }
        }

        /// <summary>
        /// Gets the receipt lines for a transaction.
        /// </summary>
        /// <param name="model">Transaction booking model</param>
        private async Task<StatusCodeResult> GetReceiptLines(BookTransactionModel model)
        {
            model.StepName = nameof(GetReceiptLines);

            var item = model.Item;
            var result = _bosIntegratorInTransient.RequestTransaction(new IdInfo() { Id = item.Id, Number = item.Number }, model.LoggingReference.ToMessageTracking());

            var receiptTextLines = new List<string>();
            if (result.IsSuccess && result.Value.IsSuccess)
            {
                var transactionItem = ((StatusCodeResult<TransactionItem>)result.Value).Results;
                receiptTextLines = (transactionItem.ReceiptContents?.Split(new[] { Environment.NewLine }, StringSplitOptions.None) ?? new[] { "No OPT Receipt" }).ToList();
            }

            model.ReceiptLines = receiptTextLines;
            return StatusCodeResult.Success;
        }

        /// <inheritdoc/>
        protected override IEnumerable<Func<BookTransactionModel, Task<StatusCodeResult>>> GetSteps()
        {
            return new List<Func<BookTransactionModel, Task<StatusCodeResult>>>()
            {
                GetXxxClientHttp,
                GetTransactionBooking,
                ValidateTransactionItem,
                StoreSendTransactionItem,
                IsEvoBookAvailable,
                GetTransactionId,
                GetReceiptLines,
                ConstructBookingDetails,
                RecordBookingDetails
            };
        }

        // TODO: Sure there's a bunch of helpers around somewhere?!?       
        private static StringContent ToStringContent(string content, Encoding encoding = null, string mediaType = "application/json")
        {
            return new StringContent(content, encoding ?? Encoding.Default, mediaType);
        }
    }
}
