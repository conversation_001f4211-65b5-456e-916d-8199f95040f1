﻿using CSharpFunctionalExtensions;
using Forecourt.Bos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Extensions;
using Htec.Foundation.Web.Hubs.Interfaces;
using Htec.Hydra.Core.Bos.Messages;
using Htec.Hydra.Core.Bos.Messages.SignalR;
using Htec.Logger.Interfaces;
using System;
using System.Collections.Generic;
using System.Net;

namespace Forecourt.Bos.Workers
{
    /// <inheritdoc/>
    public class SignalRBosOutWorker: BosOutWorker, ISignalRBosOutWorker
    {
        private readonly IHubbable<BosMessage, IMessageTracking> _hubContext;

        /// <inheritdoc />
        public SignalRBosOutWorker(IHtecLogManager logManager, IConfigurationManager configurationManager, Bos.HydraDb.Interfaces.IHydraDb hydraDb, IHubbable<BosMessage, IMessageTracking> hubContext) :
            base(logManager, nameof(SignalRBosOutWorker).ConvertToPrefixedDottedName(LoggerNamePrefix), configurationManager, hydraDb)
        {
            _hubContext = hubContext ?? throw new ArgumentNullException(nameof(hubContext));

            DoBackgroundTask(() => ProcessTransactionBookings(), null, null, TimeSpan.FromSeconds(30).Milliseconds, nameof(ProcessTransactionBookings));
        }

        /// <inheritdoc />
        protected override Result DoDayEndResponse(ShiftEndItem dayEndDetails, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CategorySalesItem> categorySales, IEnumerable<CardSalesItem> cardSales,
            IEnumerable<CardAmountSalesItem> cardAmountSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking tracking = null)
        {
            tracking ??= new MessageTracking();
            return DoAction(() => _hubContext.Publish("dayEndResponse", dayEndDetails, (all, pl) => all.dayEndResponse(pl), tracking), tracking);            
        }

        /// <inheritdoc />
        protected override Result DoSendTransaction(SendTransactionItem sendTransaction, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            SendTransactionPublish(sendTransaction, message);
            return Result.Success();
        }

        /// <summary>
        /// Publishes transaction information via SignalR hub to connected clients
        /// </summary>
        /// <param name="info">Transaction identification information</param>
        /// <param name="message">Message tracking context</param>
        private void SendTransactionPublish(IdInfo info, IMessageTracking message)
        {
            _hubContext.Publish("sendTransaction", new TransactionIds { TxnNumber = info.Number, TransId = info.Id, MessageId = message.IdAsString, Identifier = message.IdAsString },
                (all, pl) => all.sendTransaction(pl), message);
        }

        /// <inheritdoc />
        protected override Result DoShiftEndResponse(ShiftEndItem shiftEndDetails, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CardSalesItem> cardSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking tracking = null)
        {
            tracking ??= new MessageTracking();
            return DoAction(() => _hubContext.Publish("shiftEndResponse", shiftEndDetails, (all, pl) => all.shiftEndResponse(pl), tracking), tracking);
        }

        /// <inheritdoc />
        protected override Result DoWriteLocalAccountInvoiceFile(LocalAccountTransactionItem localAccountItem, IMessageTracking tracking = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        protected override Result DoWriteShiftEndFiles(bool isDayEnd, DateTime startTime, DateTime endTime, ShiftEndItem endItem, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CategorySalesItem> categorySales, IEnumerable<CardSalesItem> cardSales, IEnumerable<CardAmountSalesItem> cardAmountSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking tracking = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        protected override Result DoWriteTransactionFile(IEnumerable<TransactionItem> items, DateTime dateTime, IEnumerable<IPAddress> devices = null, IMessageTracking tracking = null)
        {
            return Result.Success();
        }

        /// <summary>
        /// Any and all pending TransactionBooking (including current) are dealt with here
        /// </summary>
        /// <returns>Result</returns>
        private Result ProcessTransactionBookings()
        {
            var message = new MessageTracking();

            var result = HydraDb.GetPendingTransactionBookings(message);
            if (!result.IsSuccess)
            {
                return result;
            }

            var transactions = result.Value;
            if (transactions != null)
            {
                foreach (var transaction in transactions)
                {
                    SendTransactionPublish(new IdInfo() { Id = transaction.TransactionId, Number = transaction.TxnNumber }, new MessageTracking());
                }
            }

            return Result.Success();
        }

        /// <inheritdoc/>
        protected override bool DoIsConnected() => _hubContext.IsConnected();

        /// <inheritdoc/>
        protected override int DoGetConnectedCount() => _hubContext.ConnectedCount;

        /// <inheritdoc/>
        protected override IEnumerable<IPAddress> DoGetAllIpAddresses() => _hubContext.GetAllIpAddresses();

        /// <inheritdoc/>
        protected override Result DoStart(params object[] startParams)
        {
            _hubContext.RegisterWorker(GetWorker<Htec.Foundation.Connections.Workers.Interfaces.INotificationWorker<string>>());
            return base.DoStart(startParams);
        }
    }
}
