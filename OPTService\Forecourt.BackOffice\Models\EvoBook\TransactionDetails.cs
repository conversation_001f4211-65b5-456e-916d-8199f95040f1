﻿using Newtonsoft.Json;

namespace Forecourt.BackOffice.Models.EvoBook
{
    /// <summary>
    /// Contains comprehensive transaction details including operator information, timing, and transaction state for EvoBook transactions
    /// </summary>
    public class TransactionDetails
    {
        /// <summary>
        /// Unique transaction key containing company, store, and sequence information
        /// </summary>
        [JsonProperty("key")]
        public TransactionKey Key { get; set; }
        /// <summary>
        /// Identifier for the operator who processed the transaction
        /// </summary>
        [JsonProperty("operatorId")]
        public string OperatorId { get; set; }
        /// <summary>
        /// Timestamp when the transaction was initiated
        /// </summary>
        [JsonProperty("startTime")]
        public string StartTime { get; set; }
        /// <summary>
        /// Timestamp when the transaction was completed
        /// </summary>
        [JsonProperty("endTime")]
        public string EndTime { get; set; }
        /// <summary>
        /// Business period number during which the transaction occurred
        /// </summary>
        [JsonProperty("periodNumber")]
        public int PeriodNumber { get; set; }
        /// <summary>
        /// Indicates whether the transaction has been cancelled
        /// </summary>
        [JsonProperty("cancelledFlag")]
        public bool CancelledFlag { get; set; }
        /// <summary>
        /// Indicates whether the transaction is currently suspended
        /// </summary>
        [JsonProperty("suspendedFlag")]
        public bool SuspendedFlag { get; set; }
        /// <summary>
        /// Indicates whether this is a training transaction
        /// </summary>
        [JsonProperty("trainingFlag")]
        public bool TrainingFlag { get; set; }
        /// <summary>
        /// Identifier for the business shift during which the transaction occurred
        /// </summary>
        [JsonProperty("shiftId")]
        public int ShiftId { get; set; }
        /// <summary>
        /// Number of times the receipt has been printed for this transaction
        /// </summary>
        [JsonProperty("receiptPrintCount")]
        public int ReceiptPrintCount { get; set; }
        
        /// <summary>
        /// Indicates whether the transaction was processed in Self-Checkout (SCO) mode
        /// </summary>
        [JsonProperty("scoMode")]
        public bool ScoMode { get; set; }
    }
}
