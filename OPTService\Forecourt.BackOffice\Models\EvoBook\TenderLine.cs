﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Forecourt.BackOffice.Models.EvoBook
{
    /// <summary>
    /// Represents payment tender information including method, amount, and card details for EvoBook transactions
    /// </summary>
    public class TenderLine
    {
        /// <summary>
        /// Sequential number identifying this tender line within the transaction
        /// </summary>
        [JsonProperty("lineItemSequenceNumber")]
        public int LineItemSequenceNumber { get; set; }
        /// <summary>
        /// Indicates whether this tender line has been voided
        /// </summary>
        [JsonProperty("voidedFlag")]
        public bool VoidedFlag { get; set; }
        /// <summary>
        /// Timestamp when the tender processing started
        /// </summary>
        [JsonProperty("startTime")]
        public string StartTime { get; set; }
        /// <summary>
        /// Timestamp when the tender processing completed
        /// </summary>
        [JsonProperty("endTime")]
        public string EndTime { get; set; }
        /// <summary>
        /// Indicates whether this tender requires fiscal receipt printing
        /// </summary>
        [JsonProperty("fiscalReceipt")]
        public bool FiscalReceipt { get; set; }
        /// <summary>
        /// JSON object containing the payment method details and configuration
        /// </summary>
        [JsonProperty("methodOfPayment")]
        public JObject MethodOfPayment { get; set; }
        /// <summary>
        /// Amount tendered for this payment line
        /// </summary>
        [JsonProperty("tenderAmount")]
        public double TenderAmount { get; set; }
        /// <summary>
        /// Amount in foreign currency if applicable
        /// </summary>
        [JsonProperty("foreignCurrencyAmount")]
        public double ForeignCurrencyAmount { get; set; }
        /// <summary>
        /// Detailed card information if payment method is card-based
        /// </summary>
        [JsonProperty("cardDetails")]
        public CardDetail CardDetails { get; set; }
        /// <summary>
        /// Indicates whether this tender line represents change given to customer
        /// </summary>
        [JsonProperty("changeLine")]
        public bool ChangeLine { get; set; }
        /// <summary>
        /// Barcode value for voucher-based payments
        /// </summary>
        [JsonProperty("voucherBarcode")]
        public string VoucherBarcode { get; set; }
        /// <summary>
        /// Indicates whether this tender represents a charitable donation
        /// </summary>
        [JsonProperty("donation")]
        public bool Donation { get; set; }
        /// <summary>
        /// Identifier for the base tender associated with this donation
        /// </summary>
        [JsonProperty("donationBaseTenderId")]
        public string DonationBaseTenderId { get; set; }
    }
}
