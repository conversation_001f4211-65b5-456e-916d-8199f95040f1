﻿using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.IO.Abstractions;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <summary>
    /// Migration to add indexes to dbo.Transactions table for improved query performance
    /// </summary>
    [Migration(28200003, "ADO#795901 - Add indexes to dbo.Transactions"), Tags(Constants.MigrationTypeSchema)]
    public class ADO795901_Add_Indexes_Transactions : Migrationable
    {
        private const string TableName = "Transactions";
        private const string IndexNamePrefix = "ix_" + TableName + "_";

        /// <inheritdoc/>
        public ADO795901_Add_Indexes_Transactions(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
        }

        /// <inheritdoc/>
        public override void Up()
        {
            var indexName = IndexNamePrefix + "trx";
            if (!Schema.Table(TableName).Index(indexName).Exists())
            {
                Create.Index(indexName).OnTable(TableName)
                    .OnColumn("TransactionTime").Ascending()
                    .OnColumn("TxnNumber").Ascending()
                    .OnColumn("CardNumber").Ascending()
                    .OnColumn("PumpDetails").Ascending()
                    .WithOptions().NonClustered();
            }
        }

        /// <inheritdoc/>
        public override void Down()
        {
            var indexName = IndexNamePrefix + "timestamp";
            if (Schema.Table(TableName).Index(indexName).Exists())
            {
                Delete.Index(indexName).OnTable(TableName);
            }
        }
    }
}

