﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace Forecourt.BackOffice.Models.EvoBook
{
    /// <summary>
    /// Represents a transaction basket containing sale lines, tax information, and payment details for EvoBook transactions
    /// </summary>
    public class Basket
    {
        /// <summary>
        /// Indicates whether this is a bunkering sale transaction
        /// </summary>
        [JsonProperty("bunkeringSale")]
        public bool BunkeringSale { get; set; }
        /// <summary>
        /// Total amount of the sale transaction
        /// </summary>
        [JsonProperty("totalSaleAmount")]
        public double TotalSaleAmount { get; set; }
        /// <summary>
        /// Collection of individual sale line items in the basket
        /// </summary>
        [JsonProperty("saleLines")]
        public List<SaleLine> SaleLines { get; set; }
        /// <summary>
        /// Collection of tax calculations applied to the basket
        /// </summary>
        [JsonProperty("taxLines")]
        public List<TaxLine> TaxLines { get; set; }
        /// <summary>
        /// Collection of payment methods used for the transaction
        /// </summary>
        [JsonProperty("tenderLines")]
        public List<TenderLine> TenderLines { get; set; }
        /// <summary>
        /// Collection of discount voucher codes applied to the transaction
        /// </summary>
        [JsonProperty("discountVouchers")]
        public List<string> DiscountVouchers { get; set; }
        /// <summary>
        /// Employee identifier who processed the transaction
        /// </summary>
        [JsonProperty("employee")]
        public string Employee { get; set; }
        /// <summary>
        /// Customer loyalty program identifier
        /// </summary>
        [JsonProperty("loyaltyId")]
        public string LoyaltyId { get; set; }
        /// <summary>
        /// Collection of loyalty program text messages for the transaction
        /// </summary>
        [JsonProperty("loyaltyTextLines")]
        public List<string> LoyaltyTextLines { get; set; }
    }
}
