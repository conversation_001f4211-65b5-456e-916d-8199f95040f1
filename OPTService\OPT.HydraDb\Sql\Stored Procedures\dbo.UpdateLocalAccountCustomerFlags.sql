USE [Hydra]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.objects WHERE OBJECT_ID = OBJECT_ID('UpdateLocalAccountCustomerFlags') AND TYPE in ('P', 'PC')) BEGIN
    DROP PROCEDURE dbo.UpdateLocalAccountCustomerFlags
END
GO

CREATE PROCEDURE dbo.UpdateLocalAccountCustomerFlags (
    @CustomerReference nvarchar(80),
    @IsPinRequired     bit = 0,
    @ShouldPrintValue  bit = 0,
    @IsLoyaltyAllowed  bit = 0
)

AS

BEGIN

    UPDATE lac
    SET [Pin]          = @IsPinRequired,
        [PrintValue]   = @ShouldPrintValue,
        [AllowLoyalty] = @IsLoyaltyAllowed
    FROM dbo.LocalAccountCustomers lac
    WHERE lac.[Reference] = @CustomerReference;

    RETURN @@ROWCOUNT;
END

GRANT EXECUTE ON dbo.UpdateLocalAccountCustomerFlags TO UserRole
GO
