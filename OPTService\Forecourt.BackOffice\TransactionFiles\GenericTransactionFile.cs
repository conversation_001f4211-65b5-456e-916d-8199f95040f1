﻿using CsvHelper;
using Forecourt.Bos.HydraDb.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.IO.Abstractions;

namespace Forecourt.Bos.TransactionFiles
{
    /// <summary>
    /// Abstract base class for transaction file operations, providing common functionality for writing transaction data to files
    /// </summary>
    public abstract class GenericTransactionFile : Loggable, IGenericTransactionFile
    {
        /// <summary>
        /// Constant for the Csv file extension
        /// </summary>
        protected const string CsvExtension = ".csv";

        /// <summary>
        /// File system abstraction for file operations
        /// </summary>
        protected IFileSystem FileSystem { get; }
        
        /// <summary>
        /// Database access interface for Hydra database operations
        /// </summary>
        protected IHydraDb HydraDb { get; }
        
        /// <summary>
        /// Synchronization object for thread-safe file operations
        /// </summary>
        protected readonly object SyncObject = new object();

        /// <summary>
        /// Directory path where transaction files are stored
        /// </summary>
        public string FileDirectory { get; protected set; }

        /// <summary>
        /// Indicates whether the file directory is available for writing
        /// </summary>
        public bool Available { get; private set; }

        /// <summary>
        /// Function to get current date and time, allows for dependency injection in testing
        /// </summary>
        internal Func<DateTime> GetNow { get; set; }

        /// <summary>
        /// Gets all file location configurations from the Hydra database
        /// </summary>
        /// <inheritdoc />
        protected AllFileLocations AllFileLocations => HydraDb.GetFileLocations();

        /// <inheritdoc />
        protected GenericTransactionFile(IHydraDb hydraDb, IHtecLogger logger, IFileSystem fileSystem, IConfigurationManager configurationManager = null): 
            base(logger, configurationManager)
        {
            HydraDb = hydraDb ?? throw new ArgumentNullException(nameof(hydraDb));
            FileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));

            GetNow = () => DateTime.Now;
        }

        /// <inheritdoc />
        public void CheckFileDirectory(string directory = null)
        {
            try
            {
                // Does an internal check if it exists
                FileSystem.Directory.CreateDirectory(string.IsNullOrEmpty(directory) ? FileDirectory : directory);
            }
            catch (Exception exception)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] {$"Error creating transaction file directory {directory}"}, exception);
            }
        }

        /// <inheritdoc />
        protected abstract void DoSetFileDirectoryAction(string directory);

        /// <inheritdoc />
        public void SetFileDirectory(string directory = null)
        {
            if (string.IsNullOrWhiteSpace(directory))
            {
                Available = false;
                DoSetFileDirectoryAction(null);
            }
            else
            {
                Available = true;
                FileDirectory = directory;

                DoSetFileDirectoryAction(directory);
                CheckFileDirectory(directory);
            }
        }

        /// <summary>
        /// Writes collection of items to a file with proper error handling and logging
        /// </summary>
        /// <typeparam name="T">Type of items to write to file</typeparam>
        /// <param name="items">Collection of items to write</param>
        /// <param name="fileName">Full path to the output file</param>
        /// <param name="fileType">Description of file type for logging purposes</param>
        protected void WriteItemsToFile<T>(IEnumerable<T> items, string fileName, string fileType)
        {
            CheckFileDirectory(FileDirectory);
            CheckFileDirectory(FileSystem.Path.GetDirectoryName(fileName));

            try
            {
                lock (SyncObject)
                {
                    using (var file = FileSystem.FileStream.New(fileName, FileMode.Append, FileAccess.Write, FileShare.None))
                    {
                        using (var writer = new StreamWriter(file))
                        {
                            using (var csv = new CsvWriter(writer, CultureInfo.CurrentCulture))
                            {
                                foreach (var item in items)
                                {
                                    csv.WriteRecord(item);
                                    csv.NextRecord();
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] {$"Error creating {fileType} file {fileName}"}, exception);
            }

            VerifyFile(fileName);
        }

        /// <summary>
        /// Verifies that a transaction file was written correctly and logs the file size information
        /// </summary>
        /// <param name="fileName">Full path to the file to verify</param>
        protected void VerifyFile(string fileName)
        {
            try
            {
                var fileInfo = FileSystem.FileInfo.New(fileName);
                DoDeferredLogging(LogLevel.Info, "BytesWritten", () => new[] {$"{fileInfo.Length}; File: {fileName}"});
            }
            catch (Exception e)
            {
                DoDeferredLogging(LogLevel.Error, $"{HeaderException}.File", () => new[] {$"{fileName}"}, e);
            }
        }

        /// <summary>
        /// Creates a standardized filename for transaction files with timestamp and optional flag
        /// </summary>
        /// <param name="name">Base name for the file</param>
        /// <param name="tillNumber">Till number to include in filename</param>
        /// <param name="timeStamp">Timestamp to format into filename</param>
        /// <param name="flag">Optional flag to append to filename</param>
        /// <returns>Full file path with generated filename</returns>
        protected string CreateFileName(string name, string tillNumber, DateTime timeStamp, string flag = "")
        {
            return FileSystem.Path.Combine(FileDirectory, $"{name}{tillNumber}{timeStamp:yyMMddHHmmss}{flag}{CsvExtension}");
        }
    }
}
