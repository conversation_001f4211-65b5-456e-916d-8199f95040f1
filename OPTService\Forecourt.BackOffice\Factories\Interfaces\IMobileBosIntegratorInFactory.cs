﻿using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Interfaces;
using Htec.Hydra.Core.Bos.Interfaces;

namespace Forecourt.Bos.Factories.Interfaces
{
    // TODO: Rename, remove Mobile
    /// <summary>
    /// Standard IFactory based definition for the (mobile) BOS Integrator In
    /// </summary>
    public interface IMobileBosIntegratorInFactory : IFactory<string, IBosIntegratorInJournal<IMessageTracking>>
    {
    }
}
