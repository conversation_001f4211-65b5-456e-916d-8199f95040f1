﻿using Newtonsoft.Json;
using OPT.Common.HydraDbClasses;
using System.Diagnostics;

namespace OPTService.OPTServiceClasses
{
    [DebuggerDisplay("{ShowData}")]
    public class LocalAccountCustomerSetFlagsRequest
    {
        public string CustomerReference { get; set; }
        public bool IsPinRequired { get; set; }
        public bool ShouldPrintValue { get; set; }
        public bool IsLoyaltyAllowed { get; set; }

        private string ShowData => $"CustomerReference {CustomerReference} - isPinRequired {IsPinRequired}, shouldPrintValue {ShouldPrintValue}, isLoyaltyAllowed {IsLoyaltyAllowed};";

        /// <summary>
        /// Returns a string representation of the current instance.
        /// </summary>
        public override string ToString() => ShowData;

        /// <summary>
        /// Explicitly converts a request model to HydraDbClasses type
        /// </summary>
        /// <param name="request">The source update flags request.</param>
        public static explicit operator LocalAccountCustomerFlags(LocalAccountCustomerSetFlagsRequest request)
        {
            if (request == null) return null;

            return new LocalAccountCustomerFlags
            {
                CustomerReference = request.CustomerReference,
                IsPinRequired = request.IsPinRequired,
                ShouldPrintValue = request.ShouldPrintValue,
                IsLoyaltyAllowed = request.IsLoyaltyAllowed
            };
        }
    }
}