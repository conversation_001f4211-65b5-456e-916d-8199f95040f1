﻿using CSharpFunctionalExtensions;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Pos.Messages;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Hydra.Core.Pump.Messages.Extensions;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Logger.Interfaces;
using System;
using System.Collections.Generic;
using System.Xml.Linq;
using ITelemetryWorker = Forecourt.Pos.Workers.Interfaces.ITelemetryWorker;
using posIntf = Htec.Hydra.Core.Pos.Interfaces;
using Pump = Forecourt.Core.Pump.Models.Pump;
using PumpData = Htec.Hydra.Core.Pump.Messages.PumpData;
using Status = Htec.Hydra.Core.Pos.Messages.HydraMobile.Status;
using Transaction = Htec.Hydra.Core.Pump.Messages.Transaction;
using VehicleRegistrationData = Htec.Hydra.Core.Pump.Messages.VehicleRegistrationData;

namespace Forecourt.Pos.Workers
{
    /// <summary>
    /// Represents a worker responsible for handling the status of Hydra Mobile operations.
    /// </summary>
    /// <inheritdoc cref="IHydraMobileStatusWorker" />
    public class HydraMobileStatusWorker: BaseHydraMobileWorker, IHydraMobileStatusWorker
    {
        public HydraMobileStatusWorker(IHtecLogManager logMan, IHydraDb hydraDb, IConfigurationManager configurationManager, ITelemetryWorker telemetryWorker, IClientConnectionThread<XElement> connectionThread,
            posIntf.IPosIntegratorOutTransient<IMessageTracking> posOut, IJournalWorkerReceipt journalWorker, IBosIntegratorOut<IMessageTracking> bosOut, IOptCollection allOpt, IPumpCollection allPumps,
            IGradeHelper gradeHelper, ISecAuthIntegratorOutTransient<IMessageTracking> secAuthOut, ITimerFactory timerFactory, IPumpIntegratorOutTransient<IMessageTracking> pumpOutTransientWorker) :
            base(logMan, hydraDb, configurationManager, telemetryWorker, connectionThread, posOut, journalWorker, bosOut, allOpt, allPumps, gradeHelper, secAuthOut, timerFactory)
        {
            RegisterWorker(pumpOutTransientWorker ?? throw new ArgumentNullException(nameof(pumpOutTransientWorker)));
        }


        /// <inheritdoc cref="IMessageTracking" />
        protected override Result<(XElement, StatusResponse)> DoOnMessageReceived_Status(IMessageTracking<XElement> message, Status status, string sendInfo)
        {
            IPump thePump = null;
            if (status.PumpState == $"{PumpState.Reserved}")
            {
                DoDeferredLogging(Htec.Foundation.Core.LogLevel.Info, "Pump", () => new[] { $"{status.Pump}; HydraMobile Transaction Starting!" });

                NotificationWorker?.SendInformation($"Custom Hydra Mobile Status Command received: {sendInfo}");

                var pumpNumberByte = (byte)status.Pump;
                if (AllPumps.TryGetPump(pumpNumberByte, out thePump) && CurrentInfo.TryGetValue(pumpNumberByte, out var pumpInfo))
                {
                    var pumpData = new PumpData
                    {
                        Number = pumpNumberByte,
                        Dispenser = new Dispenser
                        {
                            Number = pumpNumberByte,
                            State = DispenserState.Reserved,
                            CurrentInfo = pumpInfo,
                            HoseInfo = new Dictionary<byte, Hose>
                            {
                                { pumpNumberByte, pumpInfo?.Hose }
                            },
                            Transaction1 = Transaction.Empty,
                            Transaction2 = Transaction.Empty
                        },
                        RegistrationData = VehicleRegistrationData.Empty
                    };

                    var pumpIntegrator = GetWorker<IPumpIntegratorOutTransient<IMessageTracking>>();
                    var pumpStateChange = (PumpStateChange)pumpData;

                    pumpIntegrator?.OnPumpState(pumpStateChange, message);
                }
            }

            var response = new StatusResponse()
            {
                Pump = (byte)status.Pump,
                IsOffline = status.IsOffline,
                IsKioskUse = thePump?.KioskUse ?? false,
                IsInUse = thePump?.InUse ?? false,
                IsOutsideOnly = thePump?.OutsideOnly ?? false,
                IsOptLinked = thePump?.Opt != null,
                // TODO: This may have to come from IPosIntegratorConfiguration
                IsAutoAuth = false,
                PumpState = (ushort)PumpState.Reserved
            };

            return Result.Success<(XElement, StatusResponse)>((null, response));
        }

        public override void OnPumpState(PumpStateChange pumpState, IMessageTracking message = null)
        {
            base.OnPumpState(pumpState, message);
            if (pumpState.PumpData.Dispenser.State == DispenserState.Reserved)
            {
                var pumpData = (PumpData)pumpState.PumpData;
                var msg = pumpData.CreateOnPumpDataParamText();
                DoDeferredLogging(string.IsNullOrWhiteSpace(msg) ? LogLevel.None : LogLevel.Info, Pump.HeaderPump, () => new[] { msg }, reference: message.FullId, methodName: "OnPumpData");
            }
        }
    }
}
