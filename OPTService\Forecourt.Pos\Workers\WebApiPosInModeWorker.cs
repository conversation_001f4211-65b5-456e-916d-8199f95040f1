﻿using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Logger.Interfaces;

namespace Forecourt.Pos.Workers
{
    /// <inheritdoc/>
    public class WebApiPosInModeWorker: PosInModeWorker, IWebApiPosInModeWorker
    {
        /// <inheritdoc/>
        public WebApiPosInModeWorker(IHtecLogManager logManager, IHydraDb hydraDb, IPumpCollection allPumps, IPosIntegratorOutTransient<IMessageTracking> posOutTransientWorker,
            IConfigurationManager configurationManager = null) : base(logManager, hydraDb, allPumps, posOutTransientWorker, configurationManager)
        {
        }
    }
}
