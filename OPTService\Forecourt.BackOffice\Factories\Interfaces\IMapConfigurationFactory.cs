﻿using Forecourt.Bos.Orchestrations.Interfaces;
using Htec.Foundation.Interfaces;
using Htec.Foundation.Models.Interfaces;

namespace Forecourt.Bos.Factories.Interfaces
{
    /// <summary>
    /// Standard IFactory based definition for the configuration data mapping Orchestration
    /// </summary>

    public interface IMapConfigurationFactory: IFactory<string, IMapConfigurationOrchestration<ILogTracking>>
    {
    }
}
