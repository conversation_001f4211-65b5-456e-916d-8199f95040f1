using System;

namespace Forecourt.Core.Extensions
{
    /// <summary>
    /// Extension methods for DateTime operations
    /// </summary>
    public static class DateTimeExtensions
    {
        /// <summary>
        /// Determines if a DateTime is within a specified time range, handling midnight spanning scenarios
        /// </summary>
        /// <param name="currentDateTime">The DateTime to check</param>
        /// <param name="startTime">The start time of the range</param>
        /// <param name="endTime">The end time of the range</param>
        /// <returns>True if the DateTime is within the time range, false otherwise</returns>
        public static bool IsWithinTimeRange(this DateTime currentDateTime, TimeSpan startTime, TimeSpan endTime)
        {
            var currentDate = currentDateTime.Date;

            // Make the range inclusive of the start time and exclusive of the end times
            endTime = endTime.Subtract(TimeSpan.FromMilliseconds(1));

            // Handle cases where endTime is before startTime (spanning midnight) and adjust range accordingly
            var spansMidnight = startTime > endTime;
            var addDays = !spansMidnight ? 0 : currentDateTime.TimeOfDay >= startTime ? 1 : -1;
            if (addDays < 0)
            {
                var swap = endTime;
                endTime = startTime;
                startTime = swap;
            }

            var rangeStart = currentDate.Add(startTime);
            var rangeEnd = currentDate.Add(endTime).AddDays(addDays);


            // Inclusive of start time AND exclusive of end time
            return currentDateTime.Ticks >= Math.Min(rangeStart.Ticks, rangeEnd.Ticks) &&
                   currentDateTime.Ticks < Math.Max(rangeStart.Ticks, rangeEnd.Ticks);
        }
    }
}