﻿using Htec.Foundation.Connections.Models.Interfaces;
using bosIntf = Htec.Hydra.Core.Bos.Interfaces;

namespace Forecourt.Bos.TransactionFiles.Interfaces
{
    /// <summary>
    /// Retalix version of <see cref="bosIntf.IBosTransactionFileWriter{TMessageTracking, TFileItem, TLocalAccountItem, TShiftEndItem, TShiftEndOptions, TSalesItem, TCardAmountItem, TCardSalesItem, TCategorySalesItem, TCardVolumeSalesItem}"/> tied to Retalix messages
    /// </summary>
    public interface IRetalixTransactionFile : bosIntf.Retalix.ITransactionFile<IMessageTracking>
    {
    }
}
