﻿using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Bos.Messages;
using System.Threading.Tasks;

namespace Forecourt.BackOffice.Orchestrations.Interfaces
{
    /// <summary>
    /// Any and all capabilities of 
    /// </summary>
    public interface IBookTransactionOrchestration
    {
        /// <summary>
        /// Execute the whole booking a transaction process 
        /// </summary>
        /// <param name="item">The transaction instance to book</param>
        /// <param name="configDataMap">Configuration data mapping</param>
        /// <param name="message">Current message instance</param>
        /// <returns><see cref="StatusCodeResult"/> instance</returns>
        Task<StatusCodeResult> RunOrchestration(SendTransactionItem item, IOrchestratedCommand configDataMap, IMessageTracking message = null);
    }
}
