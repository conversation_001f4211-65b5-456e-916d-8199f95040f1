﻿using Forecourt.Bos.HydraDb.Interfaces;
using Forecourt.Bos.Orchestrations.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Net.Http.Interfaces;
using Htec.Foundation.Models;
using Htec.Foundation.Models.Interfaces;
using Htec.Foundation.Orchestrations;
using Htec.Logger.Interfaces;
using JetBrains.Annotations;
using System;
using System.Threading.Tasks;

namespace Forecourt.Bos.Orchestrations
{
    /// <summary>
    /// Orchestration that manages the process mapping Hydra configuration data to an external systems equivalent
    /// </summary>
    /// <inheritdoc/>
    public abstract class MapConfigurationDataOrchestration<TModel> : OrderedOrchestration<TModel, StatusCodeResult>, IMapConfigurationOrchestrationAsync<ILogTracking>, IMapConfigurationOrchestration<ILogTracking>
        where TModel : IOrchestratedCommand, new()
    {
        /// <summary>
        /// <see cref="IHydraDb"/> instance
        /// </summary>
        protected IHydraDb HydraDb { get; set; }

        /// <summary>
        /// Factory instance (<see cref="IHttpClientFactory"/>), for managing HttpClient instances
        /// </summary>
        protected IHttpClientFactory HttpClientFactory { get;set;}

        /// <inheritdoc/>
        public MapConfigurationDataOrchestration(IHtecLogManager logManager, string loggerName, IConfigurationManager configurationManager, IHydraDb hydraDb, IHttpClientFactory httpClientFactory) :
            base(logManager, loggerName, configurationManager)
        {
            HydraDb = hydraDb ?? throw new ArgumentNullException(nameof(hydraDb));
            HttpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
        }

        /// <inheritdoc/>

        public async Task<StatusCodeResult> RunOrchestrationAsync(ILogTracking message = default)
        {
            message ??= new LogTracking();

            var model = new TModel
            {
                CorrelationId = message.IdAsString
            };

            return await RunOrchestrationAsync(model, message).ConfigureAwait(false);
        }

        /// <inheritdoc/>
        protected async Task<StatusCodeResult> RunOrchestrationAsync([NotNull]TModel model, ILogTracking message)
        {
            message ??= new LogTracking();
            model.CorrelationId = message.IdAsString;

            var result = await ProcessSteps(model, checkFunction: (m, scr) => scr.IsSuccess).ConfigureAwait(false);

            return result.IsSuccess ? StatusCodeResult<TModel>.Success(model) : StatusCodeResult.Specific(result.StatusCode, result.Exception);
        }

        /// <inheritdoc/>
        public StatusCodeResult RunOrchestration(ILogTracking message = null)
        {
            message ??= new LogTracking();

            var model = new TModel
            {
                CorrelationId = message.IdAsString
            };

            return RunOrchestration(model, message);
        }

        /// <inheritdoc/>
        protected StatusCodeResult RunOrchestration([NotNull] TModel model, ILogTracking message)
        {
            message ??= new LogTracking();
            model.CorrelationId = message.IdAsString;

            var result = ProcessSteps(model, checkFunction: (m, scr) => scr.IsSuccess).Result;

            return result.IsSuccess ? StatusCodeResult<TModel>.Success(model) : StatusCodeResult.Specific(result.StatusCode, result.Exception);
        }
    }
}
