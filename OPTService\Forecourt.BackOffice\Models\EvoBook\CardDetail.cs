﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace Forecourt.BackOffice.Models.EvoBook
{
    /// <summary>
    /// Contains detailed payment card information for EvoBook transactions including authorization and terminal data
    /// </summary>
    public class CardDetail
    {
        /// <summary>
        /// Unique identifier for the card payment acquirer
        /// </summary>
        [JsonProperty("acquirerID")]
        public string AcquirerId { get; set; }
        /// <summary>
        /// Primary Account Number (PAN) of the payment card
        /// </summary>
        [JsonProperty("cardPan")]
        public string CardPan { get; set; }
        /// <summary>
        /// Code indicating the method used for transaction approval
        /// </summary>
        [JsonProperty("approvalMethodCode")]
        public string ApprovalMethodCode { get; set; }
        /// <summary>
        /// Authorization code received from the payment processor
        /// </summary>
        [JsonProperty("approvalCode")]
        public string ApprovalCode { get; set; }
        /// <summary>
        /// Type of payment card (e.g., Visa, MasterCard, Debit)
        /// </summary>
        [JsonProperty("cardType")]
        public string CardType { get; set; }
        /// <summary>
        /// Transaction reference number from the payment processor
        /// </summary>
        [JsonProperty("referenceNumber")]
        public string ReferenceNumber { get; set; }
        /// <summary>
        /// Unique identifier for the payment terminal
        /// </summary>
        [JsonProperty("terminalId")]
        public string TerminalId { get; set; }
        /// <summary>
        /// Timestamp when the transaction was approved
        /// </summary>
        [JsonProperty("approvalTime")]
        public string ApprovalTime { get; set; }
        /// <summary>
        /// System Trace Audit Number for transaction tracking
        /// </summary>
        [JsonProperty("stan")]
        public string Stan { get; set; }
        /// <summary>
        /// Additional miscellaneous data related to the card transaction
        /// </summary>
        [JsonProperty("miscellaneousData")]
        public string MiscellaneousData { get; set; }
        /// <summary>
        /// Collection of receipt text lines for the card transaction
        /// </summary>
        [JsonProperty("receiptLines")]
        public List<string> ReceiptLines { get; set; }
        /// <summary>
        /// Collection of journal text lines for audit and tracking purposes
        /// </summary>
        [JsonProperty("journalLines")]
        public List<string> JournalLines { get; set; }
        /// <summary>
        /// Unique identifier for the merchant processing the transaction
        /// </summary>
        [JsonProperty("merchantId")]
        public string MerchantId { get; set; }
        /// <summary>
        /// Batch number for terminal transaction grouping
        /// </summary>
        [JsonProperty("terminalBatch")]
        public string TerminalBatch { get; set; }
        /// <summary>
        /// Indicates whether this is a volumetric fuel card transaction
        /// </summary>
        [JsonProperty("volumetricCard")]
        public bool VolumetricCard { get; set; }
        /// <summary>
        /// Indicates whether a deposit was used for this transaction
        /// </summary>
        [JsonProperty("useDeposit")]
        public bool UseDeposit { get; set; }
        /// <summary>
        /// Vehicle registration number associated with the card
        /// </summary>
        [JsonProperty("registration")]
        public string Registration { get; set; }
        /// <summary>
        /// Flag indicating whether transaction requires reallocation
        /// </summary>
        [JsonProperty("reallocationFlag")]
        public bool ReallocationFlag { get; set; }
        /// <summary>
        /// Unique identifier for the customer associated with the card
        /// </summary>
        [JsonProperty("customerID")]
        public string CustomerId { get; set; }
    }
}
