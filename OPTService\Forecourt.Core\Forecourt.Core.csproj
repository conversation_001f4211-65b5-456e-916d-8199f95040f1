﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net48</TargetFramework>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<LangVersion>9.0</LangVersion>
		<Platforms>AnyCPU;x64</Platforms>
	</PropertyGroup>

	<PropertyGroup>
		<NoWarn>1701;1702;1591</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Htec.Foundation" Version="[5.0.0,)" />
		<PackageReference Include="Htec.Foundation.Connections" Version="[6.0.0,)" />
		<PackageReference Include="Htec.HubClient" Version="[4.0.0,)" />
		<PackageReference Include="Htec.Hydra.Core.Pos" Version="2.3.0-alpha0003" />
		<PackageReference Include="Htec.Hydra.Core.SecAuth" Version="[1.0.0,)" />
		<PackageReference Include="Htec.Hydra.Messages.Opt" Version="[2.7.0,)" />
		<PackageReference Include="Htec.Hydra.Opt.Common" Version="[2.4.0,)" />
		<PackageReference Include="Htec.Hydra.Core.Bos" Version="[1.2.0,)" />
		<PackageReference Include="Microsoft.AspNet.SignalR" Version="[2.4.3,)" />
	</ItemGroup>

</Project>
