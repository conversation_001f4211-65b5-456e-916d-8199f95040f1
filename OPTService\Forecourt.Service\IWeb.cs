﻿using CSharpFunctionalExtensions;
using Forecourt.Core.Enums;
using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Service.Interfaces;
using Htec.Foundation.Configuration;
using Htec.Foundation.Models;
using OPT.Common.HydraDbClasses;
using OPTService.OPTServiceClasses;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Net;
using optMessages = Htec.Hydra.Messages.Opt;

namespace OPTService
{
    public interface IWeb : IWebAbout, IWebConnections
    {
        IList<long> ReceiptTrans { get; }
        bool IsConfigBatch { get; }


        #region Fetch Data

        /// <summary>
        /// Get the list of Info Messages.
        /// </summary>
        /// <returns>List of Info Messages.</returns>
        IList<InfoMessageDetails> GetInfoDetails();

        /// <summary>
        /// Get the Generic OPT Config.
        /// </summary>
        /// <param name="reference">Logging reference</param>/>
        /// <returns>StatusCodeResult wrapped, Generic OPT Config.</returns>
        StatusCodeResult<GenericOptConfigDetails> GetGenericOptConfig(string reference = null);

        /// <summary>
        /// Get the Generic OPT Config.
        /// </summary>
        /// <returns>Generic OPT Config.</returns>
        GenericOptConfigDetails GetGenericOptConfig();

        /// <summary>
        /// Get the list of Local Account Customers.
        /// </summary>
        /// <returns>List of Local Account Customers.</returns>
        IList<LocalAccountCustomerDetails> GetLocalAccountCustomers();

        /// <summary>
        /// Get the advanced config.
        /// </summary>
        /// <returns>Advanced Config.</returns>
        AdvancedConfigDetails GetAdvancedConfig();

        /// <summary>
        /// Get the file locations.
        /// </summary>
        /// <returns>File Locations.</returns>
        FileLocations GetFileLocations();

        /// <summary>
        /// Get the list of Fuel Grade Prices.
        /// </summary>
        /// <returns>List of Prices.</returns>
        IList<GradePrices> GetPrices(byte gradeFilter = 0);

        /// <summary>
        /// Get the most recent alternative OPT settings.
        /// </summary>
        /// <returns>The Settings.</returns>
        DivertDetails GetDivertDetails();

        /// <summary>
        /// Get the time of the most recent Shift End.
        /// </summary>
        /// <returns>Shift End Time.</returns>
        DateTime GetShiftEndTime();

        /// <summary>
        /// Get the time of the most recent Day End.
        /// </summary>
        /// <returns>Day End Time.</returns>
        DateTime GetDayEndTime();

        /// <summary>
        /// Get the time of the next auto Day End.
        /// </summary>
        /// <returns>Day End Time, or null if not set.</returns>
        DateTime? GetNextDayEnd();

        /// <summary>
        /// Get the Unmanned Pseudo POS flag.
        /// </summary>
        /// <returns>True if unmanned pseudo POS is set, false otherwise.</returns>
        bool GetUnmanned();

        /// <summary>
        /// Get the POS Connected flag.
        /// </summary>
        /// <returns>True if a POS is connected, false otherwise.</returns>
        bool GetPosConnected();

        /// <summary>
        /// Get the Printer Enabled flag.
        /// </summary>
        /// <returns>True if printer is enabled, false otherwise.</returns>
        bool GetPrinterEnabled();

        /// <summary>
        /// Get the Asda Day End Report flag.
        /// </summary>
        /// <returns>True if Asda Day End Report is set, false otherwise.</returns>
        bool GetAsdaDayEndReport();

        /// <summary>
        /// Get the Asda Day End Printer details.
        /// </summary>
        /// <returns>Printer Details.</returns>
        PrinterDetails GetPrinterDetails();

        /// <summary>
        /// Get the log interval in hours, minutes and seconds.
        /// </summary>
        /// <returns>Log interval.</returns>
        LogIntervalDetails GetLogInterval();

        /// <summary>
        /// Get the versions of the current service and those in OPTServiceUpgrade and OPTServiceArchive.
        /// </summary>
        /// <returns>Versions.</returns>
        OPTServiceClasses.VersionInfo GetVersionInfo();

        /// <summary>
        /// Get the list of fuel transactions between the given dates.
        /// </summary>
        /// <param name="startTime">From date.</param>
        /// <param name="endTime">To date.</param>
        /// <returns>List of transactions.</returns>
        IEnumerable<FuelTransaction> GetFuelTransactions(DateTime startTime, DateTime endTime);

        /// <summary>
        /// Get the list of other events between the given dates.
        /// </summary>
        /// <param name="startTime">From date.</param>
        /// <param name="endTime">To date.</param>
        /// <returns>List of transactions.</returns>
        IEnumerable<OtherEvent> GetOtherEvents(DateTime startTime, DateTime endTime);

        /// <summary>
        /// Get the DOMS details.
        /// </summary>
        /// <returns>List of transactions.</returns>
        DomsDetails GetDomsDetails();

        /// <summary>
        /// Show the DOMS state.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        string ShowDomsState();

        /// <summary>
        /// Show the prepared DOMS setup.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        string ShowDomsPrepared();

        /// <summary>
        /// Show the fetched DOMS setup.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        string ShowDomsFetched();

        string ShowDomsTcp();

        /// <summary>
        /// Fetch the receipt stored for the given transaction number.
        /// </summary>
        /// <param name="transactionNumber">Transaction number for receipt.</param>
        /// <returns>The receipt.</returns>
        string FetchReceipt(long transactionNumber);

        /// <summary>
        /// Print the receipt stored for the given transaction number.
        /// </summary>
        /// <param name="transactionNumber">Transaction number for receipt.</param>
        /// <returns>Error message if failed.</returns>
        string PrintReceipt(long transactionNumber);

        /// <summary>
        /// Save the receipt stored for the given transaction number.
        /// </summary>
        /// <param name="transactionNumber">Transaction number for receipt.</param>
        /// <returns>Error message if failed.</returns>
        string SaveReceipt(long transactionNumber);

        /// <summary>
        /// Validate parameters, and fetch current status of OPTs.
        /// </summary>
        /// <param name="idString">The OPTId for filtering results</param>
        /// <param name="reference">Logging reference</param>/>
        /// <returns>Result wrapped, details of all, none or one OPT(s).</returns>
        StatusCodeResult<IEnumerable<OptDetails>> GetOpts(string idString = null, string reference = null);

        /// <summary>
        /// Sends an OptUnBlock message to the OPT
        /// </summary>
        /// <param name="idString">The target Opt Identifier, must be specified</param>
        /// <param name="reference">The Logging reference</param>
        /// <returns>Result</returns>
        StatusCodeResult SendOptUnBlock(string idString = null, string reference = null);

        /// <summary>
        /// Fetch current status of OPTs.
        /// </summary>
        /// <returns>Details of all, none or one OPT(s).</returns>
        IList<OptDetails> Opts(string idString = null);

        /// <summary>
        /// Gets Sent Config for an opt.
        /// </summary>
        /// <param name="idString">The target Opt Identifier, must be specified</param>
        /// <param name="reference">The Logging reference</param>
        /// <returns>Result wrapped, Config for OPT.</returns>
        StatusCodeResult<optMessages.Xsd.configType> GetOptSentConfig(string idString = null, string reference = null);

        /// <summary>
        /// Validate parameters, and fetch current status of pumps.
        /// </summary>
        /// <param name="pumpNumber">The pump number for filtering results</param>
        /// <param name="reference">Logging reference</param>/>
        /// <returns>Result wrapped, details of all, none or one pump(s).</returns>
        StatusCodeResult<IEnumerable<PumpDetails>> GetPumps(int pumpNumber = 0, string reference = null);

        /// <summary>
        /// Fetch current status of pumps.
        /// </summary>
        /// <param name="pumpNumber">The pump number for filtering results</param>
        /// <returns>Details of all, none or one pump(s).</returns>
        IList<PumpDetails> Pumps(int pumpNumber = 0);

        /// <summary>
        /// Fetch current status of connections.
        /// </summary>
        /// <returns>Details of all connections.</returns>
        ConnectionDetails ConnectionsDetails();

        /// <summary>
        /// Fetch current list of TIDs.
        /// </summary>
        /// <returns>Details of all TIDs.</returns>
        IList<string> Tids();

        #endregion

        #region Pump Control

        /// <summary>
        /// Set a pump to be closed (or open).
        /// </summary>
        /// <param name="pump">Number of pump to set closed or open.</param>
        /// <param name="closed">True to close pump, flase to open pump.</param>
        /// <returns>Error message if failed.</returns>
        string ClosePump(byte pump, bool closed);

        /// <summary>
        /// Set a pump to be force closed.
        /// </summary>
        /// <param name="pump">Number of pump to force closed.</param>
        /// <returns>Error message if failed.</returns>
        string ForceClosePump(byte pump);

        /// <summary>
        /// Set a pump to be forced to outside only mode.
        /// </summary>
        /// <param name="pump">Number of pump to force to outside only mode.</param>
        /// <returns>Error message if failed.</returns>
        string ForcePumpOutside(byte pump);

        /// <summary>
        /// Set max fill override for fuel cards flag.
        /// </summary>
        /// <param name="pump">Number of pump to force closed.</param>
        /// <param name="flag">Flag to indicate override or not.</param>
        /// <returns>Error message if failed.</returns>
        string SetMaxFillOverrideForFuelCards(byte pump, bool flag);

        /// <summary>
        /// Set max fill override for payment cards flag.
        /// </summary>
        /// <param name="pump">Number of pump to force closed.</param>
        /// <param name="flag">Flag to indicate override or not.</param>
        /// <returns>Error message if failed.</returns>
        string SetMaxFillOverrideForPaymentCards(byte pump, bool flag);

        /// <summary>
        /// Map a pump to a TID.
        /// </summary>
        /// <param name="pump">Number of pump to be mapped.</param>
        /// <param name="tid">TID to be mapped, or "No TID" to unmap.</param>
        /// <returns>Error message if failed.</returns>
        string MapToTid(byte pump, string tid);

        /// <summary>
        /// Map a pump to an OPT.
        /// </summary>
        /// <param name="pump">Number of pump to be mapped.</param>
        /// <param name="opt">Serial number of OPT to be mapped, or "No OPT" to unmap.</param>
        /// <param name="reference">Logging reference</param>
        /// <returns>Error message if failed.</returns>
        Result MapToOpt(byte pump, string opt, string reference = null);

        /// <summary>
        /// Set price for a grade of fuel.
        /// </summary>
        /// <param name="grade">Number of grade to set the price for.</param>
        /// <param name="price">Price to set, in pence per litre.</param>
        /// <returns>Error message if failed.</returns>
        string SetPrice(byte grade, int price);

        /// <summary>
        /// Set name for a grade of fuel.
        /// </summary>
        /// <param name="grade">Number of grade to set the name for.</param>
        /// <param name="name">Name to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetGradeName(byte grade, string name);

        /// <summary>
        /// Set VAT Rate for a grade of fuel.
        /// </summary>
        /// <param name="grade">Number of grade to set the VAT Rate for.</param>
        /// <param name="vatRate">VAT Rate to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetGradeVatRate(byte grade, float vatRate);

        #endregion

        #region Service Configuration

        /// <summary>
        /// Set the default mode for a pump.
        /// Mode should be "Kiosk Only", "Mixed" or "Outside Only".
        /// </summary>
        /// <param name="pump">Number of pump to be mapped.</param>
        /// <param name="kioskOnly">Default mode to be set.</param>
        /// <param name="outsideOnly">Default mode to be set.</param>
        /// <param name="reference">Logging reference</param>
        /// <returns>Error message if failed.</returns>
        Result SetDefaultMode(byte pump, bool kioskOnly, bool outsideOnly, string reference = null);

        /// <summary>
        /// Set Automatic Authentication on or off.
        /// </summary>
        /// <param name="isOn">True to set Automatic Authentication on, false to set it off.</param>
        /// <returns>Error message if failed.</returns>
        string SetAutoAuth(bool isOn);

        /// <summary>
        /// Set Media Channel on or off.
        /// </summary>
        /// <param name="isOn">True to set Media Channel on, false to set it off.</param>
        /// <returns>Error message if failed.</returns>
        string SetMediaChannel(bool isOn);

        /// <summary>
        /// Set Unmanned Pseudo POS on or off.
        /// </summary>
        /// <param name="isOn">True to set Unmanned Pseudo POS on, false to set it off.</param>
        /// <returns>Error message if failed.</returns>
        string SetUnmannedPseudoPos(bool isOn);

        /// <summary>
        /// Set Asda Day End Report on or off.
        /// </summary>
        /// <param name="isAsda">True to set Asda Day End Report on, false to set it off.</param>
        /// <returns>Error message if failed.</returns>
        string SetAsdaDayEndReport(bool isAsda);

        /// <summary>
        /// Set Printer enabled.
        /// </summary>
        /// <param name="isEnabled">True to set Printer on, false to set it off.</param>
        /// <returns>Error message if failed.</returns>
        string SetPrinterEnabled(bool isEnabled);

        /// <summary>
        /// Set Printer Port Name.
        /// </summary>
        /// <param name="portName">Port Name to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetPrinterPortName(string portName);

        /// <summary>
        /// Set Printer Baud Rate.
        /// </summary>
        /// <param name="baudRate">Baud Rate to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetPrinterBaudRate(int baudRate);

        /// <summary>
        /// Set Printer Handshake.
        /// </summary>
        /// <param name="handshake">Handshake to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetPrinterHandshake(Handshake handshake);

        /// <summary>
        /// Set Printer Stop Bits.
        /// </summary>
        /// <param name="stopBits">Stop Bits to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetPrinterStopBits(StopBits stopBits);

        /// <summary>
        /// Set Printer Data Bits.
        /// </summary>
        /// <param name="dataBits">Data Bits to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetPrinterDataBits(int dataBits);

        /// <summary>
        /// Set an OPT to allow or disallow contactless.
        /// </summary>
        /// <param name="isAllowed">True to set Contactless Allowed, false to set it Disallowed.</param>
        /// <returns>Error message if failed.</returns>
        string SetContactlessAllowed(bool isAllowed);

        /// <summary>
        /// Set the pre-auth limit for contactless cards to <paramref name="limit"/>.
        /// </summary>
        /// <param name="limit">Pre-auth limit in pence.</param>
        string SetContactlessCardPreAuth(uint limit);

        /// <summary>
        /// Set the pre-auth limit for contactless devices to <paramref name="limit"/>.
        /// </summary>
        /// <param name="limit">Pre-auth limit in pence.</param>
        string SetContactlessDevicePreAuth(uint limit);

        /// <summary>
        /// Set the TTQ to <paramref name="ttq"/> required for some cards.
        /// </summary>
        /// <param name="ttq">TTQ value.</param>
        string SetContactlessTtq(string ttq);

        /// <summary>
        /// Show a single button for contactless on the OPT.
        /// </summary>
        /// <param name="showSingleButton">Show single button if enabled.</param>
        /// <returns></returns>
        string SetContactlessSingleButton(bool showSingleButton);

        /// <summary>
        /// Set the receipt headers for an OPT.
        /// </summary>
        /// <param name="optIdString">Serial number of OPT to set the receipt headers for.</param>
        /// <param name="headers">Receipt headers to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetReceiptHeaders(string optIdString, string[] headers);

        /// <summary>
        /// Set the receipt footers for an OPT.
        /// </summary>
        /// <param name="optIdString">Serial number of OPT to set the receipt footers for.</param>
        /// <param name="footers">Receipt footers to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetReceiptFooters(string optIdString, string[] footers);

        /// <summary>
        /// Set the playlist file name for an OPT.
        /// </summary>
        /// <param name="optIdString">Serial number of OPT to set the playlist file name for.</param>
        /// <param name="filename">File name to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetPlaylistFileName(string optIdString, string filename);

        /// <summary>
        /// Send Restart to OPT.
        /// </summary>
        /// <param name="optIdString">Serial number of OPT.</param>
        /// <returns>Error message if failed.</returns>
        string RestartOpt(string optIdString);

        /// <summary>
        /// Send Refresh configuration to OPT.
        /// </summary>
        /// <param name="optIdString">Serial number of OPT.</param>
        /// <returns>Error message if failed.</returns>
        string RefreshOpt(string optIdString = null);

        /// <summary>
        /// Send Request log file to OPT.
        /// </summary>
        /// <param name="optIdString">Serial number of OPT.</param>
        /// <returns>Error message if failed.</returns>
        string RequestOptLog(string optIdString = null);

        /// <summary>
        /// Send Request to engineer's reset OPT to factory defaults.
        /// </summary>
        /// <param name="optIdString">Serial number of OPT.</param>
        /// <returns>Error message if failed.</returns>
        string EngineerResetOpt(string optIdString);

        /// <summary>
        /// Sets the (payment) timeout value for the given timeout type
        /// </summary>
        /// <param name="mode">Timeout type</param>
        /// <param name="timeout">Timeout value</param>
        /// <returns>Result</returns>
        Result SetPaymentTimeout(PaymentTimeoutType mode, int timeout);

        /// <summary>
        /// Set the payment timeout for a given OPT mode.
        /// Mode should be "POD", "OPT", "Mixed" or "Nozzle Down".
        /// </summary>
        /// <param name="mode">Mode to set timeout for.</param>
        /// <param name="timeout">Timeout to set, in seconds.</param>
        /// <returns>Error message if failed.</returns>
        [Obsolete("Use the PaymentTimeoutType typed version instead")]
        string SetPaymentTimeout(string mode, int timeout);

        /// <summary>
        /// Set the receipt reprint availability timeout.
        /// </summary>
        /// <param name="timeout">Timeout to set, in seconds.</param>
        /// <returns>Error message if failed.</returns>
        string SetReceiptTimeout(int timeout);

        /// <summary>
        /// Set the receipt maximum count.
        /// </summary>
        /// <param name="count">Count to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetReceiptMaxCount(int count);

        /// <summary>
        /// Set Forward Fuel Price Update on or off.
        /// </summary>
        /// <param name="isOn">True to set Forward Fuel Price Update on, false to set it off.</param>
        /// <returns>Error message if failed.</returns>
        string SetForwardFuelPriceUpdate(bool isOn);

        /// <summary>
        /// Perform Day End.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [Obsolete("Left in for Unit tests only!")]
        string PerformDayEnd();

        /// <summary>
        /// Perform Shift End.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        [Obsolete("Left in for Unit tests only!")]
        string PerformShiftEnd();

        /// <summary>
        /// Set next auto Day End time.
        /// </summary>
        /// <param name="dayEnd">Next day end time to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetNextDayEnd(DateTime? dayEnd);

        /// <summary>
        /// Set log interval in hours, minutes and seconds.
        /// </summary>
        /// <param name="hours">Hours to set.</param>
        /// <param name="minutes">Minutes to set.</param>
        /// <param name="seconds">Seconds to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetLogInterval(int hours, int minutes, int seconds);

        #endregion

        #region Service Installation

        /// <summary>
        /// Upgrade the service.
        /// This will install the version of the service in the folder OPTServiceUpgrade.
        /// </summary>
        void UpgradeService();

        /// <summary>
        /// Rollback the service.
        /// This will install the version of the service in the folder OPTServiceArchive.
        /// </summary>
        void RollbackService();

        /// <summary>
        /// Restart the service.
        /// This will stop and restart the service.
        /// </summary>
        void RestartService();

        string SaveFile(string fileName);
        string SaveSoftwareFile(string fileName);
        string SaveWhitelistFile(string fileName);
        string SaveLayoutFile(string fileName);
        string SaveMediaFile(string fileName);
        string SavePlaylistFile(string fileName);
        string SaveContactlessPropertiesFile(string fileName);
        string RemoveFile(string fileName);
        string RemoveWhitelistFile(string fileName);
        string RemoveLayoutFile(string fileName);
        string RemoveUpgradeFile(string fileName);
        string RemoveSoftwareFile(string fileName);
        string RemoveMediaFile(string fileName);
        string RemovePlaylistFile(string fileName);
        string RemoveDatabaseBackupFile(string fileName);
        string RemoveOptLogFile(string fileName);
        string DatabaseBackup();
        string DatabaseRestore(string fileName);

        /// <summary>
        /// Fetch an object containing all site configuration details.
        /// </summary>
        /// <returns>Result wrapped configuration details.</returns>
        StatusCodeResult<object> ConfigExtract();

        #endregion

        #region Connection Configuration

        /// <summary>
        /// Set the port for the From OPT connection (outbound port).
        /// </summary>
        /// <param name="port">The port number to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetFromOptPort(int port);

        /// <summary>
        /// Set the port for the To OPT connection (inbound port).
        /// </summary>
        /// <param name="port">The port number to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetToOptPort(int port);

        /// <summary>
        /// Set the port for the Heartbeat connection (heartbeat port).
        /// </summary>
        /// <param name="port">The port number to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetHeartbeatPort(int port);

        /// <summary>
        /// Set the port for the Hydra POS connection.
        /// </summary>
        /// <param name="port">The port number to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetHydraPosPort(int port);

        /// <summary>
        /// Set the port for the Retalix POS connection.
        /// </summary>
        /// <param name="port">The port number to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetRetalixPosPort(int port);

        /// <summary>
        /// Set the port for the Third Party POS connection.
        /// </summary>
        /// <param name="port">The port number to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetThirdPartyPosPort(int port);

        /// <summary>
        /// Set the port for the Media Channel connection.
        /// </summary>
        /// <param name="port">The port number to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetMediaChannelPort(int port);

        /// <summary>
        /// Set the port for the ANPR Link connection.
        /// </summary>
        /// <param name="port">The port number to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetAnprPort(int port);

        /// <summary>
        /// Set the IP Address for the ANPR Link connection.
        /// </summary>
        /// <param name="address">The IP Address to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetAnprAddress(IPAddress address);

        /// <summary>
        /// Set the port for the Car Wash connection.
        /// </summary>
        /// <param name="port">The port number to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetCarWashPort(int port);

        /// <summary>
        /// Set the IP Address for the Car Wash connection.
        /// </summary>
        /// <param name="address">The IP Address to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetCarWashAddress(IPAddress address);

        /// <summary>
        /// Set the port for the Hydra Mobile connection.
        /// </summary>
        /// <param name="port">The port number to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetHydraMobilePort(int port);

        /// <summary>
        /// Set the IP Address for the Hydra Mobile connection.
        /// </summary>
        /// <param name="address">The IP Address to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetHydraMobileAddress(IPAddress address);

        /// <summary>
        /// Set the Ports and IP Addresses for all connections.
        /// </summary>
        /// <param name="fromOptPort">The Port for the From OPT connection.</param>
        /// <param name="toOptPort">The Port for the To OPT connection.</param>
        /// <param name="heartbeatPort">The Port for the Heartbeat connection.</param>
        /// <param name="hydraPosPort">The Port for the Hydra POS connection.</param>
        /// <param name="retalixPosPort">The Port for the Retalix POS connection.</param>
        /// <param name="thirdPartyPosPort">The Port for the Third Party POS connection.</param>
        /// <param name="mediaChannelPort">The Port for the Media Channel connection.</param>
        /// <param name="siteControllerAddress">The IP Address for the Site Controller connection.</param>
        /// <param name="siteControllerPort">The Port for the Site Controller connection.</param>
        /// <param name="anprAddress">The IP Address for the ANPR Link connection.</param>
        /// <param name="anprPort">The Port for the ANPR Link connection.</param>
        /// <param name="carWashAddress">The IP Address for the Car Wash connection.</param>
        /// <param name="carWashPort">The Port for the Car Wash connection.</param>
        /// <param name="tankGaugeAddress">The IP Address for the Tank Gauge connection.</param>
        /// <param name="tankGaugePort">The Port for the Tank Gauge connection.</param>
        /// <param name="hydraMobileAddress">The IP Address for the Hydra Mobile connection.</param>
        /// <param name="hydraMobilePort">The Port for the Hydra Mobile connection.</param>
        /// <returns>Error message if failed.</returns>
        string SetAllEndpoints
        (int fromOptPort, int toOptPort, int heartbeatPort, int hydraPosPort, int retalixPosPort, int thirdPartyPosPort,
            int mediaChannelPort, IPAddress siteControllerAddress, int siteControllerPort, IPAddress anprAddress, int anprPort,
            IPAddress carWashAddress, int carWashPort, IPAddress tankGaugeAddress, int tankGaugePort, IPAddress hydraMobileAddress,
            int hydraMobilePort);

        /// <summary>
        /// Set the IP Address for the Primary Retalix POS connection.
        /// </summary>
        /// <param name="address">The IP Address to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetRetalixPosPrimaryIpAddress(IPAddress address);

        #endregion

        #region OPT Configuration

        /// <summary>
        /// Set the IP Address for the OPT connection in the OPT Config.
        /// </summary>
        /// <param name="address">The IP Address to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetServiceAddress(IPAddress address);

        /// <summary>
        /// Add an eSocket.POS address to the OPT Config.
        /// </summary>
        /// <param name="address">The IP Address to add.</param>
        /// <param name="port">The port to add.</param>
        /// <returns>Error message if failed.</returns>
        string AddEsocket(IPAddress address, int port);

        /// <summary>
        /// Add an eSocket.POS address to the OPT Config.
        /// </summary>
        /// <param name="address">The IP Address to add.</param>
        /// <param name="port">The port to add.</param>
        /// <returns>Error message if failed.</returns>
        string RemoveEsocket(IPAddress address, int port);

        /// <summary>
        /// Set alternative address and port numbers for the OPT Config.
        /// </summary>
        /// <param name="address">The IP Address to set.</param>
        /// <param name="fromOptPort">The From OPT Port to set.</param>
        /// <param name="toOptPort">The To OPT Port to set.</param>
        /// <param name="heartbeatPort">The Heartbeat Port to set.</param>
        /// <param name="mediaChannelPort">The Media Channel Port to set.</param>
        /// <returns>Error message if failed.</returns>
        string DivertOptService(IPAddress address, int fromOptPort, int toOptPort, int heartbeatPort, int mediaChannelPort);

        /// <summary>
        /// Cancel the alternative address and port numbers for the OPT Config.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        string CancelDivertOptService();

        /// <summary>
        /// Start or stop a Config Batch.
        /// Once a batch has started, config changes will not be notified to the OPT until the batch is stopped.
        /// </summary>
        /// <param name="isBatch">True to start the batch, false to stop it.</param>
        /// <returns>Error message if failed.</returns>
        string SetConfigBatch(bool isBatch);

        /// <summary>
        /// Set the Loyalty section of the OPT Config to be available.
        /// </summary>
        /// <param name="name">Name of Loyalty to add.</param>
        /// <returns>Error message if failed.</returns>
        string AddLoyalty(string name);

        /// <summary>
        /// Set the Loyalty section of the OPT Config to be unavailable.
        /// </summary>
        /// <param name="name">Name of Loyalty to delete.</param>
        /// <returns>Error message if failed.</returns>
        string DeleteLoyalty(string name);

        /// <summary>
        /// Set the Loyalty section of the OPT Config to be present or absent.
        /// </summary>
        /// <param name="present">Present if true, absent otherwise.</param>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyPresent(string name, bool present);

        /// <summary>
        /// Set the Site ID for the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <param name="siteId">Site ID to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyTerminalSiteId(string name, string siteId);

        /// <summary>
        /// Set the Terminal ID for the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <param name="terminalId">Terminal ID to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyTerminalTerminalId(string name, string terminalId);

        /// <summary>
        /// Set the Footer 1 for the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <param name="footer1">Footer 1 to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyTerminalFooter1(string name, string footer1);

        /// <summary>
        /// Set the Footer 2 for the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <param name="footer2">Footer 2 to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyTerminalFooter2(string name, string footer2);

        /// <summary>
        /// Set the Timeout for the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <param name="timeout">Timeout to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyTerminalTimeout(string name, int timeout);

        /// <summary>
        /// Set the API Key for the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <param name="apiKey">API Key to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyTerminalApiKey(string name, string apiKey);

        /// <summary>
        /// Set the HTTP Header for the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <param name="httpHeader">HTTP Header to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyTerminalHttpHeader(string name, string httpHeader);

        /// <summary>
        /// Add a host to the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <param name="address">IP Address of host to add.</param>
        /// <param name="port">Port number of host to add.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyHostsAddHost(string name, IPAddress address, int port);

        /// <summary>
        /// Remove a host from the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <param name="address">IP Address of host to remove.</param>
        /// <param name="port">Port number of host to remove.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyHostsRemoveHost(string name, IPAddress address, int port);

        /// <summary>
        /// Add a hostname to the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <param name="hostname">Hostname of host to add.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyHostnamesAddHostname(string name, string hostname);

        /// <summary>
        /// Remove a hostname from the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <param name="hostname">Hostname of host to remove.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyHostnamesRemoveHostname(string name, string hostname);

        /// <summary>
        /// Add an IIN to the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <param name="low">Bottom of IIN range to add.</param>
        /// <param name="high">Top of IIN range to add.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyIinsAddIin(string name, string low, string high);

        /// <summary>
        /// Remove an IIN from the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <param name="low">Bottom of IIN range to remove.</param>
        /// <param name="high">Top of IIN range to remove.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyIinsRemoveIin(string name, string low, string high);

        /// <summary>
        /// Add a Tariff Mapping to the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <param name="productCode">Product Code of the mapping to add.</param>
        /// <param name="loyaltyCode">Loyalty Code of the mapping to add.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyMappingsAddMapping(string name, string productCode, string loyaltyCode);

        /// <summary>
        /// Remove a Tariff Mapping from the Loyalty section of the OPT Config.
        /// </summary>
        /// <param name="name">Name of Loyalty to set.</param>
        /// <param name="productCode">Product Code of the mapping to remove.</param>
        /// <param name="loyaltyCode">Loyalty Code of the mapping to remove.</param>
        /// <returns>Error message if failed.</returns>
        string SetLoyaltyMappingsRemoveMapping(string name, string productCode, string loyaltyCode);

        /// <summary>
        /// Add a Wash to the Washes section of the OPT Config.
        /// </summary>
        /// <param name="programId">Program Id of the wash to add.</param>
        /// <param name="productCode">Product Code of the wash to add.</param>
        /// <param name="description">Description of the wash to add.</param>
        /// <param name="price">Price of the wash to add.</param>
        /// <param name="vatRate">VAT Rate of the wash to add.</param>
        /// <param name="category">Transaction category of the wash to add.</param>
        /// <param name="subcategory">Transaction subcategory of the wash to add.</param>
        /// <returns>Error message if failed.</returns>
        string AddWash
            (byte programId, string productCode, string description, float price, float vatRate, short category, short subcategory);

        /// <summary>
        /// Remove a Wash from the Washes section of the OPT Config.
        /// </summary>
        /// <param name="programId">Program Id of the wash to remove.</param>
        /// <returns>Error message if failed.</returns>
        string RemoveWashByProgramId(byte programId);

        /// <summary>
        /// Add a Tariff Mapping to the Cards section of the OPT Config.
        /// </summary>
        /// <param name="grade">Grade of the mapping to add.</param>
        /// <param name="productCode">Product Code of the mapping to add.</param>
        /// <param name="fuelCardsOnly">Fuel Cards Only flag of the mapping to add.</param>
        /// <returns>Error message if failed.</returns>
        string SetTariffMappingsAddMapping(byte grade, string productCode, bool fuelCardsOnly);

        /// <summary>
        /// Remove a Tariff Mapping from the Cards section of the OPT Config.
        /// </summary>
        /// <param name="grade">Grade of the mapping to remove.</param>
        /// <param name="productCode">Product Code of the mapping to remove.</param>
        /// <returns>Error message if failed.</returns>
        string SetTariffMappingsRemoveMapping(byte grade, string productCode);

        /// <summary>
        /// Set the Fuel Cards Only flag for a Tariff Mapping in the Cards section of the OPT Config.
        /// </summary>
        /// <param name="grade">Grade of the mapping to set.</param>
        /// <param name="flag">Flag to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetTariffMappingFuelCardsOnly(byte grade, bool flag);

        /// <summary>
        /// Add a Predefined Amount to the OPT Config.
        /// </summary>
        /// <param name="amount">Amount to add.</param>
        /// <returns>Error message if failed.</returns>
        string AddPredefinedAmount(int amount);

        /// <summary>
        /// Remove a Predefined Amount from the OPT Config.
        /// </summary>
        /// <param name="amount">Amount to remove.</param>
        /// <returns>Error message if failed.</returns>
        string RemovePredefinedAmount(int amount);

        /// <summary>
        /// Add a discount card to the OPT Config.
        /// </summary>
        /// <param name="iin">IIN of Discount Card.</param>
        /// <param name="name">Name of Discount Card.</param>
        /// <param name="type">Type of Discount Card.</param>
        /// <param name="value">Value of Discount Card.</param>
        /// <param name="grade">Grade of Discount Card.</param>
        /// <returns>Error message if failed.</returns>
        string AddDiscountCard(string iin, string name, string type, float value, byte grade);

        /// <summary>
        /// Remove a discount card from the OPT Config.
        /// </summary>
        /// <param name="iin">IIN of Discount Card.</param>
        /// <returns>Error message if failed.</returns>
        string RemoveDiscountCard(string iin);

        /// <summary>
        /// Add a whitelist card to a discount card in the OPT Config.
        /// </summary>
        /// <param name="iin">IIN of Discount Card.</param>
        /// <param name="pan">IIN of Whitelist Card.</param>
        /// <returns>Error message if failed.</returns>
        string AddDiscountWhitelist(string iin, string pan);

        /// <summary>
        /// Remove a whitelist card from a discount card in the OPT Config.
        /// </summary>
        /// <param name="iin">IIN of Discount Card.</param>
        /// <param name="pan">IIN of Whitelist Card.</param>
        /// <returns>Error message if failed.</returns>
        string RemoveDiscountWhitelist(string iin, string pan);

        /// <summary>
        /// Set the Receipt Layout for the OPT Config.
        /// </summary>
        /// <param name="mode">Layout to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetReceiptLayoutMode(int mode);

        /// <summary>
        /// Set the Site Name for the OPT Config.
        /// </summary>
        /// <param name="name">Name to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetSiteName(string name);

        /// <summary>
        /// Set the VAT Number for the OPT Config.
        /// </summary>
        /// <param name="number">Number to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetVatNumber(string number);

        /// <summary>
        /// Set the Currency Code for the OPT Config.
        /// </summary>
        /// <param name="number">Number to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetCurrencyCode(int number);

        /// <summary>
        /// Set the Nozzle Up For Kiosk Use flag in the OPT Config.
        /// </summary>
        /// <param name="flag">Flag to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetNozzleUpForKioskUse(bool flag);

        /// <summary>
        /// Set the Use Replace Nozzle Screen flag in the OPT Config.
        /// </summary>
        /// <param name="flag">Flag to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetUseReplaceNozzleScreen(bool flag);

        /// <summary>
        /// Get the Max Fill Override in the OPT Config
        /// </summary>
        /// <returns>The Max Fill Override</returns>
        uint GetMaxFillOverride();

        /// <summary>
        /// Set the Max Fill Override in the OPT Config.
        /// </summary>
        /// <param name="maxFillOverride">Value to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetMaxFillOverride(uint maxFillOverride);

        /// <summary>
        /// Set the Till Number for Transactions.
        /// </summary>
        /// <param name="number">Till number to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetTillNumber(short number);

        /// <summary>
        /// Set the Fuel Category for transactions.
        /// </summary>
        /// <param name="category">Category to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetFuelCategory(short category);

        /// <summary>
        /// Set the reference number for a card type.
        /// </summary>
        /// <param name="name">Card type.</param>
        /// <param name="reference">Reference number to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetCardReference(string name, int reference);

        /// <summary>
        /// Clear a card type.
        /// </summary>
        /// <param name="name">Card type to clear.</param>
        /// <returns>Error message if failed.</returns>
        string ClearCardReference(string name);

        /// <summary>
        /// Set the acquirer reference for a card type.
        /// </summary>
        /// <param name="cardName">Card type.</param>
        /// <param name="acquirerName">Acquirer reference to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetAcquirerReference(string cardName, string acquirerName);

        /// <summary>
        /// Clear the acquirer reference for a card type.
        /// </summary>
        /// <param name="cardName">Card type.</param>
        /// <returns>Error message if failed.</returns>
        string ClearAcquirerReference(string cardName);

        /// <summary>
        /// Set the Fuel Card flag for a card type.
        /// </summary>
        /// <param name="cardName">Card type.</param>
        /// <param name="isFuelCard">Fuel card flag.</param>
        /// <returns>Error message if failed.</returns>
        string SetFuelCard(string cardName, bool isFuelCard);

        /// <summary>
        /// Set the External carn name for a card type.
        /// </summary>
        /// <param name="cardName">Card type.</param>
        /// <param name="externalCardName">External card name.</param>
        /// <returns>Error message if failed.</returns>
        string SetExternalName(string cardName, string externalCardName);

        /// <summary>
        /// Clear the external name for a card type.
        /// </summary>
        /// <param name="cardName">Card type.</param>
        /// <returns>Error message if failed.</returns>
        string ClearExternalName(string cardName);

        /// <summary>
        /// Reload the OPT configuration from the database.
        /// </summary>
        void ReloadOptConfiguration();

        #endregion

        #region File Locations

        /// <summary>
        /// Set the Retalix Transaction File Directory.
        /// </summary>
        /// <param name="directory">Directory to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetRetalixTransactionFileDirectory(string directory);

        /// <summary>
        /// Clear the Retalix Transaction File Directory.
        /// </summary>
        /// <returns>Error message if failed.</returns>
        string ClearRetalixTransactionFileDirectory();

        /// <summary>
        /// Set the Transaction File Directory.
        /// </summary>
        /// <param name="directory">Directory to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetTransactionFileDirectory(string directory);

        /// <summary>
        /// Set the Whitelist Directory.
        /// </summary>
        /// <param name="directory">Directory to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetWhitelistDirectory(string directory);

        /// <summary>
        /// Set the Layout Directory.
        /// </summary>
        /// <param name="directory">Directory to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetLayoutDirectory(string directory);

        /// <summary>
        /// Set the Software Directory.
        /// </summary>
        /// <param name="directory">Directory to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetSoftwareDirectory(string directory);

        /// <summary>
        /// Set the Media Directory.
        /// </summary>
        /// <param name="directory">Directory to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetMediaDirectory(string directory);

        /// <summary>
        /// Set the Playlist Directory.
        /// </summary>
        /// <param name="directory">Directory to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetPlaylistDirectory(string directory);

        /// <summary>
        /// Set the OPT Log File Directory.
        /// </summary>
        /// <param name="directory">Directory to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetOptLogFileDirectory(string directory);

        /// <summary>
        /// Set the Log File Directory.
        /// </summary>
        /// <param name="directory">Directory to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetLogFileDirectory(string directory);

        /// <summary>
        /// Set the Trace File Directory.
        /// </summary>
        /// <param name="directory">Directory to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetTraceFileDirectory(string directory);

        /// <summary>
        /// Set the Journal File Directory.
        /// </summary>
        /// <param name="directory">Directory to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetJournalFileDirectory(string directory);

        /// <summary>
        /// Set the Received Update Directory.
        /// </summary>
        /// <param name="directory">Directory to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetReceivedUpdateDirectory(string directory);

        /// <summary>
        /// Set the Contactless Properties File.
        /// </summary>
        /// <param name="fileName">File name to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetContactlessPropertiesFile(string fileName);

        /// <summary>
        /// Set the Fuel Data Update File.
        /// </summary>
        /// <param name="fileName">File name to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetFuelDataUpdateFile(string fileName);

        /// <summary>
        /// Set the Upgrade File Directory.
        /// </summary>
        /// <param name="directory">Directory to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetUpgradeFileDirectory(string directory);

        /// <summary>
        /// Set the Rollback File Directory.
        /// </summary>
        /// <param name="directory">Directory to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetRollbackFileDirectory(string directory);

        /// <summary>
        /// Set the Database Backup Directory.
        /// </summary>
        /// <param name="directory">Directory to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetDatabaseBackupDirectory(string directory);

        /// <summary>
        /// Set the eSocket.POS Connection String.
        /// </summary>
        /// <param name="newConnectionString">String to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetEsocketConnectionString(string newConnectionString);

        /// <summary>
        /// Set eSocket.POS database connection to use connection string or config file.
        /// </summary>
        /// <param name="useConnectionString">Use connection string if true, use config file otherwise.</param>
        /// <returns>Error message if failed.</returns>
        string SetEsocketUseConnectionString(bool useConnectionString);

        /// <summary>
        /// Set the eSocket.POS Config File location.
        /// </summary>
        /// <param name="fileName">File location to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetEsocketConfigFile(string fileName);

        /// <summary>
        /// Set the eSocket.POS Keystore File location.
        /// </summary>
        /// <param name="fileName">File location to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetEsocketKeystoreFile(string fileName);

        /// <summary>
        /// Set the eSocket.POS database URL.
        /// </summary>
        /// <param name="url">URL to set.</param>
        /// <returns>Error message if failed.</returns>
        string SetEsocketDbUrl(string url);

        /// <summary>
        /// Set whether to override propreties file location for eSocket.POS database connection.
        /// </summary>
        /// <param name="flag">Override properties file location if true, do not override otherwise.</param>
        /// <returns>Error message if failed.</returns>
        string SetEsocketOverrideProperties(bool flag);

        /// <summary>
        /// Set whether to override keystore file location for eSocket.POS database connection.
        /// </summary>
        /// <param name="flag">Override keystore file location if true, do not override otherwise.</param>
        /// <returns>Error message if failed.</returns>
        string SetEsocketOverrideKeystore(bool flag);

        /// <summary>
        /// Set whether to override database URL for eSocket.POS database connection.
        /// </summary>
        /// <param name="flag">Override database URL if true, do not override otherwise.</param>
        /// <returns>Error message if failed.</returns>
        string SetEsocketOverrideUrl(bool flag);

        /// <summary>
        /// Set whether to override contactless properties file for eSocket.POS.
        /// </summary>
        /// <param name="flag">Override contactless properties file if true, do not override otherwise.</param>
        /// <returns>Error message if failed.</returns>
        string SetEsocketOverrideContactless(bool flag);

        #endregion

        #region Local Accounts

        string RemoveLocalAccountCustomer(string customerReference);

        string AddLocalAccountCustomer
        (string customerReference, string name, bool transactionsAllowed, uint transactionLimit, bool fuelOnly, bool registrationEntry,
            bool mileageEntry, bool prepayAccount, bool lowCreditWarning, bool maxCreditReached, bool pin, bool printValue,
            bool allowLoyalty);

        /// <summary>
        /// Updates the flags for a local account customer based on the provided request model.
        /// </summary>
        /// <param name="request">An instance of <see cref="LocalAccountCustomerSetFlagsRequest"/> containing the details of the flags to be updated. </param>
        /// <param name="reference"> An optional reference string for tracking or identification purposes.</param>
        /// <returns>
        /// A <see cref="StatusCodeResult"/> indicating the result of the update operation.
        /// </returns>
        StatusCodeResult SetLocalAccountCustomerFlags(LocalAccountCustomerSetFlagsRequest request, string reference = null);

        string SetLocalAccountCustomerBalance(string customerReference, uint balance);

        string AddLocalAccountCardWithoutRestrictions(string customerReference, string pan, string description, float discount);

        string AddLocalAccountCardWithRestrictions
        (string customerReference, string pan, string description, float discount, bool unleaded, bool diesel, bool lpg, bool lrp,
            bool gasOil, bool adBlue, bool kerosene, bool oil, bool avgas, bool jet, bool mogas, bool valeting, bool otherMotorRelatedGoods,
            bool shopGoods);

        string SetLocalAccountCardHot(string pan, bool hot);

        string RemoveLocalAccountCard(string pan);

        /// <summary>
        /// Sets local accounts enabled flag
        /// </summary>
        /// <param name="enabled">flag value</param>
        /// <returns>result of the operation</returns>
        string SetLocalAccountsEnabled(bool enabled);

        #endregion

        #region DOMS

        string CheckDomsState();
        string ResetDoms();
        string MasterResetDoms();
        string ReconnectDoms();
        string ClearDomsTransaction(byte fpId, int seqNo);
        string AuthoriseDoms(byte fpId, uint limit);
        string DomsEmergencyStop(byte fpId);
        string DomsCancelEmergencyStop(byte fpId);

        #endregion

        #region Fuelling

        string SetFuellingIndefiniteWait(bool flag);
        string SetFuellingWaitMinutes(int minutes);
        string SetFuellingBackoffAuth(int backoff);
        string SetFuellingBackoffPreAuth(int backoff);
        string SetFuellingBackoffStopStart(int backoff);
        string SetFuellingBackoffStopOnly(int backoff);
        string SetPosClaimNumber(byte number);

        #endregion

        #region Prune Days

        string SetFilePruneDays(int days);
        string SetTransactionPruneDays(int days);
        string SetReceiptPruneDays(int days);

        #endregion

        #region Categories

        /// <summary>
        /// Sets the list of categories into the database
        /// </summary>
        /// <param name="categories">list of categories to set</param>
        /// <returns>null or description of the error</returns>
        Result SetCategories(IEnumerable<CategoryConfiguration> categories);

        #endregion

        #region Password

        string CheckPassword(string username, string password);

        #endregion

        void SendChatMessage(string name, string message);
        ShiftEndDetails GetShiftEndDetails();

        Result SetIntegrationType(IntegrationType integrationType, string value);

        bool IsPumpControllerEnabled();
    }
}