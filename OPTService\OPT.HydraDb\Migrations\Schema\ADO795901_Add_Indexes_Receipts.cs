﻿using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.IO.Abstractions;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <summary>
    /// Migration to add indexes to dbo.Receipts table for improved query performance
    /// </summary>
    [Migration(28200001, "ADO#795901 - Add indexes to dbo.Receipts"), Tags(Constants.MigrationTypeSchema)]
    public class ADO795901_Add_Indexes_Receipts : Migrationable
    {
        private const string TableName = "Receipts";
        private const string IndexNamePrefix = "ix_" + TableName + "_";

        /// <inheritdoc/>
        public ADO795901_Add_Indexes_Receipts(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
        }

        /// <inheritdoc/>
        public override void Up()
        {
            var indexName = IndexNamePrefix + "Hash";
            if (!Schema.Table(TableName).Index(indexName).Exists())
            {
                Create.Index(indexName).OnTable(TableName)
                    .OnColumn("CardNumber").Ascending()
                    .OnColumn("TransactionNumber").Ascending()
                    .OnColumn("OPT").Ascending()
                    .OnColumn("ReceiptHash").Ascending()
                    .OnColumn("Amount").Ascending()
                    .OnColumn("TransactionTime").Ascending()
                    .OnColumn("Expiry").Ascending()
                    .WithOptions().NonClustered();
            }

            indexName = IndexNamePrefix + "Opt_Expiry";
            if (!Schema.Table(TableName).Index(indexName).Exists())
            {
                Create.Index(indexName).OnTable(TableName)
                    .OnColumn("OPT").Ascending()
                    .OnColumn("Expiry").Ascending()
                    .WithOptions().NonClustered();
            }
        }

        /// <inheritdoc/>
        public override void Down()
        {
            var indexName = IndexNamePrefix + "opt_expiry";
            if (Schema.Table(TableName).Index(indexName).Exists())
            {
                Delete.Index(indexName).OnTable(TableName);
            }

            indexName = IndexNamePrefix + "hash";
            if (Schema.Table(TableName).Index(indexName).Exists())
            {
                Delete.Index(indexName).OnTable(TableName);
            }
        }
    }
}

