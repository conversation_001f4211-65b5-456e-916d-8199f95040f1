﻿using CSharpFunctionalExtensions;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Forecourt.SecondaryAuth.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Core.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Models;
using Htec.Foundation.Workers;
using Htec.Foundation.Workers.Interfaces;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Bos.Messages;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pos.Messages;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Logger.Interfaces;
using OPT.Common.Workers.Interfaces;
using System;
using System.Collections.Generic;
using System.Net;
using bos = Htec.Hydra.Core.Bos.Messages;
using bosCore = Htec.Hydra.Core.Bos.Interfaces.Core;
using IBosIntegratorOutOfflineTransient = Htec.Hydra.Core.Bos.Interfaces.IBosIntegratorOutOfflineTransient;
using posCore = Htec.Hydra.Core.Pos.Interfaces.Core;

namespace Forecourt.Common.Workers
{
    /// <summary>
    /// <see cref="Workerable"/> class that acts as a broker between the various workers in the service
    /// </summary>
    public class MessageBroker : Workerable, IMessageBroker
    {
        bool ISecAuthIntegratorOutTransient<IMessageTracking>.TimedOutResponse => GetWorker<ISecAuthIntegratorOutTransient<IMessageTracking>>()?.TimedOutResponse ?? true;

        /// <inheritdoc/>
        public MessageBroker(IHtecLogManager logManager, IConfigurationManager configurationManager) : base(logManager, nameof(MessageBroker), configurationManager)
        {
        }

        private Result DoActionOnWorkers<TWorker>(TWorker worker, Func<TWorker, Result> action) where TWorker: IWorkerable
        {
            var connectable = worker as IConnectable;
            return (worker.IsActive && (connectable?.IsConnected() ?? true)) ? action(worker) : Result.Success();
        }

        private Result<TResult> DoActionOnWorkers<TWorker, TResult>(TWorker worker, Func<TWorker, Result<TResult>> action) where TWorker : IWorkerable
        {
            var connectable = worker as IConnectable;
            return (worker.IsActive && (connectable?.IsConnected() ?? true)) ? action(worker) : Result.Success<TResult>(default);
        }

        // This one is specific to MessageBroker, not public!
        private Result<StatusCodeResult> MapResult(Result<StatusCodeResult> result) =>
            result.IsSuccess && result.Value == null ? Result.Success(StatusCodeResult.Specific(HttpStatusCode.NotImplemented)) : result;

        /// <inheritdoc />
        public Result IsConnectableConnected<TWorkerable>() where TWorkerable : IWorkerable
        {
            return IsConnectableConnected(GetWorker<TWorkerable>());
        }

        private Result IsConnectableConnected(IWorkerable worker)
        {
            var connectable = worker as IConnectable;
            return Result.SuccessIf((connectable?.IsConnected() ?? false) && (connectable?.ConnectedCount > 0), "!IsConnected");
        }

        /// <inheritdoc cref="IPumpIntegratorInSetup"/>
        public Result ClosePump(byte pump, string loggingReference = null)
        {
            return DoActionOnWorkers<IPumpIntegratorInSetup>((w) => w.ClosePump(pump, loggingReference), loggingReference);
        }

        /// <inheritdoc cref="IPumpIntegratorInSetup"/>
        public void Disable()
        {
            DoActionOnWorkers<IPumpIntegratorInSetup>((w) => w.Disable(), null);
        }

        /// <inheritdoc cref="IPumpIntegratorInTransient{TMessageTracking}"/>
        public Result EmergencyStop(byte pump, string reg, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPumpIntegratorInTransient<IMessageTracking>>((w) => w.EmergencyStop(pump, reg, message), message.FullId);
        }

        /// <inheritdoc cref="IPumpIntegratorInTransient{TMessageTracking}"/>
        public Result EmergencyStopCancel(byte pump, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPumpIntegratorInTransient<IMessageTracking>>((w) => w.EmergencyStopCancel(pump, message), message.FullId);
        }

        /// <inheritdoc cref="IPumpIntegratorInTransient{TMessageTracking}"/>
        public Result EmergencyStopUpdate(byte pump, string reg, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPumpIntegratorInTransient<IMessageTracking>>((w) => w.EmergencyStopUpdate(pump, reg, message), message.FullId);
        }

        /// <inheritdoc cref="IPumpIntegratorInTransient{TMessageTracking}"/>
        public Result ReservePump(byte pump, uint limit, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPumpIntegratorInTransient<IMessageTracking>>((w) => w.ReservePump(pump, limit, message), message.FullId);
        }

        /// <inheritdoc cref="IPumpIntegratorInSetup"/>
        public void Enable()
        {
            DoActionOnWorkers<IPumpIntegratorInSetup>((w) => w.Enable(), null);
        }

        /// <inheritdoc cref="IPumpIntegratorInSetup"/>
        public Result MasterResetController(string loggingReference = null)
        {
            return DoActionOnWorkers<IPumpIntegratorInSetup>((w) => w.MasterResetController(loggingReference), loggingReference);
        }

        /// <inheritdoc cref="ITankGaugeIntegratorOutJournal"/>
        public void OnDips(IEnumerable<Dip> dips, string loggingReference = null)
        {
            DoActionOnWorkers<ITankGaugeIntegratorOutJournal>((w) => w.OnDips(dips, loggingReference), loggingReference);
        }

        /// <inheritdoc cref="IPumpIntegratorOutJournal{TMessageTracking}"/>
        public void OnMaxPumps(int maxPump, string loggingReference = null)
        {
            DoActionOnWorkers<IPumpIntegratorOutJournal<IMessageTracking>>((w) => w.OnMaxPumps(maxPump, loggingReference), loggingReference);

            GetWorker<IControllerWorker>()?.PushChange(EventType.AboutChanged);
        }

        /// <inheritdoc cref="IPumpIntegratorOutJournal{TMessageTracking}"/>
        public void OnMeters(IEnumerable<MeterReadings> meters, string loggingReference = null)
        {
            DoActionOnWorkers<IPumpIntegratorOutJournal<IMessageTracking>>((w) => w.OnMeters(meters, loggingReference), loggingReference);
        }

        /// <inheritdoc cref="IPumpIntegratorOutJournal{TMessageTracking}"/>
        public void OnPriceChange(PriceChange priceChange, IMessageTracking message = null)
        {
            DoActionOnWorkers<IPumpIntegratorOutJournal<IMessageTracking>>((w) => w.OnPriceChange(priceChange, message), message.FullId);

            GetWorker<IControllerWorker>()?.PushChange(EventType.FuelPriceChanged);
        }

        /// <inheritdoc cref="IPumpIntegratorInJournal"/>
        public bool ShouldSynchroniseGrades => GetWorker<IPumpIntegratorInJournal>()?.ShouldSynchroniseGrades ?? false;

        /// <inheritdoc cref="IPumpIntegratorOutTransient{TMessageTracking}"/>
        public void OnPumpState(PumpStateChange pumpState, IMessageTracking message = null)
        {
            DoActionOnWorkers<IPumpIntegratorOutTransient<IMessageTracking>>((w) => w.OnPumpState(pumpState, message), message.FullId);
        }

        /// <inheritdoc cref="IPumpIntegratorInSetup"/>
        public Result OpenPump(byte pump, string loggingReference = null)
        {
            return DoActionOnWorkers<IPumpIntegratorInSetup>((w) => w.OpenPump(pump, loggingReference), loggingReference);
        }

        /// <inheritdoc cref="IPumpIntegratorInTransient{TMessageTracking}"/>
        public Result PaymentApproved(byte pump, uint limit, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPumpIntegratorInTransient<IMessageTracking>>((w) => w.PaymentApproved(pump, limit, message), message.FullId);
        }

        /// <inheritdoc cref="IPumpIntegratorInTransient{TMessageTracking}"/>
        public Result PaymentCancelled(byte pump, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPumpIntegratorInTransient<IMessageTracking>>((w) => w.PaymentCancelled(pump, message), message.FullId);
        }

        /// <inheritdoc cref="IPumpIntegratorInTransient{TMessageTracking}"/>
        public Result PaymentCleared(byte pump, uint limit, bool isZeroPaid = false, IMessageTracking message = null, int transSeqNum = 0)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPumpIntegratorInTransient<IMessageTracking>>((w) => w.PaymentCleared(pump, limit, isZeroPaid, message, transSeqNum), message.FullId);
        }

        /// <inheritdoc cref="IPumpIntegratorInTransient{TMessageTracking}"/>
        public Result PaymentClearedOrCancelledAcknowledged(byte pump, int transSeqNum, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPumpIntegratorInTransient<IMessageTracking>>((w) => w.PaymentClearedOrCancelledAcknowledged(pump, transSeqNum, message), message.FullId);
        }

        /// <inheritdoc cref="IPumpIntegratorInSetup"/>
        public Result ReconnectController(string loggingReference = null)
        {
            return DoActionOnWorkers<IPumpIntegratorInSetup>((w) => w.ReconnectController(loggingReference), loggingReference);
        }

        /// <inheritdoc cref="ITankGaugeIntegratorInJournal"/>
        public Result RequestDips(string loggingReference = null)
        {
            return DoActionOnWorkers<ITankGaugeIntegratorInJournal>((w) =>
            {
                var result = IsConnectableConnected(w);
                return !result.IsSuccess ? result : w.RequestDips(loggingReference);
            }, loggingReference);
        }

        /// <inheritdoc cref="IPumpIntegratorInJournal"/>
        public Result RequestMeters(string loggingReference = null)
        {
            return DoActionOnWorkers<IPumpIntegratorInJournal>((w) =>
            {
                var result = IsConnectableConnected(w);
                return !result.IsSuccess ? result : w.RequestMeters(loggingReference);
            }, loggingReference);
        }

        /// <inheritdoc cref="IPumpIntegratorInJournal"/>
        public Result RequestPrices(string loggingReference = null)
        {
            return DoActionOnWorkers<IPumpIntegratorInJournal>((w) => w.RequestPrices(loggingReference), loggingReference);
        }

        /// <inheritdoc cref="IPumpIntegratorInTransient{TMessageTracking}"/>
        public Result RequestState(IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPumpIntegratorInTransient<IMessageTracking>>((w) => w.RequestState(message), message.FullId);
        }

        /// <inheritdoc cref="IPumpIntegratorInSetup"/>
        public Result ResetController(string loggingReference = null)
        {
            return DoActionOnWorkers<IPumpIntegratorInSetup>((w) => w.ResetController(loggingReference), loggingReference);
        }

        Result bosCore.IBosIntegratorInTransient<IMessageTracking, bos.IdInfo, Result>.RequestTransaction(bos.IdInfo transaction, IMessageTracking message)
        {
            message ??= new MessageTracking();
            var result = DoActionOnWorkers<IBosIntegratorInTransient<IMessageTracking, Result<StatusCodeResult>>, StatusCodeResult>((w) => w.RequestTransaction(transaction, message), message.FullId);
            return result;
        }

        /// <inheritdoc />
        public DateTime ShiftEndTime => GetWorker<IJournalWorker>()?.ShiftEndTime ?? DateTime.MinValue;

        /// <inheritdoc />
        public DateTime DayEndTime => GetWorker<IJournalWorker>()?.DayEndTime ?? DateTime.MinValue;

        Result<StatusCodeResult> bosCore.IBosIntegratorInJournal<IMessageTracking, Result<StatusCodeResult>>.RequestDayEnd(IMessageTracking message)
        {
            message ??= new MessageTracking();
            var result = DoActionOnWorkers<IBosIntegratorInJournal<IMessageTracking, Result<StatusCodeResult>>, StatusCodeResult>((w) => w.RequestDayEnd(message), message.FullId);
            return MapResult(result);
        }

        Result<StatusCodeResult> bosCore.IBosIntegratorInJournal<IMessageTracking, Result<StatusCodeResult>>.RequestShiftEnd(IMessageTracking message)
        {
            message ??= new MessageTracking();
            var result = DoActionOnWorkers<IBosIntegratorInJournal<IMessageTracking, Result<StatusCodeResult>>, StatusCodeResult>((w) => w.RequestShiftEnd(message), message.FullId);
            return MapResult(result);
        }

        /// <inheritdoc cref="IBosIntegratorInJournal{IMessageTracking}"/>
        public Result RequestDayEnd(IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IBosIntegratorInJournal<IMessageTracking>>((w) => w.RequestDayEnd(message), message.FullId);
        }

        /// <inheritdoc cref="IBosIntegratorInJournal{IMessageTracking}"/>
        public Result RequestShiftEnd(IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IBosIntegratorInJournal<IMessageTracking>>((w) => w.RequestShiftEnd(message), message.FullId);
        }

        /// <inheritdoc cref="IBosIntegratorInTransient{TMessageTracking, TResult}"/>
        public Result<StatusCodeResult> RequestTransaction(bos.IdInfo transaction, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IBosIntegratorInTransient<IMessageTracking, Result<StatusCodeResult>>, StatusCodeResult>((w) => w.RequestTransaction(transaction, message), message.FullId);
        }

        /// <inheritdoc cref="IBosIntegratorOutJournal{TMessageTracking}"/>
        public Result DayEndResponse(ShiftEndItem dayEndDetails, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CategorySalesItem> categorySales, IEnumerable<CardSalesItem> cardSales,
            IEnumerable<CardAmountSalesItem> cardAmountSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IBosIntegratorOutJournal<IMessageTracking>>((w) => w.DayEndResponse(dayEndDetails, itemSales, categorySales, cardSales, cardAmountSales, cardVolumeSales, message), message.FullId);
        }

        /// <inheritdoc cref="IBosIntegratorOutJournal{TMessageTracking}"/>
        public Result ShiftEndResponse(ShiftEndItem shiftEndDetails, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CardSalesItem> cardSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IBosIntegratorOutJournal<IMessageTracking>>((w) => w.ShiftEndResponse(shiftEndDetails, itemSales, cardSales, cardVolumeSales, message), message.FullId);
        }

        public Result RequestModeChange(byte pump, ModeChangeType mode, IMessageTracking message)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPosIntegratorInMode<IMessageTracking, Result>>((w) => w.RequestModeChange(pump, mode, message), message.FullId);
        }

        Result<StatusCodeResult> posCore.IPosIntegratorInMode<IMessageTracking, Result<StatusCodeResult>>.RequestModeChange(byte pump, ModeChangeType mode, IMessageTracking message)
        {
            message ??= new MessageTracking();
            var result = DoActionOnWorkers<IPosIntegratorInMode<IMessageTracking, Result<StatusCodeResult>>, StatusCodeResult>((w) => w.RequestModeChange(pump, mode, message), message.FullId);
            return MapResult(result);
        }

        public Result ApplyTimeMode(IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPosIntegratorInMode<IMessageTracking>>((w) => w.ApplyTimeMode(message), message.FullId);
        }

        public Result RequestDefaultMode(string loggingReference)
        {
            return DoActionOnWorkers<IPosIntegratorInMode<IMessageTracking>>((w) => w.RequestDefaultMode(loggingReference), loggingReference);
        }

        public Result RequestTimeModeChange(TimeModeChangeType mode, IMessageTracking message)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPosIntegratorInMode<IMessageTracking>>((w) => w.RequestTimeModeChange(mode, message), message.FullId);
        }

        public Result RequestReceipt(GetReceiptRequest request, IMessageTracking message)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>((w) => w.RequestReceipt(request, message), message.FullId);
        }

        public Result RequestStatus(byte pump, IMessageTracking message)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>((w) => DoActionOnWorkers(w, (w) => w.RequestStatus(pump, message)), message.FullId);
        }

        public Result RequestStatus(IPump pump, string loggingReference)
        {
            return DoActionOnWorkers<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>((w) => DoActionOnWorkers(w, (w) => w.RequestStatus(pump, loggingReference)), loggingReference);
        }

        Result<StatusCodeResult> posCore.IPosIntegratorInTransient<IMessageTracking, IPump, ReceiptInfo, StatusResponse, Result<StatusCodeResult>>.RequestReceipt(GetReceiptRequest request, IMessageTracking message)
        {
            message ??= new MessageTracking();
            var result = DoActionOnWorkers<IPosIntegratorInTransient<IMessageTracking, IPump, Result<StatusCodeResult>>, StatusCodeResult>((w) => w.RequestReceipt(request, message), message.FullId);
            return MapResult(result);
        }

        Result<StatusCodeResult> posCore.IPosIntegratorInTransient<IMessageTracking, IPump, ReceiptInfo, StatusResponse, Result<StatusCodeResult>>.RequestStatus(byte pump, IMessageTracking message)
        {
            message ??= new MessageTracking();
            var result = DoActionOnWorkers<IPosIntegratorInTransient<IMessageTracking, IPump, Result<StatusCodeResult>>, StatusCodeResult>((w) => w.RequestStatus(pump, message), message.FullId);
            return MapResult(result);
        }

        Result<StatusCodeResult> posCore.IPosIntegratorInTransient<IMessageTracking, IPump, ReceiptInfo, StatusResponse, Result<StatusCodeResult>>.RequestStatus(IPump pump, string loggingReference)
        {
            var result = DoActionOnWorkers<IPosIntegratorInTransient<IMessageTracking, IPump, Result<StatusCodeResult>>, StatusCodeResult>((w) => w.RequestStatus(pump, loggingReference), loggingReference);
            return result;
        }

        public Result ModeChangedResponse(byte pump, ModeChangeType mode, PumpOptType previousState, IMessageTracking message, bool suppressModeChange = false)
        {
            return DoActionOnWorkers<IPosIntegratorOutMode<IMessageTracking>>((w) => w.ModeChangedResponse(pump, mode, previousState, message, suppressModeChange), message.FullId);
        }

        public Result ReceiptResponse(ReceiptInfo receipt, IMessageTracking message)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPosIntegratorOutTransient<IMessageTracking>>((w) => w.ReceiptResponse(receipt, message), message.FullId);
        }

        public Result ReceiptResponse(byte pump, IMessageTracking message)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPosIntegratorOutTransient<IMessageTracking>>((w) => w.ReceiptResponse(pump, message), message.FullId);
        }

        public Result StatusResponse(StatusResponse status, IMessageTracking message)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPosIntegratorOutTransient<IMessageTracking>>((w) => w.StatusResponse(status, message), message.FullId);
        }

        public Result StatusResponse(byte pump, IMessageTracking message)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IPosIntegratorOutTransient<IMessageTracking>>((w) => w.StatusResponse(pump, message), message.FullId);
        }

        /// <inheritdoc cref="IBosIntegratorOutOfflineTransient"/>
         public Result MoveOfflineTransactionFiles(IPAddress ipAddress, string logginReference = null)
        {
           return DoActionOnWorkers<IBosIntegratorOutOfflineTransient>((w) => w.MoveOfflineTransactionFiles(ipAddress, logginReference), logginReference);
        }

        /// <inheritdoc cref="IBosIntegratorOutTransient{TMessageTracking}"/>
        public Result WriteTransactionFile(IEnumerable<TransactionItem> items, DateTime dateTime, IEnumerable<IPAddress> devices = null, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IBosIntegratorOutTransient<IMessageTracking>>((w) => w.WriteTransactionFile(items, dateTime, devices, message), message.FullId);
        }

        /// <inheritdoc cref="IBosIntegratorOutTransient{TMessageTracking}"/>
        public Result SendTransaction(SendTransactionItem sendTransaction, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IBosIntegratorOutTransient<IMessageTracking>>((w) => w.SendTransaction(sendTransaction, message), message.FullId);
        }

        /// <inheritdoc cref="IBosIntegratorOutTransient{TMessageTracking}"/>
        public Result WriteLocalAccountInvoiceFile(LocalAccountTransactionItem localAccountItem, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IBosIntegratorOutTransient<IMessageTracking>>((w) => w.WriteLocalAccountInvoiceFile(localAccountItem, message), message.FullId);
        }

        /// <inheritdoc cref="IBosIntegratorOutJournal{TMessageTracking}"/>
        public Result WriteShiftEndFiles(bool isDayEnd, DateTime startTime, DateTime endTime, ShiftEndItem endItem, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CategorySalesItem> categorySales, IEnumerable<CardSalesItem> cardSales, IEnumerable<CardAmountSalesItem> cardAmountSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<IBosIntegratorOutJournal<IMessageTracking>>((w) => w.WriteShiftEndFiles(isDayEnd, startTime, endTime, endItem, itemSales, categorySales, cardSales, cardAmountSales, cardVolumeSales, message), message.FullId);
        }

        /// <inheritdoc cref="IBosIntegratorOutTransient{TMessageTracking}"/>
        public Result<string> GetTransactionFileFolder()
        {
            return DoActionOnWorkers<IBosIntegratorOutTransient<IMessageTracking>, string>((w) => w.GetTransactionFileFolder(), LoggingReference);
        }

        Result ISecAuthIntegratorInTransient<IMessageTracking>.PreAuthReponse(SecAuthResponse response, IMessageTracking message)
        {
            return DoProcessSecAuthResponse(response, message, (w, r, m) => w.PreAuthReponse(r, m));
        }

        Result ISecAuthIntegratorInTransient<IMessageTracking>.PostAuthResponse(SecAuthResponse response, IMessageTracking message)
        {
            return DoProcessSecAuthResponse(response, message, (w, r, m) => w.PostAuthResponse(r, m));
        }

        Result ISecAuthIntegratorInTransient<IMessageTracking>.RequestTimedOut(SecAuthResponse response, IMessageTracking message)
        {
            return DoProcessSecAuthResponse(response, message, (w, r, m) => w.RequestTimedOut(r, m));
        }

        private Result DoProcessSecAuthResponse(SecAuthResponse response, IMessageTracking message, Func<ISecAuthIntegratorInTransient<IMessageTracking>, SecAuthResponse, IMessageTracking, Result> action)
        {
            message ??= new MessageTracking();
            var secAuthIn = GetWorker<ISecAuthInWorker>() as ISecAuthIntegratorInTransient<IMessageTracking>;
            var result = secAuthIn != null ? action(secAuthIn, response, message) : Result.Failure("Not Implemented");
            var resultMobile = Result.Success();
            if (result.IsSuccess)
            {
                secAuthIn = GetWorker<IHydraMobileWorker>();
                resultMobile = secAuthIn != null ? action(secAuthIn, response, message) : Result.Success();
            }

            return Result.Combine(result, resultMobile);
        }

        Result ISecAuthIntegratorOutTransient<IMessageTracking>.PreAuthRequest(SecAuthRequest request, IMessageTracking message)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<ISecAuthIntegratorOutTransient<IMessageTracking>>((w) => w.PreAuthRequest(request, message), message.FullId);
        }

        Result ISecAuthIntegratorOutTransient<IMessageTracking>.PostAuthRequest(SecAuthRequest request, IMessageTracking message)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<ISecAuthIntegratorOutTransient<IMessageTracking>>((w) => w.PostAuthRequest(request, message), message.FullId);
        }

        /// <inheritdoc cref="ISecAuthIntegratorOutTransient{TMessageTracking}"/>
        public Result RequestTimedOutRequest(SecAuthRequest request, IMessageTracking message)
        {
            message ??= new MessageTracking();
            return DoActionOnWorkers<ISecAuthIntegratorOutTransient<IMessageTracking>>((w) => w.RequestTimedOutRequest(request, message), message.FullId);
        }
    }
}
