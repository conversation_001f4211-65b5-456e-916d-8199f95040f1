﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net48</TargetFramework>
		<IsPackable>false</IsPackable>
		<Platforms>AnyCPU;x64</Platforms>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNet.WebApi.Core" Version="[5.3.0,)" />
		<PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="[9.0.7,)" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="[17.14.1,)" />
		<PackageReference Include="FluentAssertions.Web" Version="1.9.5" />
		<PackageReference Include="Htec.Foundation.Testing.Helpers" Version="5.0.0" />
		<PackageReference Include="Htec.Logger.Interfaces" Version="5.0.0" />
		<PackageReference Include="Htec.Testing.Helpers" Version="3.1.0" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.extensibility.core" Version="2.9.3" />
		<PackageReference Include="xunit.runner.console" Version="2.9.3">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="xunit.runner.visualstudio" Version="3.1.3">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Forecourt.Service\Forecourt.Service.csproj" />
	</ItemGroup>
</Project>
