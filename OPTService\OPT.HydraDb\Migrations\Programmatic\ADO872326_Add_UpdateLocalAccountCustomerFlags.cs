﻿using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.Collections.Generic;
using System.IO.Abstractions;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc cref="Migrationable"/>
    [Migration(********, "ADO#872326 - Add dbo.UpdateLocalAccountCustomerFlags stored procedure"), Tags(Constants.MigrationTypeSchema)]
    public class ADO872326_Add_UpdateLocalAccountCustomerFlags : MigrationableProgrammibility
    {
        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="logger">Migration logger instance</param>
        /// <param name="dbExecutorFactory">IDbExecutorFactory instance</param>
        /// <param name="fileSystem">IFileSystem instance</param>
        public ADO872326_Add_UpdateLocalAccountCustomerFlags(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory, IFileSystem fileSystem) :
            base(null, new List<string> { "dbo.UpdateLocalAccountCustomerFlags" }, logger, dbExecutorFactory, fileSystem)
        { }
    }
}
