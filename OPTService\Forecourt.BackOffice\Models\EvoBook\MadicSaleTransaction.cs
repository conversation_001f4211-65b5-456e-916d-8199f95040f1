﻿using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Htec.Hydra.Core.Bos.Configuration.EvoBook;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;

namespace Forecourt.BackOffice.Models.EvoBook
{
    /// <summary>
    /// MADIC sales transaction
    /// </summary>
    public class MadicSaleTransaction
    {
        /// <summary>
        /// Transaction details
        /// </summary>
        [JsonProperty("transactionDetails")]
        public TransactionDetails TransactionDetails { get; set; }

        /// <summary>
        /// Email override
        /// </summary>
        [JsonProperty("emailOverride")]
        public bool EmailOverride { get; set; }

        /// <summary>
        /// Transaction basket
        /// </summary>
        [JsonProperty("basket")]
        public Basket Basket { get; set; }

        /// <summary>
        /// Creates a new sale transaction from booking details and configuration.
        /// </summary>
        /// <param name="model">Transaction booking model</param>
        /// <param name="bosConfig">Back office configuration</param>
        /// <param name="cardReference">Hydra card reference</param>
        /// <param name="madicGrade">MADIC grade</param>
        /// <param name="madicTaxLevel">MADIC tax level</param>
        /// <param name="madicTender">MADIC tender</param>
        /// <returns>MADIC sales transaction instance</returns>
        /// <param name="vatCalculator">Vat calculator instance</param>
        /// <exception cref="ArgumentNullException"></exception>
        public static MadicSaleTransaction MapFromBookTransactionModel(BookTransactionModel model, BosConfig bosConfig, CardReference cardReference, Grade madicGrade, TaxLevel madicTaxLevel, Tender madicTender, IVatCalculator vatCalculator)
        {
            if (model == null) throw new ArgumentNullException(nameof(model));
            if (bosConfig == null) throw new ArgumentNullException(nameof(bosConfig));
            if (cardReference == null) throw new ArgumentNullException(nameof(cardReference));
            if (madicGrade == null) throw new ArgumentNullException(nameof(madicGrade));
            if (madicTaxLevel == null) throw new ArgumentNullException(nameof(madicTaxLevel));
            if (madicTender == null) throw new ArgumentNullException(nameof(madicTender));

            bool bunkeredCard = model.Item.IsBunkeredCard;
            bool fuelCard = cardReference.FuelCard;

            double saleAmount = (double)model.Item.AuthAmount / 100;
            double saleTaxAmount = (double)model.Item.VatAmount / 100;

            // VatAmount is not currently being passed so calculate if needed.
            if (model.Item.VatAmount <= 0)
            {
                (saleTaxAmount, _) = vatCalculator.CalculateVat(saleAmount, madicTaxLevel.TaxRate);
            }

            var transactionKey = new TransactionKey
            {
                CompanyId = bosConfig.CompanyId,
                StoreId = bosConfig.StoreId,
                WorkstationId = model.Item.TillNumber,
                BusinessDay = model.BusinessDate,
                TransactionSequenceNumber = model.ExternalTransactionId
            };

            var transactionDetails = new TransactionDetails
            {
                Key = transactionKey,
                OperatorId = bosConfig.OperatorId.ToString(),
                StartTime = model.Item.TransactionStart.ToString("yyyy-MM-ddTHH:mm:ssZ"), 
                EndTime = model.Item.TransactionFinish.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                PeriodNumber = model.PeriodId,
                CancelledFlag = false,
                SuspendedFlag = false,
                TrainingFlag = false,
                ShiftId = model.ShiftId,
                ReceiptPrintCount = 0,
                ScoMode = false
            };

            var fuelSale = new FuelSale
            {
                FuellingTransactionId = model.Item.TransactionSequenceNumber == 0 ? 5000 : model.Item.TransactionSequenceNumber,
                FuellingTimeStamp = model.Item.TransactionFinish.ToString("yyyy-MM-ddTHH:mm:ssZ"), // TODO - Need DOMS FinishDate, FinishTime ADO#657918
                FuelGradeId = madicGrade.Id,
                FuelItemId = madicGrade.ItemId,
                FuelingPointId = model.Item.Pump,
                NozzleId = model.Item.Hose,
                FuelSalePrice = (double)model.Item.Price / 1000,
                FuelEntryMethod = "PO",
                FuelSalesVolume = (double)model.Item.Volume / 1000,
                FuelSalesValue = saleAmount,
                FuelGradeDescription = madicGrade.Description,
            };

            JObject gradeItem = madicGrade.CloneProductItem();
            gradeItem["price"] = fuelSale.FuelSalePrice;

            var saleLine = new SaleLine
            {
                LineItemSequenceNumber = 1,
                VoidedFlag = false,
                StartTime = null,
                EndTime = null,
                FiscalReceipt = !(fuelCard || bunkeredCard),
                SaleItem = gradeItem,
                SaleQuantity = fuelSale.FuelSalesVolume,
                PromotionEligible = 0,
                PromotionApplied = 0,
                SaleAmount = saleAmount,
                SaleTaxAmount = saleTaxAmount,
                OriginalSaleAmount = saleAmount,
                ItemReturn = false,
                Overrides = new List<string>(),
                DiscountModifiers = new List<string>(),
                PromotionModifiers = new List<string>(),
                PriceChangeModifiers = new List<string>(),
                FuelSale = fuelSale
            };

            var cardDetails = new CardDetail
            {
                AcquirerId = "",
                CardPan = model.Item.CardNumber,
                ApprovalCode = model.Item.AuthCode ?? string.Empty,
                CardType = cardReference.CardRef.ToString(),
                ReferenceNumber = model.Item.Reference ?? string.Empty,
                TerminalId = model.Item.TerminalId ?? string.Empty,
                ApprovalTime = model.Item.TimeStamp.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                Stan = model.Item.Number,
                MiscellaneousData = null,
                ReceiptLines = model.ReceiptLines,
                JournalLines = new List<string>(),
                MerchantId = model.Item.MerchantId,
                TerminalBatch = "00",
                VolumetricCard = bunkeredCard,
                UseDeposit = false,
                Registration = model.Item.Registration,
                ReallocationFlag = false,
                CustomerId = null
            };

            var tenderLine = new TenderLine
            {
                LineItemSequenceNumber = 2,
                VoidedFlag = false,
                StartTime = null,
                EndTime = null,
                FiscalReceipt = !(fuelCard || bunkeredCard),
                MethodOfPayment = madicTender.Item,
                TenderAmount = saleAmount,
                ForeignCurrencyAmount = 0,
                CardDetails = cardDetails,
                ChangeLine = false,
                VoucherBarcode = null,
                Donation = false,
                DonationBaseTenderId = null
            };

            var taxLine = new TaxLine
            {
                LineItemSequenceNumber = 3,
                VoidedFlag = false,
                StartTime = null,
                EndTime = null,
                FiscalReceipt = !(fuelCard || bunkeredCard),
                TaxAuthorityId = madicTaxLevel.TaxAuthorityId,
                TaxGroupId = madicTaxLevel.TaxLevelId,
                TaxGroupName = madicTaxLevel.TaxDescription,
                TaxAmount = saleTaxAmount,
                TaxableAmount = saleAmount - saleTaxAmount,
                TaxPercent = madicTaxLevel.TaxRate,
                TaxReclaimable = true
            };

            return new MadicSaleTransaction()
            {
                TransactionDetails = transactionDetails,
                EmailOverride = false,
                Basket = new Basket
                {
                    BunkeringSale = bunkeredCard,
                    TotalSaleAmount = saleAmount,
                    SaleLines = new List<SaleLine> { saleLine },
                    TaxLines = new List<TaxLine> { taxLine },
                    TenderLines = new List<TenderLine> { tenderLine },
                    DiscountVouchers = new List<string>(),
                    Employee = null,
                    LoyaltyId = null,
                    LoyaltyTextLines = new List<string>()
                }
            };
        }
    }
}
