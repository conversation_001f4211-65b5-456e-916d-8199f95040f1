using FluentAssertions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using System;
using System.Collections.Specialized;
using Xunit;

namespace Forecourt.Pos.Tests.Workers
{
    public class HydraPosInModeWorkerTests
    {
        private readonly IHtecLogManager _logManager;
        private readonly IHydraDb _hydraDb;
        private readonly IPumpCollection _allPumps;
        private readonly IPosIntegratorOutTransient<IMessageTracking> _posOutWorker;

        private readonly NameValueCollection _appSettings = new();
        private readonly IConfigurationManager _configurationManager;

        private readonly IMessageTracking _messageTracking;

        public HydraPosInModeWorkerTests()
        {
            _logManager = Substitute.For<IHtecLogManager>();
            _hydraDb = Substitute.For<IHydraDb>();
            _allPumps = Substitute.For<IPumpCollection>();
            _posOutWorker = Substitute.For<IPosIntegratorOutTransient<IMessageTracking>>();
            
            _configurationManager = Substitute.For<IConfigurationManager>();
            _configurationManager.AppSettings.Returns(_appSettings);

            Substitute.For<IHtecLogger>();
            _messageTracking = Substitute.For<IMessageTracking>();
        }

        private HydraPosInModeWorker CreateWorker(string now)
        {
            var pump = Substitute.For<IPump>();
            pump.Number.Returns((byte)1);
            pump.ClosePending.Returns(false);
            _allPumps.AllPumps.Returns(new[] { pump });

            var workerSpy = Substitute.ForPartsOf<HydraPosInModeWorker>(
                _logManager,
                _hydraDb,
                _allPumps,
                _posOutWorker,
                _configurationManager);

            var testTime = DateTime.Parse(now);
            workerSpy.GetNow = () => testTime;
            
            return workerSpy;
        }

        [Theory]
        // Evening mode - within range
        [InlineData("2025-08-14 16:00:00", "15:00:00", "17:00:00", "00:00:00", "00:00:00", TimeModeChangeType.EveningMode)]
        // Evening mode - start boundary
        [InlineData("2025-08-14 18:00:00", "18:00:00", "21:00:00", "00:00:00", "00:00:00", TimeModeChangeType.EveningMode)]
        // Evening mode - end boundary
        [InlineData("2025-08-14 20:59:59", "18:00:00", "21:00:00", "21:00:00", "23:45:59", TimeModeChangeType.EveningMode)]
        // Evening mode - overlap evening with night range, where evening has priority!
        [InlineData("2025-08-14 21:30:00", "18:00:00", "22:00:00", "21:00:00", "06:00:00", TimeModeChangeType.EveningMode)]

        // Night mode - start boundary
        [InlineData("2025-08-14 21:00:00", "18:00:00", "18:30:00", "21:00:00", "23:45:59", TimeModeChangeType.NightMode)]
        // Night mode - end boundary
        [InlineData("2025-08-14 23:45:58", "18:00:00", "18:30:00", "21:00:00", "23:45:59", TimeModeChangeType.NightMode)]
        // Night mode - falling within same date range (not crossing midnight)
        [InlineData("2025-08-14 23:00:00", "18:00:00", "18:30:00", "21:00:00", "00:00:00", TimeModeChangeType.NightMode)]
        // Night mode - end boundary crossing midnight (next day)
        [InlineData("2025-08-15 06:59:59", "18:00:00", "18:30:00", "21:00:00", "07:00:00", TimeModeChangeType.NightMode)]
        // Night mode - end day in early morning during night mode (next day)
        [InlineData("2025-08-15 03:00:00", "18:00:00", "21:00:00", "22:00:00", "06:00:00", TimeModeChangeType.NightMode)]

        // DayMode - no time mode change request should be made
        [InlineData("2025-08-14 12:00:00", "18:00:00", "21:00:00", "21:00:00", "06:00:00", TimeModeChangeType.DayMode)]
        // DayMode - just before evening start i.e. no time mode change request made
        [InlineData("2025-08-14 17:59:59", "18:00:00", "21:00:00", "21:00:00", "06:00:00", TimeModeChangeType.DayMode)]
        // DayMode - edge case just after evening ends, however before night range starts (i.e. there is gap b/w two range) so no time mode change request made
        [InlineData("2025-08-14 21:30:00", "18:00:00", "21:00:00", "22:00:00", "06:00:00", TimeModeChangeType.DayMode)]
        public void apply_time_mode_change_scenarios_and_verify_expected_mode_change(string now, 
            string eveningStart, string eveningEnd, string nightStart, string nightEnd, 
            TimeModeChangeType expectedMode)
        {
            // Arrange
            _appSettings[PosInModeWorker.ConfigKeyEveningModeStartTime] = eveningStart;
            _appSettings[PosInModeWorker.ConfigKeyEveningModeEndTime] = eveningEnd;
            _appSettings[PosInModeWorker.ConfigKeyNightModeStartTime] = nightStart;
            _appSettings[PosInModeWorker.ConfigKeyNightModeEndTime] = nightEnd;

            var workerSpy = CreateWorker(now);

            // Act
            var result = workerSpy.ApplyTimeMode(_messageTracking);

            // Assert
            result.IsSuccess.Should().BeTrue();

            switch (expectedMode)
            {
                case TimeModeChangeType.EveningMode:
                    workerSpy.Received(1).RequestTimeModeChange(TimeModeChangeType.EveningMode, _messageTracking);
                    workerSpy.DidNotReceive().RequestTimeModeChange(TimeModeChangeType.NightMode, _messageTracking);
                    break;

                case TimeModeChangeType.NightMode:
                    workerSpy.Received(1).RequestTimeModeChange(TimeModeChangeType.NightMode, _messageTracking);
                    workerSpy.DidNotReceive().RequestTimeModeChange(TimeModeChangeType.EveningMode, _messageTracking);
                    break;

                case TimeModeChangeType.DayMode:
                    workerSpy.DidNotReceive().RequestTimeModeChange(Arg.Any<TimeModeChangeType>(), _messageTracking);
                    break;
            }
        }
    }
}