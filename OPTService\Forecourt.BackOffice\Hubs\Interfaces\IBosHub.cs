﻿using Htec.Foundation.Web.Hubs.Interfaces;
using Htec.Hydra.Core.Bos.Messages.SignalR;
using System;

namespace Forecourt.Bos.Hubs.Interfaces
{
    /// <inheritdoc/>
    public interface IBosHub: IHub
    {
    }

    /// <inheritdoc/>
    public interface IBosHubIn : IHubIn
    {
        /// <summary>
        /// Sent when the transaction has been successfully booked into the external system
        /// </summary>
        /// <param name="request">Request model</param>
        void TransactionBooked(CompleteBooking request);

        /// <summary>
        /// Sent when the transaction has been successfully booked into the external system
        /// </summary>
        /// <param name="request">Request model</param>
        /// <param name="guid">Additional rety GUID</param>
        void TransactionBooked(CompleteBooking request, Guid guid);
    }
}
