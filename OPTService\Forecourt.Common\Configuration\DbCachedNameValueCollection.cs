﻿using Forecourt.Core.Helpers.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Configuration;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using ConfigurationConstants = Forecourt.Core.Configuration.Constants;

namespace OPT.Common.Configuration
{
    public class DbCachedNameValueCollection: NameValueCollection
    {
        private readonly IHtecLogger _logger;
        private readonly IConfigurationRepository _repository;
        private readonly ICacheHelper _cacheHelper;

        public DbCachedNameValueCollection(IHtecLogger logger, IConfigurationRepository repository, ICacheHelper cacheHelper)
        {
            _logger = logger;
            _repository = repository;
            _cacheHelper = cacheHelper;

            RefreshSection(null);
        }

        public override void Clear()
        {
            base.Clear();

            RefreshSection(null);
        }

        public void RefreshSection(string sectionName)
        {
            var categories = GetCategoriesConfiguration().Where(x => sectionName.IsNullOrWhiteSpace() || x.Category.Equals(sectionName, StringComparison.InvariantCultureIgnoreCase)).ToList();

            foreach (var category in categories)
            {
                foreach (var data in category.Settings)
                {
                    Add($"{category.Category.ToUpper()}::{data.Key}", data.Value.Value);
                }
            }
        }

        private IEnumerable<CategoryConfiguration> GetCategoriesConfiguration()
        {
            return _cacheHelper.GetCachedItem($"{ConfigurationConstants.CachedItemTypeConfiguration}", ConfigurationConstants.CachedItemCategories, () => _repository.GetCategoriesConfiguration());
        }
    }
}
