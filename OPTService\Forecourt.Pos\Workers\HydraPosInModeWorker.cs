﻿using CSharpFunctionalExtensions;
using Forecourt.Core.Extensions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Logger.Interfaces;
using System;

namespace Forecourt.Pos.Workers
{
    /// <summary>
    /// Custom PosInModeWorker, specific to HydraPos
    /// </summary>
    public class HydraPosInModeWorker : PosInModeWorker, IHydraPosInModeWorker
    {
        /// <summary>
        /// Function to get current date and times
        /// </summary>
        public Func<DateTime> GetNow { get; set; }
        
        /// <inheritdoc/>
        public HydraPosInModeWorker(IHtecLogManager logManager, IHydraDb hydraDb, IPumpCollection allPumps, IPosIntegratorOutTransient<IMessageTracking> posOutWorker, IConfigurationManager configurationManager = null) : base(logManager, hydraDb, allPumps, posOutWorker, configurationManager)
        {
            GetNow = () => DateTime.Now;
        }

        /// <inheritdoc/>
        protected override void DoRequestDefaultMode(IPump pump, string loggingReference)
        {
            pump.SetModeFromDefault(loggingReference, false);

            base.DoRequestDefaultMode(pump, loggingReference);
        }

        /// <inheritdoc/>
        protected override bool DoRequestModeChange(IPump pump, ModeChangeType mode, string reference, bool overridePumpIsClosed)
        {
            pump.OptIsClosed = mode == ModeChangeType.Close;

            var result = base.DoRequestModeChange(pump, mode, reference, true);

            if (result && !pump.OptIsClosed && (pump.PumpIsClosed || pump.ClosePending))
            {
                pump.OpenPump(reference: reference);
            }

            return result;
        }

        /// <inheritdoc/>
        protected override bool DoRequestTimeModeChange(IPump pump, TimeModeChangeType mode, IMessageTracking message)
        {
            pump.OptIsClosed = false;

            return base.DoRequestTimeModeChange(pump, mode, message);
        }

        /// <inheritdoc/> 
        protected override Result DoApplyTimeMode(IMessageTracking message)
        {
            var eveningStartTime = ConfigValueEveningModeStartTime.GetValue();
            var eveningEndTime = ConfigValueEveningModeEndTime.GetValue();
            var nightStartTime = ConfigValueNightModeStartTime.GetValue();
            var nightEndTime = ConfigValueNightModeEndTime.GetValue();

            var currentDateTime = GetNow();
            var isEveningMode = currentDateTime.IsWithinTimeRange(eveningStartTime, eveningEndTime);
            var isNightMode = currentDateTime.IsWithinTimeRange(nightStartTime, nightEndTime);

            var mode = isEveningMode ?
                TimeModeChangeType.EveningMode :
                isNightMode ? TimeModeChangeType.NightMode : TimeModeChangeType.DayMode;

            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "TimeMode.Status", () => new[] {
                $"Current local time: {currentDateTime}; isEveningMode: {isEveningMode}; isNightMode: {isNightMode}; isDayMode: {!isEveningMode && !isNightMode}; modeType: {mode}"
            });

            if (isEveningMode || isNightMode)
            {
                return RequestTimeModeChange(mode, message);
            }

            return base.DoApplyTimeMode(message);
        }
    }
}
