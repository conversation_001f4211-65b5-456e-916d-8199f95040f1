﻿using Newtonsoft.Json;

namespace Forecourt.BackOffice.Models.EvoBook
{
    /// <summary>
    /// Contains sequence number and business context information for MADIC transaction processing
    /// </summary>
    public class MadicSequenceNumber
    {
        /// <summary>
        /// Unique identifier for the company
        /// </summary>
        [JsonProperty("companyId")]
        public int CompanyId { get; set; }
        /// <summary>
        /// Unique identifier for the store location
        /// </summary>
        [JsonProperty("storeId")]
        public int StoreId { get; set; }
        /// <summary>
        /// Unique identifier for the workstation or terminal
        /// </summary>
        [JsonProperty("workstationId")]
        public int WorkstationId { get; set; }
        /// <summary>
        /// Next sequential transaction number to be assigned
        /// </summary>
        [JsonProperty("nextSequenceNumber")]
        public int NextSequenceNumber { get; set; }
        /// <summary>
        /// Identifier for the current business shift
        /// </summary>
        [JsonProperty("shiftId")]
        public int ShiftId { get; set; }
        /// <summary>
        /// Identifier for the current business period
        /// </summary>
        [JsonProperty("periodId")]
        public int PeriodId { get; set; }
        /// <summary>
        /// Current business day date for transaction processing
        /// </summary>
        [JsonProperty("currentBusinessDay")]
        public string CurrentBusinessDay { get; set; }
    }
}
