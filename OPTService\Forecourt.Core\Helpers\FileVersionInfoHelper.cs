﻿using CSharpFunctionalExtensions;
using Force.Crc32;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.Helpers.Interfaces;
using System;
using System.IO.Abstractions;
using System.Reflection;
using IFileVersionInfoFactory = Htec.Common.Abstractions.System.Diagnostics.Interfaces.IFileVersionInfoFactory;

namespace Forecourt.Core.Helpers
{
    /// <summary>
    /// Simple helper class that provides filename version information
    /// NB. This is an almost duplicate copy of the class (<see cref="OPT.Common.Helpers.FileVersionInfoHelper"/>) that deals with <see cref="System.IO.Abstractions.IFileInfoFactory"/> deprecating (and removing) FromFileName(string filename) method.
    /// </summary>
    public class FileVersionInfoHelper : Loggable, IFileVersionInfoHelper
    {
        private readonly IFileSystem _fileSystem;
        private readonly IFileVersionInfoFactory _fileInfoVersionFactory;

        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="logger"><see cref="IHtecLogger"/> instance</param>
        /// <param name="fileSystem"><see cref="IFileSystem"/> instance</param>
        /// <param name="fileVersionInfoFactory"><see cref="IFileVersionInfoFactory"/> instance</param>
        /// <exception cref="ArgumentNullException">Thrown if mandatory parameters are null/default</exception>
        public FileVersionInfoHelper(IHtecLogger logger, IFileSystem fileSystem, IFileVersionInfoFactory fileVersionInfoFactory) : base(logger)
        {
            _fileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));
            _fileInfoVersionFactory = fileVersionInfoFactory ?? throw new ArgumentNullException(nameof(fileVersionInfoFactory));
        }

        /// <inheritdoc cref="IFileVersionInfoHelper"/>
        public Result<FileVersionInfo> ExtractFileVersionInfo(Assembly assembly)
        {
            return (assembly == null) ? Result.Failure<FileVersionInfo>("Null Assembly") : ExtractFileVersionInfo(assembly.Location);
        }

        /// <inheritdoc cref="IFileVersionInfoHelper"/>
        public Result<FileVersionInfo> ExtractFileVersionInfo(string targetFolder, string targetFile)
        {
            return ExtractFileVersionInfo(_fileSystem.Path.Combine(_fileSystem.Path.GetDirectoryName(targetFolder), targetFile));
        }

        /// <inheritdoc cref="IFileVersionInfoHelper"/>
        public Result<FileVersionInfo> ExtractFileVersionInfo(string fileName)
        {
            return DoAction(() =>
            {
                try
                {
                    if (!_fileSystem.File.Exists(fileName))
                    {
                        var msg = $"File.NotFound: {fileName}";
                        DoDeferredLogging(LogLevel.Info, HeaderException, () => new[] { msg });
                        return Result.Failure<FileVersionInfo>(msg);
                    }

                    var info = _fileInfoVersionFactory.GetFileVersionInfoWrapper(fileName);
                    var file = _fileSystem.FileInfo.New(fileName);
                    var crc = ExtactCheckSum(fileName);

                    return Result.Success(new FileVersionInfo(file.Name, info.FileVersion, crc.IsSuccess ? crc.Value : "n/a", file?.LastWriteTime ?? DateTime.MinValue));
                }
                catch (Exception ex)
                {
                    DoDeferredLogging(LogLevel.Debug, HeaderException, () => new[] { $"File: {fileName}" }, ex);
                    DoDeferredLogging(LogLevel.Info, HeaderException, () => new[] { $"File: {fileName}; Exception: {ex.Message}" });
                    return Result.Failure<FileVersionInfo>(ex.Message);
                }
            }, null);
        }

        /// <inheritdoc cref="IFileVersionInfoHelper"/>
        public Result<string> ExtactCheckSum(string fileName)
        {
            return DoAction(() =>
            {
                if (!_fileSystem.File.Exists(fileName))
                {
                    var msg = $"File.NotFound: {fileName}";
                    DoDeferredLogging(LogLevel.Info, HeaderException, () => new[] { msg });
                    return Result.Failure<string>(msg);
                }

                try
                {
                    byte[] bytes = _fileSystem.File.ReadAllBytes(fileName);
                    var crc = Crc32Algorithm.Compute(bytes);
                    return Result.Success($"{crc:X8}");
                }
                catch (Exception ex)
                {
                    DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { $"File: {fileName}" }, ex);
                    return Result.Failure<string>(ex.Message);
                }
            }, null);
        }
    }
}
