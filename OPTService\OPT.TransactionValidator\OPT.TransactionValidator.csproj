﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net48</TargetFramework>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<Platforms>AnyCPU;x64</Platforms>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Foundation\**" />
		<EmbeddedResource Remove="Foundation\**" />
		<None Remove="Foundation\**" />
	</ItemGroup>

	<ItemGroup>
		<Compile Remove="Extensions\NameValueCollectionExtensions.cs" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="CSharpFunctionalExtensions" Version="[3.6.0,)" />
		<PackageReference Include="Htec.Foundation.Connections" Version="[6.0.0,)" />
		<PackageReference Include="Htec.Logger.log4Net" Version="[8.0.0,)" />
	</ItemGroup>


	<ItemGroup>
		<ProjectReference Include="..\Forecourt.Core\Forecourt.Core.csproj" />
	</ItemGroup>
</Project>

