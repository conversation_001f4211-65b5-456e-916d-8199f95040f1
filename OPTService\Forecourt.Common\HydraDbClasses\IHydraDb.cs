﻿using CSharpFunctionalExtensions;
using Forecourt.Core.Enums;
using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.HydraDb.Models;
using Htec.Foundation.Configuration;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Models;
using OPT.Common.HydraDbClasses;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Net;
using connGenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;
using PumpDelivered = OPT.Common.HydraDbClasses.PumpDelivered;

namespace Forecourt.Common.HydraDbClasses
{
    /// <summary>
    /// Any and all capabilities of the IHydraDb, split into seperate parts
    /// </summary>
    public interface IHydraDb :
        Bos.HydraDb.Interfaces.IHydraDb,
        Core.HydraDb.Interfaces.IHydraDb,
        Pump.HydraDb.Interfaces.IHydraDb,
        Pos.HydraDb.Interfaces.IHydraDb,
        SecondaryAuth.HydraDb.Interfaces.IHydraDb,
        PaymentConfiguration.HydraDb.Interfaces.IHydraDb,
        IHydraDbConfig,
        IHydraDbEndpoints,
        IHydraDbLoyalty, 
        IHydraDbOpt, 
        IHydraDbCarwash, 
        IHydraDbJournal, 
        IHydraDbTransaction, 
        IHydraDbDiscounts, 
        IHydraDbLocalAccounts
    {
    }

    /// <summary>
    /// Any and all capabilities of the IHydraDb, relevant to Configuration
    /// </summary>
    public interface IHydraDbConfig
    {
        /// <summary>
        /// Sets all categories in the list
        /// </summary>
        /// <param name="categories">List of categories to set</param>
        /// <param name="preActions">List of pre/validation actions</param>
        /// <param name="postActions">List of post actions</param>
        /// <returns>Result wrapped error text</returns>
        Result SetCategories(IEnumerable<CategoryConfiguration> categories, IEnumerable<ConfigKeyValueValidator> preActions = null, IEnumerable<Action<IEnumerable<CategoryConfiguration>>> postActions = null);

        /// <summary>
        /// Retrieves all database categories
        /// </summary>
        /// <returns>All database categories</returns>
        IList<CategoryConfiguration> GetCategoriesConfiguration();

        ///// <summary>
        ///// Get Integration type
        ///// </summary>
        ///// ///<param name="integrationType">The <see cref="IntegrationType"/> type</param>
        ///// <param name="loggingReference">Logging reference</param>
        //Result<string> GetIntegrationType(IntegrationType integrationType, string loggingReference = null);

        /// <summary>
        /// Set Integration type
        /// </summary>
        /// ///<param name="integrationType">The <see cref="IntegrationType"/> type</param>
        /// <param name="value">The integration type value</param>
        /// <param name="loggingReference">Logging reference</param>
        Result SetIntegrationType(IntegrationType integrationType, string value, string loggingReference = null);

        /// <summary>Fetch the set of Predefined Amounts from the database.</summary>
        /// <returns>The Predefined Amounts.</returns>
        IList<int> FetchPredefinedAmounts();

        /// <summary>Store Predefined Amounts in the database.</summary>
        /// <param name="amounts">The loyalty to store</param>>
        void SetPredefinedAmounts(IList<int> amounts);

        /// <summary>Store contactless state for the given OPT in the database.</summary>
        /// <param name="isEnabled">True if enabled, false if disabled.</param>
        void SetContactless(bool isEnabled);

        void SetContactlessCardPreAuth(int limit);
        void SetContactlessDevicePreAuth(int limit);
        void SetContactlessTtq(string ttq);

        void SetContactlessSingleButton(bool isEnabled);

        /// <summary>Store Unmanned Pseuod POS state for Hydra OPT Service in the database.</summary>
        /// <param name="hydraId">Hydra ID to set state for.</param>
        /// <param name="unmanned">True if enabled, false if disabled.</param>
        void SetUnmannedPseudoPos(string hydraId, bool unmanned);

        /// <summary>Store Asda Day End Report state for Hydra OPT Service in the database.</summary>
        /// <param name="hydraId">Hydra ID to set state for.</param>
        /// <param name="isAsda">True if enabled, false if disabled.</param>
        void SetAsdaDayEndReport(string hydraId, bool isAsda);

        /// <summary>Store IP address for Hydra OPT Service in the database.</summary>
        /// <param name="hydraId">Hydra ID to set IP address for.</param>
        /// <param name="ip">IP address to set.</param>
        void SetServiceAddress(string hydraId, IPAddress ip);

        /// <summary>Store port numbers for Hydra OPT Service in the database.</summary>
        /// <param name="hydraId">Hydra ID to set port numbers for.</param>
        /// <param name="fromOptPort">From OPT port number.</param>
        /// <param name="toOptPort">To OPT port number.</param>
        /// <param name="heartbeatPort">Heartbeat port number.</param>
        /// <param name="hydraPosPort">Hydra POS port number.</param>
        /// <param name="retalixPosPort">Retalix POS port number.</param>
        /// <param name="thirdPartyPosPort">Third Party POS port number.</param>
        /// <param name="mediaChannelPort">Media Channel port number.</param>
        void SetServicePorts
        (string hydraId, int fromOptPort, int toOptPort, int heartbeatPort, int hydraPosPort, int retalixPosPort, int thirdPartyPosPort,
            int mediaChannelPort);

        /// <summary>Store the receipt timeout in the database.</summary>
        /// <param name="timeout">The timeout is seconds.</param>
        void SetReceiptTimeout(int timeout);

        /// <summary>Store the receipt maximum count per OPT in the database.</summary>
        /// <param name="maxCount">The maximum count.</param>
        void SetReceiptMaxCount(int maxCount);

        /// <summary>Fetch the names and VAT rates for all grades from the database.</summary>
        /// <returns>List of grades, names and VAT rates.</returns>
        IList<GradeName> FetchGradeNames();

        /// <summary>
        /// Store the name and VAT rate for a given from the database.
        /// </summary>
        /// <param name="grade">Grade number.</param>
        /// <param name="name">Grade name.</param>
        /// <param name="vatRate">VAT rate.</param>
        Result<int> SetGradeName(byte grade, string name, float vatRate);

        void SetRetalixPosPrimaryIpAddress(IPAddress address);
        void SetWhitelistDirectory(string directory);
        void SetLayoutDirectory(string directory);
        void SetSoftwareDirectory(string directory);
        void SetMediaDirectory(string directory);
        void SetPlaylistDirectory(string directory);
        void SetOptLogFileDirectory(string directory);
        void SetLogFileDirectory(string directory);
        void SetTraceFileDirectory(string directory);
        void SetJournalFileDirectory(string directory);
        void SetReceivedUpdateDirectory(string directory);
        void SetDatabaseBackupDirectory(string directory);
        void SetContactlessPropertiesFile(string fileName);
        void SetFuelDataUpdateFile(string fileName);
        void SetUpgradeFileDirectory(string directory);
        void SetRollbackFileDirectory(string directory);

        void SetReceiptLayoutMode(int mode);
        void SetSiteName(string siteName);
        void SetVatNumber(string vatNumber);
        void SetCurrencyCode(int currencyCode);
        void SetNozzleUpForKioskUse(bool flag);
        void SetUseReplaceNozzleScreen(bool flag);
        void SetForwardFuelPriceUpdate(bool flag);
        void SetPlaylistFileName(string opt, string playlistFileName);
        PruneDays GetPruneDays();
        void SetFilePruneDays(int days);
        void SetTransactionPruneDays(int days);
        void SetReceiptPruneDays(int days);
        DateTime? GetNextDayEnd();
        void SetNextDayEnd(DateTime? dayEnd);
        int GetLogInterval();
        void SetLogInterval(int interval);

        void RunQuery(string query);
        void RunScript(string fileName);
    }

    /// <summary>
    /// Any and all capabilities of the IHydraDb, relevant to EndPoints
    /// </summary>
    public interface IHydraDbEndpoints
    {
        /// <summary>Fetch the Car Wash end point from the database.</summary>
        /// <returns>The Car Wash end point.</returns>
        connGenericEndPoint FetchCarWashEndPoint();      

        /// <summary>Store End Point for Car Wash in the database.</summary>
        /// <param name="ip">IP address to set.</param>
        /// <param name="port">Port number to set.</param>
        void SetCarWashEndPoint(IPAddress ip, int port);
    }

    /// <summary>
    /// Any and all capabilities of the IHydraDb, relevant to Loyalty
    /// </summary>
    public interface IHydraDbLoyalty
    {
        /// <summary>Fetch Loyalty details from the database.</summary>
        /// <param name="name">Name of loyalty details to fetch.</param>
        /// <returns>The loyalty details.</returns>
        GenericLoyalty FetchGenericLoyalty(string name);

        /// <summary>Add Loyalty to the database.</summary>
        /// <param name="name">Name of loyalty details to add.</param>>
        void AddGenericLoyalty(string name);

        /// <summary>Delete Loyalty from the database.</summary>
        /// <param name="name">Name of loyalty details to delete.</param>>
        void DeleteGenericLoyalty(string name);

        /// <summary>Check whether Loyalty is available.</summary>
        bool IsGenericLoyaltyAvailable(string name);

        /// <summary>Check whether Loyalty is present.</summary>
        bool IsGenericLoyaltyPresent(string name);

        /// <summary>Set Loyalty to be present in the database.</summary>
        /// <param name="name">Name of loyalty details to set.</param>>
        /// <param name="present">Loyalty is present if true, not if false.</param>>
        void SetGenericLoyaltyPresent(string name, bool present);

        /// <summary>Store Loyalty details in the database.</summary>
        /// <param name="name">Name of loyalty details to store</param>>
        /// <param name="loyalty">The loyalty to store</param>>
        void SetGenericLoyalty(string name, GenericLoyalty loyalty);
    }

    /// <summary>
    /// Any and all capabilities of the IHydraDb, relevant to OPT
    /// </summary>

    public interface IHydraDbOpt
    {
        /// <summary>Fetch the OPT Mode for the given OPT from the database.</summary>
        /// <param name="optIdString">ID string of the required OPT.</param>
        /// <returns>The end points.</returns>
        OptMode FetchOptMode(string optIdString);       

        void SetLastLogTime(string opt, DateTime? logTime);
        void SetReceiptHeader(string opt, string receiptHeader);
        void SetReceiptFooter(string opt, string receiptFooter);

        /// <summary>Store AutoAuth state for Hydra OPT Service in the database.</summary>
        /// <param name="hydraId">Hydra ID to set state for.</param>
        /// <param name="autoAuth">True if enabled, false if disabled.</param>
        void SetAutoAuth(string hydraId, bool autoAuth);

        /// <summary>Store Divert OPT in the database.</summary>
        /// <param name="ip">IP address to set.</param>
        /// <param name="fromOptPort">From OPT port number to set.</param>
        /// <param name="toOptPort">To OPT port number to set.</param>
        /// <param name="heartbeatPort">Heartbeat port number to set.</param>
        /// <param name="mediaChannelPort">Media Channel port number to set.</param>
        void SetDivertOpt(IPAddress ip, int fromOptPort, int toOptPort, int heartbeatPort, int mediaChannelPort);

        DivertOpt FetchDivertOpt();
        void SetOptDiverted();
        void SetOptNotDiverted();

        /// <summary>Store the payment timeout for a given mode in the database.</summary>
        /// <param name="mode">Mode to set timeout for.</param>
        /// <param name="timeout">The timeout is seconds.</param>
        /// <returns>Result</returns>
        Result SetPaymentTimeout(PaymentTimeoutType mode, int timeout);

        /// <summary>Store Media Channel state for Hydra OPT Service in the database.</summary>
        /// <param name="hydraId">Hydra ID to set state for.</param>
        /// <param name="mediaChannel">True if enabled, false if disabled.</param>
        void SetMediaChannel(string hydraId, bool mediaChannel);
    }

    /// <summary>
    /// Any and all capabilities of the IHydraDb, relevant to Carwash
    /// </summary>

    public interface IHydraDbCarwash
    {
        /// <summary>Fetch Washes from the database.</summary>
        /// <returns>The washes.</returns>
        IList<Wash> FetchWashes();

        /// <summary>Add wash to the database.</summary>
        /// <param name="wash">The wash to add.</param>
        void AddWash(Wash wash);

        /// <summary>Remove wash from the database.</summary>
        /// <param name="programId">Program Id of the wash to be removed.</param>
        void RemoveWashByProgramId(byte programId);
    }

    /// <summary>
    /// Any and all capabilities of the IHydraDb, relevant to Journal
    /// </summary>

    public interface IHydraDbJournal
    {
        /// <summary>Add a non-fuel event to the database.</summary>
        /// <param name="transactionTime">Time of event.</param>
        /// <param name="transactionNumber">Outputs transaction number.</param>
        void AddEvent(DateTime transactionTime, out int transactionNumber);

        /// <summary>Add a transaction to the cumulative totals for day end and shift end in the database.</summary>
        /// <param name="fuelAmount">Total cost of fuel.</param>
        /// <param name="dryAmount">Total cost of dry sales.</param>
        /// <param name="quantity">Quantity of fuel.</param>
        /// <param name="transactionNumber">Outputs transaction number.</param>
        /// <param name="category">Category of transaction.</param>
        /// <param name="subcategory">Subcategory of transaction.</param>
        /// <param name="gradeName">Grade name.</param>
        /// <param name="gradeCode">Grade code.</param>
        /// <param name="cardProductName">Card product name.</param>
        /// <param name="discount">Discount value.</param>
        /// <param name="message">Current message</param>
        void AddDayEnd
        (uint fuelAmount, uint dryAmount, uint quantity, int transactionNumber, short category, short subcategory, string gradeCode,
            string gradeName, string cardProductName, uint discount, IMessageTracking message = null);

        /// <summary>Retrieve and clear the cumulative totals for day end or shift end in the database.</summary>
        /// <param name="dayEnd">True for Day End, false for Shift End.</param>
        /// <param name="fuelAmount">Outputs total cost of fuel.</param>
        /// <param name="dryAmount">Outputs total cost of dry sales.</param>
        /// <param name="discount">Outputs total discount.</param>
        /// <param name="startTime">Outputs start time of shift or day.</param>
        /// <param name="endTime">Outputs end time of shift or day.</param>
        /// <param name="firstTransaction">Outputs number of first transaction of shift or day.</param>
        /// <param name="lastTransaction">Outputs number of last transaction of shift or day.</param>
        /// <param name="shiftNumber">Outputs shift number, or zero for day end.</param>
        void TakeDayEnd
        (bool dayEnd, out uint fuelAmount, out uint dryAmount, out uint discount, out DateTime startTime, out DateTime endTime,
            out int firstTransaction, out int lastTransaction, out int shiftNumber);

        /// <summary>Retrieve and clear the cumulative totals of item sales for day end or shift end in the database.</summary>
        /// <param name="dayEnd">True for Day End, false for Shift End.</param>
        /// <returns>List of item sales.</returns>
        IList<ItemSales> TakeItemSales(bool dayEnd);

        /// <summary>Retrieve and clear the cumulative totals of card sales for day end or shift end in the database.</summary>
        /// <param name="dayEnd">True for Day End, false for Shift End.</param>
        /// <returns>List of card sales.</returns>
        IList<CardSales> TakeCardSales(bool dayEnd);

        /// <summary>Retrieve and clear the cumulative totals of card volume sales for shift end in the database.</summary>
        /// <returns>List of card volume sales.</returns>
        IList<CardVolumeSales> TakeCardVolumeSales();

        /// <summary>
        /// Set the acquirer reference for a card type.
        /// </summary>
        /// <param name="cardName">Name of card type.</param>
        /// <param name="acquirerName">Acquirer name to set.</param>
        Result<int> SetAcquirerReference(string cardName, string acquirerName);

        /// <summary>
        /// Clear a the acquirer reference for a card type.
        /// </summary>
        /// <param name="cardName">Name of card type.</param>
        Result<int> ClearAcquirerReference(string cardName);

        /// <summary>
        /// Set the Fuel Card flag for a card type.
        /// </summary>
        /// <param name="cardName">Name of card type.</param>
        /// <param name="isFuelCard">Fuel Card flag.</param>
        Result<int> SetFuelCard(string cardName, bool isFuelCard);

        /// <summary>Set the Fuel Card flag for a card type.</summary>
        /// <param name="cardName">Name of card type.</param>
        /// <param name="externalCardName">External card name.</param>
        Result<int> SetExternalName(string cardName, string externalCardName);

        /// <summary>Clear the external name for a card type.</summary>
        /// <param name="cardName">Name of card type.</param>
        Result<int> ClearExternalName(string cardName);

        /// <summary>Fetch the start time for day end or shift end in the database.</summary>
        /// <param name="dayEnd">True for Day End, false for Shift End.</param>
        /// <returns>Start time.</returns>
        DateTime FetchShiftStart(bool dayEnd);

        /// <summary>Fetch a list of other events between the given dates.</summary>
        /// <param name="startTime">From date.</param>
        /// <param name="endTime">To date.</param>
        /// <returns>List of transactions.</returns>
        IEnumerable<OtherEvent> FetchOtherEvents(DateTime startTime, DateTime endTime);

        void AddShiftToList(int shiftNumber, DateTime startTime, DateTime endTime);
        IEnumerable<Shift> TakeShiftList();
        IEnumerable<MeterReading> FetchPreviousMeterReadings();

        void StorePreviousMeterReadings(IEnumerable<MeterReading> readings);

        void SetTillNumber(short number);
        void SetFuelCategory(short category);

        PrinterConfig GetPrinterConfig();
        void SetPrinterEnabled(bool isEnabled);
        void SetPrinterPortName(string portName);
        void SetPrinterBaudRate(int baudRate);
        void SetPrinterHandshake(Handshake handshake);
        void SetPrinterStopBits(StopBits stopBits);
        void SetPrinterDataBits(int dataBits);

    }

    /// <summary>
    /// Any and all capabilities of the IHydraDb, relevant to Transaction
    /// </summary>

    public interface IHydraDbTransaction: Core.HydraDb.Interfaces.IHydraDbTransaction
    {
        IList<long> ReceiptTrans();

        /// <summary>
        /// Set a pump as having an OPT payment.
        /// </summary>
        /// <param name="pump">The pump number.</param>
        /// <param name="message">The current message</param>
        void SetOptPayment(byte pump, IMessageTracking message = null);

        /// <summary>
        /// Clear OPT payment from pump.
        /// </summary>
        /// <param name="pump">The pump number.</param>
        /// <param name="message">The current message</param>
        void ClearOptPayment(byte pump, IMessageTracking message = null);

        /// <summary>
        /// Check whether a pump has an OPT payment.
        /// </summary>
        /// <param name="pump">The pump number.</param>
        /// <returns>True if it has an OPT payment, false otherwise.</returns>
        /// <param name="message">The current message</param>
        bool HasOptPayment(byte pump, IMessageTracking message = null);

        /// <summary>
        /// Get delivery notification for pump.
        /// </summary>
        /// <param name="pump">The pump number.</param>
        /// <param name="message">The current message</param>
        Result<PumpDelivered> GetDelivered(byte pump, IMessageTracking message = null);

        /// <summary>
        /// Check whether a pump has a delivery notification.
        /// </summary>
        /// <param name="pump">The pump number.</param>
        /// <param name="grade">The grade number.</param>
        /// <param name="volume">The delivery volume.</param>
        /// <param name="amount">The delivery amount.</param>
        /// <param name="name">The grade name.</param>
        /// <param name="price">The delivery price.</param>
        /// <param name="netAmount">The delivery net amount.</param>
        /// <param name="vatAmount">The delivery VAT amount.</param>
        /// <param name="vatRate">The delivery VAT rate.</param>
        /// <returns>True if it has a delivery notification, false otherwise.</returns>
        bool HasDelivered(byte pump, out byte grade, out uint volume, out uint amount, out string name, out ushort price, out uint netAmount, out uint vatAmount, out float vatRate);

        /// <summary>
        /// Fetch the set of receipts from the database.
        /// </summary>
        void FetchReceipts();
    }

    /// <summary>
    /// Any and all capabilities of the IHydraDb, relevant to Discounts
    /// </summary>

    public interface IHydraDbDiscounts
    {
        IList<DiscountCard> FetchDiscountCards();
        void AddDiscountCard(string iin, string name, string type, float value, byte grade);
        void RemoveDiscountCard(string iin);
        void AddDiscountWhitelist(string iin, string pan);
        void RemoveDiscountWhitelist(string iin, string pan);
    }

    /// <summary>
    /// Any and all capabilities of the IHydraDb, relevant to LocalAccounts
    /// </summary>

    public interface IHydraDbLocalAccounts
    {
        IList<LocalAccountCustomer> FetchLocalAccountCustomers();

        void AddLocalAccountCustomer(LocalAccountCustomer customer);

        void DeleteLocalAccountCustomer(LocalAccountCustomer customer);

        void AddLocalAccountCard(LocalAccountCustomer customer, LocalAccountCard card);

        void DeleteLocalAccountCard(LocalAccountCard card);

        void SetLocalAccountsEnabled(bool enabled);

        /// <summary>
        /// Updates local account customer with provided flags.
        /// </summary>
        /// <param name="model">An instance of <see cref="LocalAccountCustomerFlags"/> model containing the customer flags to be updated.</param>
        /// <param name="message">Optional. An instance of <see cref="IMessageTracking"/> to track the operation's message flow.</param>
        /// <returns>
        /// A <see cref="Result{T}"/> containing a <see cref="StatusCodeResult"/> indicating the success or failure of the db operation.
        /// </returns>
        Result<StatusCodeResult> UpdateLocalAccountCustomer(LocalAccountCustomerFlags model, IMessageTracking message = null);
    }
}