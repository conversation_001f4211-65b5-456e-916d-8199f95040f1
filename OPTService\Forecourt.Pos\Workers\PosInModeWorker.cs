﻿using CSharpFunctionalExtensions;
using Forecourt.Core.Enums;
using Forecourt.Core.Extensions;
using Forecourt.Core.Opt.Extensions;
using Forecourt.Core.Pump.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.Pump.Models;
using Forecourt.Pos.HydraDb.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Pos.Common;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Logger.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Forecourt.Pos.Workers
{
    /// <inheritdoc/>
    public abstract class PosInModeWorker : PosXxxWorker, IPosIntegratorInMode<IMessageTracking>, IPosIntegratorInMode<IMessageTracking, Result>, IPosIntegratorInMode<IMessageTracking, Result<StatusCodeResult>>
    {
        public const string PumpList = "PumpList:";
        public const string EveningMode = "EveningMode:";
        public const string NightMode = "NightMode:";

        /// <summary>
        /// Config Key for, the EveningMode Pump list
        /// </summary>
        public const string ConfigKeyEveningModePumpList = Core.Configuration.Constants.CategoryNameTimeModeChange + Core.Configuration.Constants.CategorySeparator + PumpList + "EveningMode";

        /// <summary>
        /// Default value for, the EveningMode Pump list
        /// </summary>
        public const string DefaultValueEveningModePumpList = "";

        /// <summary>
        /// Configurable value for, the EveningMode Pump list
        /// </summary>
        protected ConfigurableString ConfigValueEveningModePumpList { get; private set; }

        /// <summary>
        /// Config Key for, the NightMode Pump list
        /// </summary>
        public const string ConfigKeyNightModePumpList = Core.Configuration.Constants.CategoryNameTimeModeChange + Core.Configuration.Constants.CategorySeparator + PumpList + "NightMode";

        /// <summary>
        /// Default value for, the NightMode Pump list
        /// </summary>
        public const string DefaultValueNightModePumpList = "";

        /// <summary>
        /// Configurable value for, the NightMode Pump list
        /// </summary>
        protected ConfigurableString ConfigValueNightModePumpList { get; private set; }


        /// <summary>
        /// Config Key for, the EveningMode Start Time
        /// </summary>
        public const string ConfigKeyEveningModeStartTime = Core.Configuration.Constants.CategoryNameTimeModeChange + Core.Configuration.Constants.CategorySeparator + EveningMode + "StartTime";


        /// <summary>
        /// Default value for, the EveningMode Start Time
        /// </summary>
        public const string DefaultValueEveningModeStartTime = "18:00:00";

        /// <summary>
        /// Configurable value for, EveningMode Start Time
        /// </summary>
        protected ConfigurableTimeSpan ConfigValueEveningModeStartTime { get; private set; }


        /// <summary>
        /// Config Key for, the EveningMode End Time
        /// </summary>
        public const string ConfigKeyEveningModeEndTime = Core.Configuration.Constants.CategoryNameTimeModeChange + Core.Configuration.Constants.CategorySeparator + EveningMode + "EndTime";

        /// <summary>
        /// Default value for, the EveningMode End Time
        /// </summary>
        public const string DefaultValueEveningModeEndTime = "20:59:59";

        /// <summary>
        /// Configurable value for, the EveningMode End Time
        /// </summary>
        protected ConfigurableTimeSpan ConfigValueEveningModeEndTime { get; private set; }


        /// <summary>
        /// Config Key for, the NightMode Start Time
        /// </summary>
        public const string ConfigKeyNightModeStartTime = Core.Configuration.Constants.CategoryNameTimeModeChange + Core.Configuration.Constants.CategorySeparator + NightMode + "StartTime";

        /// <summary>
        /// Default value for, the NightMode Start Time
        /// </summary>
        public const string DefaultValueNightModeStartTime = "21:00:00";

        /// <summary>
        /// Configurable value for, the NightMode Start Time
        /// </summary>
        protected ConfigurableTimeSpan ConfigValueNightModeStartTime { get; private set; }

        /// <summary>
        /// Config Key for, the NightMode End Time
        /// </summary>
        public const string ConfigKeyNightModeEndTime = Core.Configuration.Constants.CategoryNameTimeModeChange + Core.Configuration.Constants.CategorySeparator + NightMode + "EndTime";

        /// <summary>
        /// Default value for, the NightMode End Time
        /// </summary>
        public const string DefaultValueNightModeEndTime = "05:59:59";

        /// <summary>
        /// Configurable value for, the NightMode End Time
        /// </summary>
        protected ConfigurableTimeSpan ConfigValueNightModeEndTime { get; private set; }

        
        /// <inheritdoc/>
        protected PosInModeWorker(IHtecLogManager logManager, IHydraDb hydraDb, IPumpCollection allPumps, IPosIntegratorOutTransient<IMessageTracking> posOutTransientWorker, 
            IConfigurationManager configurationManager = null) : base(logManager, nameof(PosInModeWorker), hydraDb, allPumps, configurationManager)
        {
            RegisterWorker(posOutTransientWorker ?? throw new ArgumentException(nameof(posOutTransientWorker)));

            ConfigValueEveningModePumpList = new ConfigurableString(this, ConfigKeyEveningModePumpList, DefaultValueEveningModePumpList);
            ConfigValueNightModePumpList = new ConfigurableString(this, ConfigKeyNightModePumpList, DefaultValueNightModePumpList);

            ConfigValueEveningModeStartTime = new ConfigurableTimeSpan(this, ConfigKeyEveningModeStartTime, DefaultValueEveningModeStartTime);
            ConfigValueEveningModeEndTime = new ConfigurableTimeSpan(this, ConfigKeyEveningModeEndTime, DefaultValueEveningModeEndTime);

            ConfigValueNightModeStartTime = new ConfigurableTimeSpan(this, ConfigKeyNightModeStartTime, DefaultValueNightModeStartTime);
            ConfigValueNightModeEndTime = new ConfigurableTimeSpan(this, ConfigKeyNightModeEndTime, DefaultValueNightModeEndTime);
        }

        /// <inheritdoc cref="IPosIntegratorInMode{IMessageTracking}"/>
        protected virtual void DoRequestDefaultMode(IPump pump, string loggingReference)
        {
            GetWorker<IPosIntegratorOutTransient<IMessageTracking>>()?.StatusResponse(pump.Number, new MessageTracking { IdAsString = loggingReference });
        }
        
        /// <inheritdoc cref="IPosIntegratorInMode{IMessageTracking}"/>
        public Result RequestDefaultMode(string loggingReference = null)
        {
            return DoAction(() =>
            {
                foreach (var thePump in AllPumps.AllPumps)
                {
                    DoRequestDefaultMode(thePump, loggingReference);
                }

                return Result.Success();
            }, loggingReference);
        }

        /// <inheritdoc/>
        Result<StatusCodeResult> Htec.Hydra.Core.Pos.Interfaces.Core.IPosIntegratorInMode<IMessageTracking, Result<StatusCodeResult>>.RequestModeChange(byte pump, ModeChangeType mode, IMessageTracking message)
        {
            var result = RequestModeChange(pump, mode, message);

            return Result.Success<StatusCodeResult>(result.IsSuccess ? StatusCodeResult.Success : StatusCodeResult.Specific(System.Net.HttpStatusCode.BadRequest, new System.Exception(result.Error)));
        }

        /// <inheritdoc/>
        public Result RequestModeChange(byte pump, ModeChangeType mode, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            return DoAction(() =>
            {
                var logRef = LoggingReference;
                var failedPumps = new List<byte>();

                if (!AllPumps.AllPumps.Any(x => pump == 0 || x.Number == pump))
                {
                    failedPumps.Add(pump);
                }

                foreach (var thePump in AllPumps.AllPumps.Where(x => pump == 0 || x.Number == pump))
                {
                    var p = thePump.Number;
                    var prevState = thePump.PumpState;

                    DoDeferredLogging(LogLevel.Info, "ModeChange", () => new[] { $"{mode}; {Core.Pump.Models.Pump.HeaderPump}: {p}; " });

                    if (!DoRequestModeChange(thePump, mode, message.IdAsString, true))
                    {
                        failedPumps.Add(p);
                    }

                    GetWorker<IPosIntegratorOutMode<IMessageTracking>>()?.ModeChangedResponse(p, mode, prevState.ToPumpOptType(), message);

                    NotificationWorker?.PushChange(EventType.PumpChanged, p.ToString());
                    if (thePump.Opt != null)
                    {
                        NotificationWorker?.PushChange(EventType.OPTChanged, thePump.Opt.IdString);
                    }
                }

                return Result.SuccessIf(!failedPumps.Any(), $"Failed to change {Pluralise("pump", failedPumps.Count)}: {string.Join(", ", failedPumps)}");
            }, message.FullId);
        }

        /// <inheritdoc cref="IPosIntegratorInMode{IMessageTracking}"/>
        protected virtual bool DoRequestModeChange(IPump pump, ModeChangeType mode, string reference, bool overridePumpIsClosed)
        {
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"{HeaderStatus}.{Forecourt.Core.Pump.Models.Pump.HeaderPump}", () => new[] {
                $"{pump.Number}; ModeChangeType: {mode}; OptIsClosed: {pump.OptIsClosed}; pump.Mode: {pump.PumpMode}; pump.PumpIsClosed: {pump.PumpIsClosed};  override: {overridePumpIsClosed};"
            });
            switch (mode)
            {
                case ModeChangeType.KioskOnly:
                    pump.SetKioskOnly(reference: reference);
                    return true;

                case ModeChangeType.Close:
                    var result = false;
                    if (pump.OptIsClosed && (pump.PumpMode == PumpModeType.Mixed || pump.PumpMode == PumpModeType.KioskUse))
                    {
                        pump.SetKioskOnly(store: true, reference: reference);
                        result = true;
                    }

                    if (overridePumpIsClosed || !pump.PumpIsClosed)
                    {
                        pump.ClosePump(reference: reference);
                        result = true;
                    }
                    return result;

                case ModeChangeType.Open:
                    if (pump.OptIsClosed)
                    {
                        pump.SetModeFromDefault(reference);
                    }

                    pump.OptIsClosed = false;
                    if (overridePumpIsClosed || pump.PumpIsClosed || pump.ClosePending)
                    {
                        pump.OpenPump(reference: reference);
                        return true;
                    }
                    break;

                case ModeChangeType.OutsideOnly:
                    pump.SetOutsideOnly(reference: reference);
                    return true;

                case ModeChangeType.Mixed:
                default:
                    pump.SetMixed(reference: reference);
                    return true;
            }

            return false;
        }

        /// <inheritdoc/>
        public virtual Result RequestTimeModeChange(TimeModeChangeType mode, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            return DoAction(() =>
            {
                var logRef = LoggingReference;
                var failedPumps = new List<byte>();

                foreach (var thePump in AllPumps.AllPumps.OrderBy(x => x.Number))
                {
                    var pump = thePump.Number;
                    var prevState = thePump.PumpState;

                    DoDeferredLogging(LogLevel.Info, "TimeModeChange", () => new[] { $"{mode}; {Core.Pump.Models.Pump.HeaderPump}: {pump}" });

                    if (!DoRequestTimeModeChange(thePump, mode, message))
                    {
                        failedPumps.Add(pump);
                    }

                    var isLastOptPump = thePump.IsLastPumpOnOpt;
                    var pumpMode = thePump.PumpMode;
                    var modeChangeType =
                        thePump.PumpIsClosed || thePump.OptIsClosed ? ModeChangeType.Close :
                        pumpMode == PumpModeType.KioskUse || pumpMode == PumpModeType.KioskOnly ? ModeChangeType.KioskOnly :
                        pumpMode == PumpModeType.OutsideOnly ? ModeChangeType.OutsideOnly :
                        pumpMode == PumpModeType.Mixed ? ModeChangeType.Mixed : ModeChangeType.Open;

                    GetWorker<IPosIntegratorOutMode<IMessageTracking>>()?.ModeChangedResponse(pump, modeChangeType, prevState.ToPumpOptType(), message, !isLastOptPump);
                    GetWorker<IPosIntegratorOutTransient<IMessageTracking>>().StatusResponse(pump, message);
                    NotificationWorker?.PushChange(EventType.PumpChanged, pump.ToString());

                    if (isLastOptPump)
                    {
                        var modeChangeType1 = thePump.Opt?.Mode.ToModeChangeType();
                        DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"{HeaderStatus}.#4.{nameof(Pump)}", () => new[] { $"{thePump.Number}; optMode: {thePump.Opt?.Mode}/{modeChangeType1}/{(ModeChangeType)modeChangeType1}; modeChangePendng: {thePump.Opt?.ModeChangePending}" });

                        NotificationWorker?.PushChange(EventType.OPTChanged);
                    }
                }

                return Result.SuccessIf(!failedPumps.Any(), $"Failed to change {Pluralise("pump", failedPumps.Count)}: {string.Join(", ", failedPumps)}");
            }, message.FullId);          
        }

        /// <inheritdoc cref="IPosIntegratorInMode{IMessageTracking}"/>
        protected virtual bool DoRequestTimeModeChange(IPump pump, TimeModeChangeType mode, IMessageTracking message)
        {
            bool ProcessMode(string pumpList, IPump pump, bool closePump, string reference)
            {
                var pumpMode = pump.PumpMode;
                var ss = pumpList.Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries).ToList();

                if (ss.Contains($"{pump.Number}"))
                {
                    if (pumpMode != PumpModeType.OutsideOnly)
                    {
                        pump.SetOutsideOnly(reference: reference);
                    }
                    pump.OpenPump(reference: reference);
                    DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"{HeaderStatus}.#1.{nameof(Pump)}", () => new[] { $"{pump.Number}; pumpMode: {pumpMode}/{pump.PumpMode}; optMode: {pump.Opt?.Mode}; modeChangePendng: {pump.Opt?.ModeChangePending}" });
                    return true;
                }

                if (!closePump)
                {
                    DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"{HeaderStatus}.#2.{nameof(Pump)}", () => new[] { $"{pump.Number}; pumpMode: {pump.PumpMode}; optMode: {pump.Opt?.Mode}; modeChangePendng: {pump.Opt?.ModeChangePending}" });
                    return true;
                }

                pump.ClosePump(reference: reference);
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"{HeaderStatus}.#3.{nameof(Pump)}", () => new[] { $"{pump.Number}; pumpMode: {pumpMode}/{pump.PumpMode}; closed: {pump.PumpIsClosed}/{pump.ClosePending}; optMode: {pump.Opt?.Mode}; modeChangePendng: {pump.Opt?.ModeChangePending}" });

                return pump.PumpIsClosed || pump.ClosePending;
            }

            message ??= new MessageTracking();
            var reference = message.FullId;

            pump.TimeMode = mode;

            switch (mode)
            {
                case TimeModeChangeType.NightMode:
                    return ProcessMode(ConfigValueNightModePumpList.GetValue(), pump, true, reference);

                case TimeModeChangeType.EveningMode:
                    return ProcessMode(ConfigValueEveningModePumpList.GetValue(), pump, false, reference);

                case TimeModeChangeType.DayMode:
                default:
                    var defaultMode = pump.DefaultOutsideOnly ? PumpModeType.OutsideOnly : pump.DefaultKioskUse ? PumpModeType.KioskOnly : PumpModeType.Mixed;
                    var result = pump.PumpMode == defaultMode || pump.SetModeFromDefault(message.FullId);
                    if (result)
                    {
                        pump.OpenPump(reference: reference);
                    }
                    return result;
            }
        }

        /// <inheritdoc/>
        public Result ApplyTimeMode(IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoAction<Result>(() => DoApplyTimeMode(message), message);
        }

        protected virtual Result DoApplyTimeMode(IMessageTracking message)
        {
            return RequestDefaultMode(message.FullId);
        }
    }
}
