﻿using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.IO.Abstractions;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <summary>
    /// Migration to add indexes to dbo.TransactionBookingState table for improved query performance
    /// </summary>
    [Migration(28200004, "ADO#795901 - Add indexes to dbo.TransactionBookingState"), Tags(Constants.MigrationTypeSchema)]
    public class ADO795901_Add_Indexes_TransactionBookingState : Migrationable
    {
        private const string TableName = "TransactionBookingState";
        private const string IndexNamePrefix = "ix_" + TableName + "_";

        /// <inheritdoc/>
        public ADO795901_Add_Indexes_TransactionBookingState(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
        }

        /// <inheritdoc/>
        public override void Up()
        {
            var indexName = IndexNamePrefix + "ids";
            if (!Schema.Table(TableName).Index(indexName).Exists())
            {
                Create.Index(indexName).OnTable(TableName)
                    .OnColumn("Id").Ascending()
                    .OnColumn("TransactionId").Ascending()
                    .OnColumn("TxnNumber").Ascending()
                    .OnColumn("ExternalTransactionId").Ascending()
                    .OnColumn("BookedDate").Ascending()
                    .WithOptions().NonClustered();
            }

            indexName = indexName = IndexNamePrefix + "timestamp";
            if (!Schema.Table(TableName).Index(indexName).Exists())
            {
                Create.Index(indexName).OnTable(TableName)
                    .OnColumn("TransactionDate").Ascending()
                    .WithOptions().NonClustered();
            }
        }

        /// <inheritdoc/>
        public override void Down()
        {
            var indexName = IndexNamePrefix + "timestamp";
            if (Schema.Table(TableName).Index(indexName).Exists())
            {
                Delete.Index(indexName).OnTable(TableName);
            }

            indexName = IndexNamePrefix + "ids";
            if (Schema.Table(TableName).Index(indexName).Exists())
            {
                Delete.Index(indexName).OnTable(TableName);
            }
        }
    }
}

