﻿using System.Collections.Generic;

namespace Forecourt.Bos.Configuration
{
    /// <summary>
    /// Class that contains all reference data, within a sub-system, that needs to be mapped to that of another sub-system
    /// </summary>
    /// <typeparam name="TGrade">Grade model type</typeparam>
    /// <typeparam name="TProduct">Product model type</typeparam>
    /// <typeparam name="TCurrency">Currency model</typeparam>
    /// <typeparam name="TVat">VAT rate model</typeparam>
    /// <typeparam name="TCardReference">Card referer model</typeparam>
    public class ReferenceData<TGrade, TProduct, TCurrency, TVat, TCardReference>
    {
        /// <summary>
        /// All grades within a sub-system
        /// </summary>
        public IEnumerable<TGrade> Grades { get; set; } = new List<TGrade>();

        /// <summary>
        /// All grades within a sub-system
        /// </summary>
        public IEnumerable<TProduct> Products { get; set; } = new List<TProduct>();

        /// <summary>
        /// All currencies within a sub-system
        /// </summary>
        public IEnumerable<TCurrency> Currencies { get; set; } = new List<TCurrency>();

        /// <summary>
        /// All VAT Rates within a sub-system
        /// </summary>
        public IEnumerable<TVat> VatRates { get; set; } = new List<TVat>();

        /// <summary>
        /// All VAT Rates within a sub-system
        /// </summary>
        public IEnumerable<TCardReference> CardReferences { get; set; } = new List<TCardReference>();
    }
}
