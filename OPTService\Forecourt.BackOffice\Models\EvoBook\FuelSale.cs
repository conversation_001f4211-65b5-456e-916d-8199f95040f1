﻿using Newtonsoft.Json;

namespace Forecourt.BackOffice.Models.EvoBook
{
    /// <summary>
    /// Represents fuel sale details including transaction identifiers, pricing, and volume information for EvoBook transactions
    /// </summary>
    public class FuelSale
    {
        /// <summary>
        /// Unique identifier for the fuelling transaction
        /// </summary>
        [JsonProperty("fuellingTransactionId")]
        public int FuellingTransactionId { get; set; }
        /// <summary>
        /// Timestamp when the fuel transaction occurred
        /// </summary>
        [JsonProperty("fuellingTimeStamp")]
        public string FuellingTimeStamp { get; set; }
        /// <summary>
        /// Unique identifier for the fuel grade (e.g., unleaded, diesel)
        /// </summary>
        [JsonProperty("fuelGradeId")]
        public int FuelGradeId { get; set; }
        /// <summary>
        /// Item identifier for the specific fuel product
        /// </summary>
        [JsonProperty("fuelItemId")]
        public string FuelItemId { get; set; }
        /// <summary>
        /// Identifier for the fuel dispenser point used
        /// </summary>
        [JsonProperty("fuelingPointId")]
        public int FuelingPointId { get; set; }
        /// <summary>
        /// Identifier for the fuel nozzle used for dispensing
        /// </summary>
        [JsonProperty("nozzleId")]
        public int NozzleId { get; set; }
        /// <summary>
        /// Price per unit of fuel sold
        /// </summary>
        [JsonProperty("fuelSalePrice")]
        public double FuelSalePrice { get; set; }
        /// <summary>
        /// Method used for fuel transaction entry (e.g., pump, manual)
        /// </summary>
        [JsonProperty("fuelEntryMethod")]
        public string FuelEntryMethod { get; set; }
        /// <summary>
        /// Total volume of fuel dispensed in the sale
        /// </summary>
        [JsonProperty("fuelSalesVolume")]
        public double FuelSalesVolume { get; set; }
        /// <summary>
        /// Total monetary value of the fuel sale
        /// </summary>
        [JsonProperty("fuelSalesValue")]
        public double FuelSalesValue { get; set; }
        
        /// <summary>
        /// Descriptive name of the fuel grade (e.g., "Unleaded 95", "Diesel")
        /// </summary>
        [JsonProperty("fuelGradeDescription")]
        public string FuelGradeDescription { get; set; }
    }
}
